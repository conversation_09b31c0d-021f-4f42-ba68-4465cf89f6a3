{"protocolVersion":"0.1.1","runnerVersion":null,"pid":44949,"type":"start","time":0}
{"suite":{"id":0,"platform":"vm","path":"/Users/<USER>/StudioProjects/el-junto-flutter-app/test/widget_test.dart"},"type":"suite","time":0}
{"test":{"id":1,"name":"loading /Users/<USER>/StudioProjects/el-junto-flutter-app/test/widget_test.dart","suiteID":0,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":9}
{"count":1,"time":11,"type":"allSuites"}

[{"event":"test.startedProcess","params":{"vmServiceUri":"http://127.0.0.1:61406/LTFrjCVqP8U=/","observatoryUri":"http://127.0.0.1:61406/LTFrjCVqP8U=/"}}]
{"testID":1,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":1891}
{"group":{"id":2,"suiteID":0,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":null,"column":null,"url":null},"type":"group","time":1892}
{"test":{"id":3,"name":"Counter increments smoke test","suiteID":0,"groupIDs":[2],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":13,"root_column":3,"root_url":"file:///Users/<USER>/StudioProjects/el-junto-flutter-app/test/widget_test.dart"},"type":"testStart","time":1892}
{"testID":3,"messageType":"print","message":"══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY ╞═══════════════════════════════════════════════════════════\nThe following StateError was thrown building _FocusInheritedScope:\nBad state: GetIt: Object/factory with type HttpApiService is not registered inside GetIt.\n(Did you accidentally do GetIt sl=GetIt.instance(); instead of GetIt sl=GetIt.instance;\nDid you forget to register it?)\n\nWhen the exception was thrown, this was the stack:\n#0      throwIfNot (package:get_it/get_it_impl.dart:14:19)\n#1      _GetItImplementation._findFactoryByNameAndType (package:get_it/get_it_impl.dart:578:5)\n#2      _GetItImplementation.get (package:get_it/get_it_impl.dart:602:29)\n#3      _GetItImplementation.call (package:get_it/get_it_impl.dart:675:12)\n#4      new LoginService (package:eljunto/services/login_service.dart:11:30)\n#5      new NotificationServices (package:eljunto/services/notification_service.dart:23:31)\n#6      _MyAppState.initState (package:eljunto/main.dart:159:29)\n#7      StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5860:55)\n#8      ComponentElement.mount (package:flutter/src/widgets/framework.dart:5709:5)\n#9      Element.inflateWidget (package:flutter/src/widgets/framework.dart:4555:16)\n#10     Element.updateChild (package:flutter/src/widgets/framework.dart:4014:20)\n#11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#12     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#13     ProxyElement.update (package:flutter/src/widgets/framework.dart:6059:5)\n#14     _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:108:11)\n#15     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#16     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#17     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5892:11)\n#18     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#19     StatefulElement.update (package:flutter/src/widgets/framework.dart:5917:5)\n#20     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#21     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#22     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#23     ProxyElement.update (package:flutter/src/widgets/framework.dart:6059:5)\n#24     _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:108:11)\n#25     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#26     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#27     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5892:11)\n#28     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#29     StatefulElement.update (package:flutter/src/widgets/framework.dart:5917:5)\n#30     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#31     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#32     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5892:11)\n#33     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#34     StatefulElement.update (package:flutter/src/widgets/framework.dart:5917:5)\n#35     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#36     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#37     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#38     ProxyElement.update (package:flutter/src/widgets/framework.dart:6059:5)\n#39     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#40     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#41     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5892:11)\n#42     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#43     StatefulElement.update (package:flutter/src/widgets/framework.dart:5917:5)\n#44     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#45     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#46     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#47     ProxyElement.update (package:flutter/src/widgets/framework.dart:6059:5)\n#48     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#49     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#50     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#51     ProxyElement.update (package:flutter/src/widgets/framework.dart:6059:5)\n#52     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#53     _RawViewElement._updateChild (package:flutter/src/widgets/view.dart:481:16)\n#54     _RawViewElement.update (package:flutter/src/widgets/view.dart:569:5)\n#55     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#56     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#57     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#58     StatelessElement.update (package:flutter/src/widgets/framework.dart:5805:5)\n#59     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#60     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5756:16)\n#61     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5892:11)\n#62     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#63     StatefulElement.update (package:flutter/src/widgets/framework.dart:5917:5)\n#64     Element.updateChild (package:flutter/src/widgets/framework.dart:3998:15)\n#65     RootElement._rebuild (package:flutter/src/widgets/binding.dart:1687:16)\n#66     RootElement.update (package:flutter/src/widgets/binding.dart:1665:5)\n#67     RootElement.performRebuild (package:flutter/src/widgets/binding.dart:1679:7)\n#68     Element.rebuild (package:flutter/src/widgets/framework.dart:5445:7)\n#69     BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2704:15)\n#70     BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2762:11)\n#71     BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3066:18)\n#72     AutomatedTestWidgetsFlutterBinding.drawFrame (package:flutter_test/src/binding.dart:1495:19)\n#73     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:482:5)\n#74     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1442:15)\n#75     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1355:9)\n#76     AutomatedTestWidgetsFlutterBinding.pump.<anonymous closure> (package:flutter_test/src/binding.dart:1340:9)\n#79     TestAsyncUtils.guard (package:flutter_test/src/test_async_utils.dart:74:41)\n#80     AutomatedTestWidgetsFlutterBinding.pump (package:flutter_test/src/binding.dart:1329:27)\n#81     WidgetTester.pumpWidget.<anonymous closure> (package:flutter_test/src/widget_tester.dart:599:22)\n#84     TestAsyncUtils.guard (package:flutter_test/src/test_async_utils.dart:74:41)\n#85     WidgetTester.pumpWidget (package:flutter_test/src/widget_tester.dart:596:27)\n#86     main.<anonymous closure> (file:///Users/<USER>/StudioProjects/el-junto-flutter-app/test/widget_test.dart:15:18)\n#87     testWidgets.<anonymous closure>.<anonymous closure> (package:flutter_test/src/widget_tester.dart:193:29)\n<asynchronous suspension>\n#88     TestWidgetsFlutterBinding._runTestBody (package:flutter_test/src/binding.dart:1064:5)\n<asynchronous suspension>\n<asynchronous suspension>\n(elided 5 frames from dart:async and package:stack_trace)\n\n════════════════════════════════════════════════════════════════════════════════════════════════════","type":"print","time":2038}
{"testID":3,"messageType":"print","message":"══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════\nThe following TestFailure was thrown running a test:\nExpected: exactly one matching candidate\n  Actual: _TextWidgetFinder:<Found 0 widgets with text \"0\": []>\n   Which: means none were found but one was expected\n\nWhen the exception was thrown, this was the stack:\n#4      main.<anonymous closure> (file:///Users/<USER>/StudioProjects/el-junto-flutter-app/test/widget_test.dart:18:5)\n<asynchronous suspension>\n#5      testWidgets.<anonymous closure>.<anonymous closure> (package:flutter_test/src/widget_tester.dart:193:15)\n<asynchronous suspension>\n#6      TestWidgetsFlutterBinding._runTestBody (package:flutter_test/src/binding.dart:1064:5)\n<asynchronous suspension>\n<asynchronous suspension>\n(elided one frame from package:stack_trace)\n\nThis was caught by the test expectation on the following line:\n  file:///Users/<USER>/StudioProjects/el-junto-flutter-app/test/widget_test.dart line 18\nThe test description was:\n  Counter increments smoke test\n════════════════════════════════════════════════════════════════════════════════════════════════════","type":"print","time":2039}
{"testID":3,"messageType":"print","message":"══╡ EXCEPTION CAUGHT BY FLUTTER TEST FRAMEWORK ╞════════════════════════════════════════════════════\nThe following message was thrown:\nMultiple exceptions (2) were detected during the running of the current test, and at least one was\nunexpected.\n════════════════════════════════════════════════════════════════════════════════════════════════════","type":"print","time":2039}
{"testID":3,"error":"Test failed. See exception logs above.\nThe test description was: Counter increments smoke test","stackTrace":"","isFailure":false,"type":"error","time":2040}
{"testID":3,"result":"error","skipped":false,"hidden":false,"type":"testDone","time":2042}
{"success":false,"type":"done","time":3017}
