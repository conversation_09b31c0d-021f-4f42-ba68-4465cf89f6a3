[{"currencyName": "United States Dollar", "isoCode": "USD", "symbol": ["$", "US$"], "notes": ""}, {"currencyName": "Euro", "isoCode": "EUR", "symbol": ["€", "EUR"], "notes": ""}, {"currencyName": "Japanese Yen", "isoCode": "JPY", "symbol": ["¥", "円"], "notes": ""}, {"currencyName": "British Pound Sterling", "isoCode": "GBP", "symbol": ["£", "GB£"], "notes": ""}, {"currencyName": "Australian Dollar", "isoCode": "AUD", "symbol": ["$", "A$", "AU$"], "notes": ""}, {"currencyName": "Canadian Dollar", "isoCode": "CAD", "symbol": ["$", "C$", "CA$"], "notes": ""}, {"currencyName": "Swiss Franc", "isoCode": "CHF", "symbol": ["CHF", "<PERSON>.", "SFr."], "notes": ""}, {"currencyName": "Chinese Yuan Renminbi", "isoCode": "CNY", "symbol": ["¥", "元", "CN¥", "RMB"], "notes": "Yuan is the unit, Renminbi is the currency name"}, {"currencyName": "Swedish Krona", "isoCode": "SEK", "symbol": ["kr", "SEK"], "notes": ""}, {"currencyName": "New Zealand Dollar", "isoCode": "NZD", "symbol": ["$", "NZ$", "NZ$"], "notes": ""}, {"currencyName": "South Korean Won", "isoCode": "KRW", "symbol": ["₩", "원"], "notes": ""}, {"currencyName": "Norwegian Krone", "isoCode": "NOK", "symbol": ["kr", "NOK"], "notes": ""}, {"currencyName": "Singapore Dollar", "isoCode": "SGD", "symbol": ["$", "S$", "S$"], "notes": ""}, {"currencyName": "Hong Kong Dollar", "isoCode": "HKD", "symbol": ["$", "HK$", "HK$"], "notes": ""}, {"currencyName": "Indian Rupee", "isoCode": "INR", "symbol": ["₹", "Rs", "INR"], "notes": "Symbol ₹ relatively recent (2010)"}, {"currencyName": "Mexican Peso", "isoCode": "MXN", "symbol": ["$", "Mex$", "Mex$"], "notes": ""}, {"currencyName": "Russian Ruble", "isoCode": "RUB", "symbol": ["₽", "руб."], "notes": "Symbol ₽ relatively recent (2013)"}, {"currencyName": "South African Rand", "isoCode": "ZAR", "symbol": ["R", "ZAR"], "notes": ""}, {"currencyName": "Brazilian Real", "isoCode": "BRL", "symbol": ["R$", "BRL"], "notes": ""}, {"currencyName": "Danish Krone", "isoCode": "DKK", "symbol": ["kr", "DKK"], "notes": ""}, {"currencyName": "Polish Zloty", "isoCode": "PLN", "symbol": ["zł", "PLN"], "notes": ""}, {"currencyName": "Taiwanese New Dollar", "isoCode": "TWD", "symbol": ["$", "NT$", "NT$"], "notes": ""}, {"currencyName": "Thai Baht", "isoCode": "THB", "symbol": ["฿", "บาท"], "notes": ""}, {"currencyName": "Hungarian Forint", "isoCode": "HUF", "symbol": ["Ft", "HUF"], "notes": ""}, {"currencyName": "Czech Koruna", "isoCode": "CZK", "symbol": ["Kč", "CZK"], "notes": ""}, {"currencyName": "Israeli New <PERSON>", "isoCode": "ILS", "symbol": ["₪", "NIS"], "notes": ""}, {"currencyName": "Philippine Peso", "isoCode": "PHP", "symbol": ["₱", "PhP"], "notes": ""}, {"currencyName": "Indonesian Rupiah", "isoCode": "IDR", "symbol": ["Rp", "IDR"], "notes": ""}, {"currencyName": "Malaysian Ringgit", "isoCode": "MYR", "symbol": ["RM", "MYR"], "notes": ""}, {"currencyName": "Colombian Peso", "isoCode": "COP", "symbol": ["$", "Col$", "Col$"], "notes": ""}, {"currencyName": "Saudi Riyal", "isoCode": "SAR", "symbol": ["﷼", "SR"], "notes": ""}, {"currencyName": "United Arab Emirates Dirham", "isoCode": "AED", "symbol": ["د.إ.", "AED"], "notes": ""}, {"currencyName": "Egyptian Pound", "isoCode": "EGP", "symbol": ["£", "ج.م.", "E£"], "notes": "£ often used but can be ambiguous"}, {"currencyName": "Vietnamese Dong", "isoCode": "VND", "symbol": ["₫", "VNĐ"], "notes": ""}, {"currencyName": "Turkish Lira", "isoCode": "TRY", "symbol": ["₺", "TL"], "notes": "Symbol ₺ relatively recent (2012)"}, {"currencyName": "Argentine Peso", "isoCode": "ARS", "symbol": ["$", "Arg$", "Arg$"], "notes": ""}, {"currencyName": "Chilean Peso", "isoCode": "CLP", "symbol": ["$", "CL$", "CL$"], "notes": ""}, {"currencyName": "Nigerian Naira", "isoCode": "NGN", "symbol": ["₦"], "notes": "Symbol ₦ relatively recent"}, {"currencyName": "Pakistani Rupee", "isoCode": "PKR", "symbol": ["₨", "PKR"], "notes": ""}, {"currencyName": "Romanian Leu", "isoCode": "RON", "symbol": ["lei", "RON"], "notes": ""}, {"currencyName": "Peruvian Sol", "isoCode": "PEN", "symbol": ["S/", "Soles"], "notes": ""}, {"currencyName": "Ukrainian Hryvnia", "isoCode": "UAH", "symbol": ["₴", "грн"], "notes": ""}, {"currencyName": "Moroccan <PERSON><PERSON><PERSON>", "isoCode": "MAD", "symbol": ["د.م.", "MAD"], "notes": ""}, {"currencyName": "Algerian Dinar", "isoCode": "DZD", "symbol": ["د.ج.", "DA"], "notes": ""}, {"currencyName": "Tunisian Dinar", "isoCode": "TND", "symbol": ["د.ت.", "DT"], "notes": ""}, {"currencyName": "<PERSON><PERSON>", "isoCode": "KZT", "symbol": ["₸", "KZT"], "notes": "Symbol ₸ relatively recent (2007)"}, {"currencyName": "Kenyan Shilling", "isoCode": "KES", "symbol": ["KSh", "KES"], "notes": ""}, {"currencyName": "<PERSON><PERSON>", "isoCode": "GHS", "symbol": ["GH₵", "GH¢"], "notes": ""}, {"currencyName": "Azerbaijani Manat", "isoCode": "AZN", "symbol": ["₼", "AZN"], "notes": "Symbol ₼ relatively recent"}, {"currencyName": "Croatian Kuna", "isoCode": "HRK", "symbol": ["kn", "HRK"], "notes": ""}, {"currencyName": "Serbian Dinar", "isoCode": "RSD", "symbol": ["дин.", "RSD"], "notes": ""}, {"currencyName": "Bulgarian Lev", "isoCode": "BGN", "symbol": ["лв.", "BGN"], "notes": ""}, {"currencyName": "Uruguayan Peso", "isoCode": "UYU", "symbol": ["$", "Ur$", "Ur$"], "notes": ""}, {"currencyName": "Costa Rican Colon", "isoCode": "CRC", "symbol": ["₡"], "notes": ""}, {"currencyName": "Panamanian Balboa", "isoCode": "PAB", "symbol": ["B/.", "PAB"], "notes": "Pegged 1:1 to USD, USD often used interchangeably"}, {"currencyName": "Albanian Lek", "isoCode": "ALL", "symbol": ["Lek", "ALL"], "notes": ""}, {"currencyName": "Armenian Dram", "isoCode": "AMD", "symbol": ["֏", "AMD"], "notes": "Symbol ֏ relatively recent"}, {"currencyName": "Angolan <PERSON>", "isoCode": "AOA", "symbol": ["Kz", "AOA"], "notes": ""}, {"currencyName": "Bangladeshi Taka", "isoCode": "BDT", "symbol": ["৳", "Tk", "BDT"], "notes": ""}, {"currencyName": "Belarusian Ruble", "isoCode": "BYN", "symbol": ["Br", "BYN"], "notes": "Replaced BYR in 2016"}, {"currencyName": "Bolivian Boliviano", "isoCode": "BOB", "symbol": ["Bs.", "BOB"], "notes": ""}, {"currencyName": "Bosnia and Herzegovina Mark", "isoCode": "BAM", "symbol": ["KM", "BAM"], "notes": ""}, {"currencyName": "Botswana Pula", "isoCode": "BWP", "symbol": ["P", "BWP"], "notes": ""}, {"currencyName": "Brunei Dollar", "isoCode": "BND", "symbol": ["$", "B$", "B$"], "notes": "Pegged 1:1 to SGD, SGD often used interchangeably"}, {"currencyName": "Cambodian Riel", "isoCode": "KHR", "symbol": ["៛", "KHR"], "notes": ""}, {"currencyName": "Cape Verdean Escudo", "isoCode": "CVE", "symbol": ["Esc"], "notes": "$ symbol is common but can be ambiguous"}, {"currencyName": "Cayman Islands Dollar", "isoCode": "KYD", "symbol": ["$", "CI$", "CI$"], "notes": ""}, {"currencyName": "Congolese Franc", "isoCode": "CDF", "symbol": ["FC", "CDF"], "notes": ""}, {"currencyName": "Cuban Peso", "isoCode": "CUP", "symbol": ["$", "CU$", "CU$"], "notes": ""}, {"currencyName": "Dominican Peso", "isoCode": "DOP", "symbol": ["$", "RD$", "RD$"], "notes": ""}, {"currencyName": "Eritrean Nakfa", "isoCode": "ERN", "symbol": ["Nfk", "ERN"], "notes": ""}, {"currencyName": "Ethiopian Birr", "isoCode": "ETB", "symbol": ["Br", "ETB"], "notes": ""}, {"currencyName": "Falkland Islands Pound", "isoCode": "FKP", "symbol": ["£", "FK£"], "notes": "Pegged 1:1 to GBP"}, {"currencyName": "Fijian Dollar", "isoCode": "FJD", "symbol": ["$", "FJ$", "FJ$"], "notes": ""}, {"currencyName": "Gambian Dalasi", "isoCode": "GMD", "symbol": ["D", "GMD"], "notes": ""}, {"currencyName": "Georgian Lari", "isoCode": "GEL", "symbol": ["₾", "GEL"], "notes": "Symbol ₾ relatively recent"}, {"currencyName": "Guatemalan <PERSON>", "isoCode": "GTQ", "symbol": ["Q", "GTQ"], "notes": ""}, {"currencyName": "Guernsey Pound", "isoCode": "GGP", "symbol": ["£", "G£"], "notes": "Pegged 1:1 to GBP"}, {"currencyName": "Guinean Franc", "isoCode": "GNF", "symbol": ["FG", "GNF"], "notes": ""}, {"currencyName": "Guyanese Dollar", "isoCode": "GYD", "symbol": ["$", "G$", "G$"], "notes": ""}, {"currencyName": "Haitian Gourde", "isoCode": "HTG", "symbol": ["G", "HTG"], "notes": ""}, {"currencyName": "<PERSON><PERSON><PERSON>", "isoCode": "HNL", "symbol": ["L", "HNL"], "notes": ""}, {"currencyName": "Icelandic Króna", "isoCode": "ISK", "symbol": ["kr", "ISK"], "notes": ""}, {"currencyName": "Iranian Rial", "isoCode": "IRR", "symbol": ["﷼", "IR"], "notes": ""}, {"currencyName": "Iraqi <PERSON>", "isoCode": "IQD", "symbol": ["<PERSON><PERSON>د", "IQD"], "notes": ""}, {"currencyName": "Isle of Man Pound", "isoCode": "IMP", "symbol": ["£", "IM£"], "notes": "Pegged 1:1 to GBP"}, {"currencyName": "Jamaican Dollar", "isoCode": "JMD", "symbol": ["$", "J$", "J$"], "notes": ""}, {"currencyName": "<PERSON><PERSON>", "isoCode": "JOD", "symbol": ["د.ا", "JD"], "notes": ""}, {"currencyName": "Kyrgyzstani Som", "isoCode": "KGS", "symbol": ["сом", "KGS"], "notes": ""}, {"currencyName": "<PERSON>", "isoCode": "LAK", "symbol": ["₭", "₭N", "LAK"], "notes": "Symbol ₭ relatively recent"}, {"currencyName": "Latvian Lats", "isoCode": "LVL", "symbol": ["Ls", "LVL"], "notes": "Replaced by EUR"}, {"currencyName": "Lebanese Pound", "isoCode": "LBP", "symbol": ["£", "ل.ل.", "L£"], "notes": "£ often used but can be ambiguous"}, {"currencyName": "Lesotho Loti", "isoCode": "LSL", "symbol": ["L", "M"], "notes": "Plural is Maloti, LSL often used"}, {"currencyName": "Liberian Dollar", "isoCode": "LRD", "symbol": ["$", "L$", "L$"], "notes": ""}, {"currencyName": "Libyan Dinar", "isoCode": "LYD", "symbol": ["ل.د", "LYD"], "notes": ""}, {"currencyName": "Lithuanian Litas", "isoCode": "LTL", "symbol": ["Lt", "LTL"], "notes": "Replaced by EUR"}, {"currencyName": "Macedonian Denar", "isoCode": "MKD", "symbol": ["ден", "MKD"], "notes": ""}, {"currencyName": "Malagasy Ariary", "isoCode": "MGA", "symbol": ["Ar", "MGA"], "notes": ""}, {"currencyName": "<PERSON><PERSON>", "isoCode": "MWK", "symbol": ["MK", "MWK"], "notes": ""}, {"currencyName": "Maldives Rufiyaa", "isoCode": "MVR", "symbol": ["Rf.", "MVR"], "notes": ""}, {"currencyName": "Mauritanian Ouguiya", "isoCode": "MRU", "symbol": ["UM", "MRU"], "notes": "Replaced MRO, decimal places abolished"}, {"currencyName": "Mauritian Rupee", "isoCode": "MUR", "symbol": ["₨", "MUR"], "notes": ""}, {"currencyName": "Moldovan Leu", "isoCode": "MDL", "symbol": ["L", "MDL"], "notes": ""}, {"currencyName": "Mongolian Tugrik", "isoCode": "MNT", "symbol": ["₮", "MNT"], "notes": "Symbol ₮ relatively recent"}, {"currencyName": "Mozambican Metical", "isoCode": "MZN", "symbol": ["MT", "MZN"], "notes": "Replaced MZM"}, {"currencyName": "Myanmar Kyat", "isoCode": "MMK", "symbol": ["Ks", "MMK"], "notes": ""}, {"currencyName": "Namibian Dollar", "isoCode": "NAD", "symbol": ["$", "N$", "N$"], "notes": ""}, {"currencyName": "Nepalese Rupee", "isoCode": "NPR", "symbol": ["₨", "NRs", "NPR"], "notes": ""}, {"currencyName": "Netherlands Antillean Guilder", "isoCode": "ANG", "symbol": ["ƒ", "NAƒ", "ANG"], "notes": ""}, {"currencyName": "Nicaraguan Cordoba", "isoCode": "NIO", "symbol": ["C$", "NIO"], "notes": ""}, {"currencyName": "North Korean Won", "isoCode": "KPW", "symbol": ["₩", "圓", "원"], "notes": "Symbol ₩ same as South Korean Won"}, {"currencyName": "Omani R<PERSON>", "isoCode": "OMR", "symbol": ["﷼", "RO"], "notes": ""}, {"currencyName": "Papua New Guinean Kina", "isoCode": "PGK", "symbol": ["K", "PGK"], "notes": ""}, {"currencyName": "Paraguayan <PERSON>", "isoCode": "PYG", "symbol": ["₲", "Gs.", "PYG"], "notes": "Symbol ₲ relatively recent"}, {"currencyName": "Qatari Riyal", "isoCode": "QAR", "symbol": ["﷼", "QR"], "notes": ""}, {"currencyName": "Rwandan <PERSON>", "isoCode": "RWF", "symbol": ["FRw", "RWF"], "notes": ""}, {"currencyName": "Samoan <PERSON>", "isoCode": "WST", "symbol": ["T", "WS$"], "notes": "Tala also means Dollar in Samoan, WS$ helps differentiate"}, {"currencyName": "Sao Tome and Prin<PERSON><PERSON>", "isoCode": "STD", "symbol": ["Db", "STD"], "notes": "To be replaced by STN"}, {"currencyName": "Seychellois Rupee", "isoCode": "SCR", "symbol": ["₨", "SR", "SRe"], "notes": ""}, {"currencyName": "Sierra Leonean Leone", "isoCode": "SLL", "symbol": ["Le", "SLL"], "notes": ""}, {"currencyName": "Slovak Koruna", "isoCode": "SKK", "symbol": ["Sk", "SKK"], "notes": "Replaced by EUR"}, {"currencyName": "Slovenian Tolar", "isoCode": "SIT", "symbol": ["To<PERSON>ar"], "notes": "Replaced by EUR"}, {"currencyName": "Solomon Islands Dollar", "isoCode": "SBD", "symbol": ["$", "SI$", "SI$"], "notes": ""}, {"currencyName": "Somali Shilling", "isoCode": "SOS", "symbol": ["Sh.So.", "SOS"], "notes": ""}, {"currencyName": "South Sudanese Pound", "isoCode": "SSP", "symbol": ["£", "SSP"], "notes": ""}, {"currencyName": "Sri Lankan Rupee", "isoCode": "LKR", "symbol": ["₨", "SLRs", "LKR"], "notes": ""}, {"currencyName": "Sudanese Pound", "isoCode": "SDG", "symbol": ["ج.س.", "SDG"], "notes": ""}, {"currencyName": "Surinamese Dollar", "isoCode": "SRD", "symbol": ["$", "SR$", "SR$"], "notes": "Replaced SRG"}, {"currencyName": "Swazi Lilangeni", "isoCode": "SZL", "symbol": ["L", "E"], "notes": "Plural Emalangeni, SZL often used"}, {"currencyName": "Syrian Pound", "isoCode": "SYP", "symbol": ["£", "LS", "S£"], "notes": "£ often used but can be ambiguous"}, {"currencyName": "<PERSON>i Somoni", "isoCode": "TJS", "symbol": ["смн", "TJS"], "notes": ""}, {"currencyName": "Tanzanian <PERSON>", "isoCode": "TZS", "symbol": ["TSh", "TZS"], "notes": ""}, {"currencyName": "Tongan Paʻanga", "isoCode": "TOP", "symbol": ["T$", "TOP"], "notes": ""}, {"currencyName": "Trinidad and Tobago Dollar", "isoCode": "TTD", "symbol": ["$", "TT$", "TT$"], "notes": ""}, {"currencyName": "Ugandan <PERSON>", "isoCode": "UGX", "symbol": ["USh", "UGX"], "notes": ""}, {"currencyName": "Uzbekistani Som", "isoCode": "UZS", "symbol": ["лв", "UZS"], "notes": "Cyrillic script symbol, Latin script usually uses code UZS"}, {"currencyName": "Vanuatu Vatu", "isoCode": "VUV", "symbol": ["Vt", "VUV"], "notes": ""}, {"currencyName": "Venezuelan Bolí<PERSON>", "isoCode": "VES", "symbol": ["Bs.S.", "VES"], "notes": "Replaced VEF"}, {"currencyName": "Yemeni R<PERSON>", "isoCode": "YER", "symbol": ["﷼", "YR"], "notes": ""}, {"currencyName": "Zambian <PERSON>", "isoCode": "ZMW", "symbol": ["ZK", "ZMW"], "notes": "Replaced ZMK"}, {"currencyName": "Zimbabwean Dollar", "isoCode": "ZWL", "symbol": ["$", "Z$"], "notes": "Multiple versions of Zimbabwean Dollar, current is ZWL"}]