# Project identification, either hardcode it or use Environment variables
sonar.projectKey=El-Junto
sonar.projectName=El Junto
sonar.token=sqp_318b4aaccdcc59be39184ad9e4ebb8e7b39cef4f
# The host URL
sonar.host.url=http://localhost:9000
# Source code location.
# Path is relative to the sonar-project.properties file. Defaults to .
# Use commas to specify more than one folder.
sonar.dart.analyzer.mode=FLUTTER
sonar.sources=lib
sonar.tests=test
# Use existing options to perform dartanalyzer analysis
sonar.dart.analyzer.options.override=true
# Encoding of the source code. Default is default system encoding.
sonar.sourceEncoding=UTF-8
# exclude generated files
sonar.exclusions=test/**/*_test.mocks.dart,lib/**/*.g.dart
sonar.dart.analysis.useExistingOptions=true