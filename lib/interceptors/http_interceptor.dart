import 'package:dio/dio.dart';
import 'package:eljunto/views/routing.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HttpInterceptor extends Interceptor {
  final List<String> excludedEndpoints = [
    '/auth/signin',
    '/auth/email-verification',
    '/auth/pass-code-verify',
    '/auth/sign-up-set-password',
    '/auth/signout',
    '/error-logs/add',
  ];
  final List<String> profileSetUpEndPoints = [
    'auth/check-available-handle',
    'auth/signup-name-handle'
  ];

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    bool shouldExclude =
        excludedEndpoints.any((endpoint) => options.path.contains(endpoint));
    bool isProfileSetUpEndPoint = profileSetUpEndPoints
        .any((endpoint) => options.path.contains(endpoint));

    if (!shouldExclude || isProfileSetUpEndPoint) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('jwttoken');

      if (token != null) {
        options.headers['Authorization'] = 'Bearer $token';
      }
    }

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final code = err.response?.statusCode;
    if (code == 501 || code == 502 || code == 503 || code == 504) {
      AppRouter.rootNavigatorKeys.currentContext?.go('/server-down');
    }
    handler.next(err);
  }
}
