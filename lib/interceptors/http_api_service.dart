import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/constants/constants.dart';
import 'package:http_parser/http_parser.dart';

class HttpApiService {
  final dio.Dio _dio; // Make dio instance private

  // Constructor accepting a Dio instance
  HttpApiService({required dio.Dio dioInstance}) : _dio = dioInstance;

  Future<dio.Response> postRequest(
      String url, Map<String, dynamic> body) async {
    return await _dio.post(
      url,
      options: dio.Options(
        headers: {
          'Content-Type': 'application/json',
          'Portal-Type': PortalType.app,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
      data: jsonEncode(body),
    );
  }

  Future<dio.Response> getRequest(String url) async {
    return await _dio.get(
      url,
      options: dio.Options(
        headers: {
          'Content-Type': 'application/json',
          'Portal-Type': PortalType.app,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
    );
  }

  Future<dio.Response> putRequest(String url, Map<String, dynamic> body) async {
    return await _dio.put(
      url,
      options: dio.Options(
        headers: {
          'Content-Type': 'application/json',
          'Portal-Type': PortalType.app,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
      data: jsonEncode(body),
    );
  }

  Future<dio.Response> deleteRequest(String url) async {
    return await _dio.delete(
      url,
      options: dio.Options(
        headers: {
          'Content-Type': 'application/json',
          'Portal-Type': PortalType.app,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
    );
  }

  Future<dio.Response> uploadFileRequest(
      String url, File file, int userId) async {
    String fileName = file.path.split('/').last;
    dio.FormData formData = dio.FormData.fromMap({
      'file': await dio.MultipartFile.fromFile(
        file.path,
        filename: fileName,
        contentType:
            MediaType.parse('image/${file.path.split('.').last.toLowerCase()}'),
      ),
      'userId': userId.toString(),
    });

    return await _dio.post(
      url,
      data: formData,
      options: dio.Options(
        headers: {
          'Portal-Type': PortalType.app,
        },
        validateStatus: (status) =>
            status != null &&
            (status >= 200 && status < 300 || status >= 400 && status < 500),
      ),
    );
  }
}
