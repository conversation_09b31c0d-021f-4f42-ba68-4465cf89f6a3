import 'dart:async';
import 'dart:developer'; // For logging

import 'package:dio/dio.dart';
import 'package:eljunto/controller/connectivity_controller.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

class RetryOnConnectionChangeInterceptor extends Interceptor {
  final Dio dio;
  final ConnectivityProvider connectivityProvider;

  RetryOnConnectionChangeInterceptor({
    required this.dio,
    required this.connectivityProvider,
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (_shouldRetry(err)) {
      log('[RetryInterceptor] Network error detected (${err.type}), waiting for connection...');
      try {
        // Wait for the *next* time the status is connected. Add a timeout.
        await connectivityProvider.statusStream
            .where((status) => status == InternetStatus.connected)
            .first
            .timeout(const Duration(minutes: 1));

        log('[RetryInterceptor] Connection established. Retrying request: ${err.requestOptions.path}');

        // --- Retry the request ---
        final requestOptions = err.requestOptions;
        final response = await dio.request(
          requestOptions.path,
          cancelToken: requestOptions.cancelToken, // Preserve cancel token
          data: requestOptions.data,
          queryParameters:
              requestOptions.queryParameters, // Preserve query params
          onReceiveProgress:
              requestOptions.onReceiveProgress, // Preserve progress listeners
          onSendProgress:
              requestOptions.onSendProgress, // Preserve progress listeners
          options: Options(
            method: requestOptions.method,
            headers: requestOptions.headers,
            responseType: requestOptions.responseType,
            contentType: requestOptions.contentType,
            validateStatus: requestOptions.validateStatus,
            receiveTimeout: requestOptions.receiveTimeout,
            sendTimeout: requestOptions.sendTimeout,
            extra: requestOptions.extra,
            responseDecoder: requestOptions.responseDecoder,
            listFormat: requestOptions.listFormat,
            followRedirects: requestOptions.followRedirects,
            maxRedirects: requestOptions.maxRedirects,
            requestEncoder: requestOptions.requestEncoder,
          ),
        );
        log('[RetryInterceptor] Retry successful for: ${err.requestOptions.path}');
        handler.resolve(response);
      } on TimeoutException catch (_) {
        log('[RetryInterceptor] Timeout waiting for connection. Failing request: ${err.requestOptions.path}');
        handler.next(err);
      } catch (retryError) {
        log('[RetryInterceptor] Error during retry for ${err.requestOptions.path}: $retryError');
        handler.next(err);
      }
    } else {
      handler.next(err);
    }
  }

  bool _shouldRetry(DioException err) {
    return err.type == DioExceptionType.connectionError ||
        err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.receiveTimeout;
  }
}
