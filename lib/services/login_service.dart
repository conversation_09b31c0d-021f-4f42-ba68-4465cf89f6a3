import 'dart:developer';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/interceptors/http_api_service.dart';
import 'package:eljunto/services/setup_locator.dart';

import '../constants/config.dart';

class LoginService {
  final String baseURL = Config.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();

  Future<dio.Response> login(
      String email, String password, String fcmToken, String deviceId) async {
    final Map<String, dynamic> payload = {
      "userEmailId": email,
      "userCred": password,
      "fcmToken": fcmToken,
      "deviceId": deviceId,
    };
    log("Login Payload : $payload");

    return await _apiService.postRequest('$baseURL/auth/signin', payload);
  }

  Future<dio.Response> emailVerification(String email) async {
    final Map<String, dynamic> payload = {"userEmailId": email};

    return await _apiService.postRequest(
        '$baseURL/auth/email-verification', payload);
  }

  Future<dio.Response> forgotPasswordMailVerification(String email) async {
    return await _apiService.getRequest(
      '$baseURL/auth/forgot-password?input=$email',
    );
  }

  Future<dio.Response> otpVerification(
      String email, String token, String otp, bool flag) async {
    final Map<String, dynamic> payload = {
      "userEmailId": email,
      "token": token,
      "passcode": otp,
      "isResetPassword": flag,
    };

    return await _apiService.postRequest(
        '$baseURL/auth/pass-code-verify', payload);
  }

  Future<dio.Response> setPassword(String email, String token, String otp,
      String password, String fcmToken, String deviceId) async {
    final Map<String, dynamic> payload = {
      "userEmailId": email,
      "token": token,
      "passcode": otp,
      "userCred": password,
      "fcmToken": fcmToken,
      "deviceId": deviceId,
    };
    log("SetPassword Payload : $payload");
    return await _apiService.postRequest(
        '$baseURL/auth/sign-up-set-password', payload);
  }

  Future<dio.Response> reSetPassword(
      String email, String token, String otp, String password) async {
    final Map<String, dynamic> payload = {
      "userEmailId": email,
      "tokenCred": otp,
      "token": token,
      "userCred": password,
      "confirmUserCred": password
    };

    return await _apiService.postRequest(
        '$baseURL/auth/reset-password', payload);
  }

  Future<dio.Response> setNameAndHandle(
    String email,
    String token,
    String otp,
    String userName,
    String userHandle,
    String userLocation,
    String userBio,
  ) async {
    final Map<String, dynamic> payload = {
      "userEmailId": email,
      "token": token,
      "passcode": otp,
      "userName": userName,
      "userHandle": userHandle,
      "userLocation": userLocation,
      "userBio": userBio,
    };
    log("Payload : $payload");

    return await _apiService.postRequest(
        '$baseURL/auth/signup-name-handle', payload);
  }

  Future<dio.Response> checkAavailabilityOfHandle(
    String email,
    String userHandle,
  ) async {
    final Map<String, dynamic> payload = {
      "userEmailId": email,
      "userHandle": userHandle,
    };

    return await _apiService.postRequest(
        '$baseURL/auth/check-available-handle', payload);
  }

  Future<dio.Response> changePassword(String userEmail, String currentPassword,
      String newPassword, String confirmPassword) async {
    Map<String, dynamic> payload = {
      "userEmailId": userEmail,
      "currentCred": currentPassword,
      "userCred": newPassword,
      "confirmUserCred": confirmPassword
    };
    return await _apiService.postRequest(
        "$baseURL/auth/change-password", payload);
  }

  Future<dio.Response> logOutFunction(String userMail) async {
    return _apiService.getRequest('$baseURL/auth/signout/$userMail/');
  }

  Future<dio.Response> updateFcmToken(String deviceId, String fcmToken) async {
    final Map<String, dynamic> payload = {
      "deviceId": deviceId,
      "fcmToken": fcmToken,
    };
    log("Update fcm token payload: $payload");
    return _apiService.putRequest(
        '$baseURL/notifications/update-fcm-token', payload);
  }
}
