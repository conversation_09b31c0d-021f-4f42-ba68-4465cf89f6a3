import 'package:dio/dio.dart' as dio;
import 'package:eljunto/interceptors/http_api_service.dart';
import 'package:eljunto/services/setup_locator.dart';

import '../constants/config.dart';

class MessageService {
  final String baseURL = Config.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();
  Future<dio.Response> getBookClubsForChat(String? bookClubType, int? userId,
      int? bookClubId, int offSet, int limit) async {
    return await _apiService.getRequest(
        '$baseURL/book_club/list?bookClubType=$bookClubType&userId=$userId&bookClubId=$bookClubId&limit=$limit&offset=$offSet');
  }
}
