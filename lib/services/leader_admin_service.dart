import 'package:dio/dio.dart' as dio;
import 'package:eljunto/constants/config.dart';
import 'package:eljunto/interceptors/http_api_service.dart';
import 'package:eljunto/services/setup_locator.dart';

class LeaderAdminservice {
  final String baseURL = Config.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();

  Future<dio.Response> updateCharter(
    // String bookClubName,
    int? bookClubId,
    String? clubCharter,
    String? memberReqPrompt,
    String? clubStatus,
  ) async {
    final Map<String, dynamic> payload = {
      'bookClubId': bookClubId,
      // 'bookClubName': bookClubName,
      'clubCharter': club<PERSON>harter,
      'memberReqPrompt': memberReqPrompt,
      'clubStatus': clubStatus,
    };
    return await _apiService.postRequest('$baseURL/book_club/update', payload);
  }
}
