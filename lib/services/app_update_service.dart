import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:eljunto/constants/config.dart';
import 'package:eljunto/models/app_version_model.dart';

class AppUpdateService {
  final _dio = Dio();
  final baseUrl = Config.flavorBaseUrl;
  Future<AppVersionModel?> checkForUpdates() async {
    try {
      final response = await _dio.get('$baseUrl/sysParam/version-info');
      if (response.statusCode == 200) {
        return AppVersionModel.fromJson(response.data);
      }
      log('Failed to check for updates: ${response.statusCode}');
      return null;
    } catch (e) {
      print('Error checking for updates: $e');
      return null;
    }
  }
}
