import 'dart:developer';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/interceptors/http_api_service.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/services/setup_locator.dart';

import '../constants/config.dart';

class BookCaseService {
  final String baseURL = Config.flavorBaseUrl;
  final _apiService = locator<HttpApiService>();

  Future<dio.Response> getCurrentReadBookCase(
      int userId, int limit, int offSet) async {
    return await _apiService.getRequest(
      '$baseURL/bookCase/currently-reading?userId=$userId&limit=$limit&offset=$offSet',
    );
  }

  Future<dio.Response> getToBeReadBook(
      int userId, int limit, int offSet) async {
    return await _apiService.getRequest(
      '$baseURL/bookCase/toBeRead-list?toBeRead=true&userId=$userId&limit=$limit&offset=$offSet',
    );
  }

  Future<dio.Response> allBooksRead(
      int userId, int limit, int offSet, bool completeReading) async {
    // } else { topshelf=$topShelf REMOVE 2 JAN 2025
    return await _apiService.getRequest(
      '$baseURL/bookCase/topshelf-list?&userId=$userId&limit=$limit&offset=$offSet&completeReading=$completeReading&toBeRead=false',
    );
    // }
  }

  Future<dio.Response> getTopShelfCompletedBookCase(
      int userId, int limit, int offSet) async {
    return await _apiService.getRequest(
      '$baseURL/bookCase/list?userId=$userId&limit=$limit&offset=$offSet',
    );
  }

  Future<dio.Response> addBookInBookCase(BookCaseModel newBook) async {
    final Map<String, dynamic> payload = {
      // "userId": newBook.userId,  /// 8 JAN 2025
      "bookId": newBook.bookId,
      "isCurrentlyReading": newBook.is_currently_reading,
      "topShelf": newBook.topShelf,
      "ratings": newBook.ratings,
      "review": newBook.review,
      "readingCompleteDate": newBook.reading_complete_date_String,
      "toBeRead": newBook.toBeRead,
      "bookName": newBook.bookName,
      "bookAuthor": newBook.bookAuthor,
    };
    return _apiService.postRequest('$baseURL/bookCase/add', payload);
  }

  Future<dio.Response> updateBookCase(BookCaseModel obj) async {
    print("Rating : ${obj.ratings}");
    final Map<String, dynamic> payload = {
      "bookCaseId": obj.bookCaseId,
      "userId": obj.userId,
      "bookId": obj.bookId,
      "bookName": obj.bookName,
      "bookAuthor": obj.bookAuthor,
      "isCurrentlyReading": obj.is_currently_reading,
      "ratings": obj.ratings ?? 0.5,
      "topShelf": obj.topShelf,
      "review": obj.review,
      "readingCompleteDate":
          obj.reading_complete_date_String ?? obj.readingCompleteDate,
      "reRead": obj.reRead,
      "toBeRead": obj.toBeRead,
    };
    log("Update book Payload : $payload");
    return _apiService.postRequest('$baseURL/bookCase/update', payload);
  }

  Future<dio.Response> deleteBook(int? bookId) async {
    return _apiService.deleteRequest('$baseURL/bookCase/delete/$bookId');
  }
}
