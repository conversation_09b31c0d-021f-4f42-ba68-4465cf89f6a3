import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/app_version_controller.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/reusableWidgets/appbar.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/services/notification_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/config.dart';
import '../../controller/inapp_purchase_controller.dart';
import '../../controller/login_controller.dart';
import '../../controller/subscription_controller.dart';
import '../../models/club_charter_model.dart';
import '../../models/home_model/home_screen1_model/fellow_reader_model.dart';
import '../../models/home_model/home_screen1_model/new_club_opening_model.dart';
import '../../reusableWidgets/marquee_text.dart';
import '../../reusableWidgets/matches_bottom_sheet/club_opening_matches_sheet.dart';
import '../../reusableWidgets/matches_bottom_sheet/matches_bottom_sheet.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  bool isLoading = false;
  int? loggedinUserId;
  List<MatchedMeeting>? matchedMeetings;

  List<NewClubOpeningList> clubList = [];
  List<FellowReader> fellowReadList = [];

  List<FellowReader> currentlyReadList = [
    FellowReader(
      bookName: "The Business Blockchain",
      bookAuthor: "William Mougayar",
    ),
    FellowReader(
      bookName: "City of God",
      bookAuthor: "Saint Augustine",
    ),
    FellowReader(
      bookName: "The Business Blockchain",
      bookAuthor: "William Mougayar",
    ),
  ];
  String image = Config.imageBaseUrl;

  int offset = 0;
  int clubOpeningLimit = 10;
  int fellowReadersLimit = 10;
  bool clubOpenigLoading = true;
  bool fellowReadersLoading = false;
  int clubOpeningcount = 0;
  int fellowReadersCount = 0;
  final ScrollController _scrollController = ScrollController();
  final ScrollController _fellowScrollController = ScrollController();
  bool isLoadingMore = false;
  NotificationServices? notificationService;
  String fcmToken = '';
  final List<GlobalKey> _newOpeningsItemKeys = [];
  final _newOpeningsMaxHeightNotifier = ValueNotifier(0.0);
  final List<GlobalKey> _fellowReadersItemKeys = [];
  final _fellowReadersMaxHeightNotifier = ValueNotifier(0.0);
  final List<GlobalKey> currentlyReadingBookAuthorItemKeys = [];
  final _currentlyReadingBookAuthorMaxHeightNotifier = ValueNotifier(0.0);
  late AnimationController _fellowAnimationController;
  late AnimationController _newClubsAnimationController;
  late AppVersionProvider _appVersionProvider;

  late MessageController messageController;

  @override
  void initState() {
    messageController = Provider.of<MessageController>(context, listen: false);

    Provider.of<InAppPurchaseController>(context, listen: false)
        .fetchProducts(context)
        .then((_) {});
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => checkDeepLink());
    isActiveSubscription();
    _fellowAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _newClubsAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    /// WHEN APP IS BACKGROUND STATE
    FirebaseMessaging.onMessageOpenedApp.listen(
      (RemoteMessage message) {
        // handleNotificationClick(message.data['deepLink']);
      },
    );

    notificationService?.getDeviceToken().then((value) async {
      fcmToken = value;
      SharedPreferences pref = await SharedPreferences.getInstance();
      pref.setString('fcmToken', fcmToken);
      setState(() {});
    });
    notificationService?.isTokenRefresh(context);

    /// WHEN APP IS TERMINATED STATE
    FirebaseMessaging.instance.getInitialMessage();
    userController = Provider.of<UserController>(context, listen: false);
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    checkFirstTimeUser();
    _initializeUserIdAndFetchData();
    super.initState();
    _scrollController.addListener(_onScroll);
    _fellowScrollController.addListener(_fellowOnScroll);

    _appVersionProvider =
        Provider.of<AppVersionProvider>(context, listen: false);
    Future.delayed(Duration(milliseconds: 100), () async {
      await checkForUpdates();
    });
  }

  Future<void> checkForUpdates() async {
    await _appVersionProvider.checkForUpdate(context);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print("State : $state");
    if (state == AppLifecycleState.resumed) {
      checkDeepLink();
    }
  }

  void checkDeepLink() {
    print("In checkDeepLink function");
    if (messageController.isInvalidLink && mounted) {
      CommonHelper.inValidDeepLink(context, InvalidDeepLink.message,
          onDialogClosed: () {
        messageController.isInvalidLink = false;
      });
    } else {
      log("Else part : ${messageController.isInvalidLink}");
    }
  }

  Future<void> checkFirstTimeUser() async {
    await Provider.of<LoginController>(context, listen: false).updateFcmToken();

    SharedPreferences pref = await SharedPreferences.getInstance();
    bool isFirstTimeUser = pref.getBool('isFirstTimeUser') ?? false;
    if (isFirstTimeUser) {
      showInfoDialog().then((_) {
        pref.setBool('isFirstTimeUser', false);
      });
    }
  }

  Future<void> isActiveSubscription() async {
    await Provider.of<SubscriptionController>(context, listen: false)
        .getSubscriptionDetails(context);
    if (mounted) {
      await Provider.of<SubscriptionController>(context, listen: false)
          .isActiveSubscription()
          .then((_) {
        if (mounted) {
          String isActiveSubscription =
              Provider.of<SubscriptionController>(context, listen: false)
                      .verifySubscriptionModel
                      ?.data
                      ?.usubStatus ??
                  '';
          log("Is Active Subscription : $isActiveSubscription");
        }
      });
    }
    if (mounted) {
      await Provider.of<SubscriptionController>(context, listen: false)
          .updateApiTriggerValue(false);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _fellowScrollController.dispose();
    _fellowReadersMaxHeightNotifier.dispose();
    _fellowAnimationController.dispose();
    _newClubsAnimationController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _initializeNewOpeningKeys() {
    _newOpeningsItemKeys.clear();
    for (int i = 0; i < clubList.length; i++) {
      _newOpeningsItemKeys.add(GlobalKey());
    }
  }

  void _initializeFellowReaderKeys() {
    _fellowReadersItemKeys.clear();
    for (int i = 0; i < fellowReadList.length; i++) {
      _fellowReadersItemKeys.add(GlobalKey());
    }
  }

  void _initializeCurrentReadingBookAuthorKeys() {
    currentlyReadingBookAuthorItemKeys.clear();
    for (int i = 0; i < currentlyReadList.length; i++) {
      currentlyReadingBookAuthorItemKeys.add(GlobalKey());
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent &&
        !isLoadingMore &&
        clubList.length < (bookClubController?.clubOpeningCount ?? 0)) {
      CommonHelper.networkClose(getNewClubOpenings(true), context);
    }
  }

  void _calculateNewOpeningMaxHeight() {
    if (!mounted) return;

    double maxHeight = 0.0;
    for (var key in _newOpeningsItemKeys) {
      try {
        final context = key.currentContext;
        if (context != null && mounted) {
          final renderBox = context.findRenderObject() as RenderBox?;
          if (renderBox != null) {
            final height = renderBox.size.height;
            maxHeight = maxHeight < height ? height : maxHeight;
          }
        }
      } catch (e) {
        continue;
      }
    }

    if (maxHeight != _newOpeningsMaxHeightNotifier.value && mounted) {
      _newOpeningsMaxHeightNotifier.value = maxHeight;
    }
  }

  void _fellowOnScroll() {
    if (_fellowScrollController.position.pixels >=
            _fellowScrollController.position.maxScrollExtent &&
        !fellowReadersLoading &&
        fellowReadList.length < (userController?.fellowReadersCount ?? 0)) {
      CommonHelper.networkClose(getFellowReaders(true), context);
      // getFellowReaders(true).then((_) => _initializeFellowReaderKeys());
    }
  }

  void _calculateFellowReaderMaxHeight() {
    if (!mounted) return;

    double maxHeight = 0.0;
    for (var key in _fellowReadersItemKeys) {
      try {
        final context = key.currentContext;
        if (context != null && mounted) {
          final renderBox = context.findRenderObject() as RenderBox?;
          if (renderBox != null && renderBox.hasSize) {
            final height = renderBox.size.height;
            maxHeight = maxHeight < height ? height : maxHeight;
          }
        }
      } catch (e) {
        continue;
      }
    }

    if (maxHeight != _fellowReadersMaxHeightNotifier.value && mounted) {
      setState(() {
        _fellowReadersMaxHeightNotifier.value = maxHeight;
      });
    }
  }

  /// CALCULATE MAX HEIGHT FOR CURRENTLY READING BOOK OR AUTHORS SCROLL VIEW
  void _calculateReadingBookAuthorMaxHeight() {
    if (!mounted) return;

    double maxHeight = 0.0;
    for (var key in currentlyReadingBookAuthorItemKeys) {
      try {
        final context = key.currentContext;
        if (context != null && mounted) {
          final renderBox = context.findRenderObject() as RenderBox?;
          if (renderBox != null && renderBox.hasSize) {
            final height = renderBox.size.height;
            maxHeight = maxHeight < height ? height : maxHeight;
          }
        }
      } catch (_) {
        continue;
      }

      if (maxHeight != _currentlyReadingBookAuthorMaxHeightNotifier.value &&
          mounted) {
        _currentlyReadingBookAuthorMaxHeightNotifier.value = maxHeight;
      }
    }
  }

  Future _initializeUserIdAndFetchData() async {
    await _initializeUserId();
    setState(() {
      isLoading = true;
    });
    await Future.wait(
      [
        getFellowReaders(false),
        getNewClubOpenings(false),
      ],
    ).then((_) {
      setState(() {
        isLoading = false;
      });
    });
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();
  }

  BookClubController? bookClubController;
  UserController? userController;

  Future<void> getNewClubOpenings(bool isMore) async {
    if (clubList.length <= (bookClubController?.clubOpeningCount ?? 0) ||
        !isMore) {
      isLoadingMore = true;

      if (isMore) {
        clubOpeningLimit += 10;
      }
    }

    await Provider.of<BookClubController>(context, listen: false)
        .getNewClubs(loggedinUserId ?? 0, offset, clubOpeningLimit, context)
        .then((responseMap) {
      clubList.clear();
      clubList = bookClubController?.clubList ?? [];

      _initializeNewOpeningKeys();
      _initializeCurrentReadingBookAuthorKeys();

      if (mounted) {
        _calculateNewOpeningMaxHeight();
        _calculateReadingBookAuthorMaxHeight();
      }
    }).whenComplete(() {
      isLoadingMore = false;
    });
  }

  Future<void> getFellowReaders(bool isMore) async {
    if (fellowReadList.length <= (userController?.fellowReadersCount ?? 0) ||
        !isMore) {
      fellowReadersLoading = true;

      if (isMore) {
        fellowReadersLimit += 10;
      }
    }
    Map<String, dynamic> payload = {
      "userId": loggedinUserId,
      "offset": 0,
      "limit": fellowReadersLimit,
    };

    await Provider.of<UserController>(context, listen: false)
        .getFellowReadersByUserId(payload, context)
        .then((responseMap) {
      fellowReadList.clear();
      fellowReadList = userController?.fellowReadList ?? [];
      _initializeFellowReaderKeys();

      if (mounted) {
        _calculateFellowReaderMaxHeight();
      }
    }).whenComplete(() {
      fellowReadersLoading = false;
    });
  }

  Future<void> clubOpeningRefresh() async {
    _newClubsAnimationController.reset();
    _newClubsAnimationController.forward();
    if (clubList.isNotEmpty) {
      _scrollController.position
          .animateTo(
        0.0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOut,
      )
          .then(
        (value) async {
          clubOpeningLimit = 0;
          await getNewClubOpenings(true);
        },
      );
    } else {
      clubOpeningLimit = 0;
      await getNewClubOpenings(true);
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const AppBarWidget(),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          enabled: isLoading,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20.0),
                  child: Row(
                    children: [
                      Text(
                        "New Club Openings",
                        overflow: TextOverflow.ellipsis,
                        style: lbRegular.copyWith(
                          fontSize: 20,
                        ),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      RotationTransition(
                        turns: Tween(begin: 0.0, end: 1.0)
                            .animate(_newClubsAnimationController),
                        child: NetworkAwareTap(
                          onTap: clubOpeningRefresh,
                          // () async {
                          //   _newClubsAnimationController.reset();
                          //   _newClubsAnimationController.forward();
                          //   if (clubList.isNotEmpty) {
                          //     _scrollController.position
                          //         .animateTo(
                          //       0.0,
                          //       duration: const Duration(milliseconds: 500),
                          //       curve: Curves.easeOut,
                          //     )
                          //         .then(
                          //       (value) async {
                          //         clubOpeningLimit = 0;
                          //         await getNewClubOpenings(true);
                          //       },
                          //     );
                          //   } else {
                          //     clubOpeningLimit = 0;
                          //     await getNewClubOpenings(true);
                          //   }

                          //   setState(() {});
                          // },
                          child: const Icon(
                            Icons.autorenew_rounded,
                            // Icons.loop_rounded,
                            size: 25,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                clubList.isNotEmpty
                    ? SingleChildScrollView(
                        padding: const EdgeInsets.only(left: 10, right: 20),
                        scrollDirection: Axis.horizontal,
                        controller: _scrollController,
                        child: Consumer<BookClubController>(
                          builder: (context, homeController, child) {
                            return Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                isLoadingMore
                                    ? clubList.length + 1
                                    : clubList.length,
                                (index) {
                                  if (index == clubList.length &&
                                      isLoadingMore) {
                                    return Padding(
                                      padding:
                                          const EdgeInsets.only(left: 10.0),
                                      child: Container(
                                        padding:
                                            const EdgeInsets.only(left: 10.0),
                                        height:
                                            _newOpeningsMaxHeightNotifier.value,
                                        child: const Center(
                                          child: CircularProgressIndicator(
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                      ),
                                    );
                                  }

                                  matchedMeetings =
                                      clubList[index].matchedMeetings ?? [];

                                  String meetingTime = '';
                                  String? authorName = '';
                                  String? clubCount = '';
                                  String? selectedBook = '';
                                  String? selectedBookAuthor = '';
                                  String? partCovered = '';

                                  partCovered = clubList[index]
                                          .latestPartsOfBookCovered ??
                                      '';

                                  if (clubList[index].clubType ==
                                      ClubType.impromptu) {
                                    authorName =
                                        clubList[index].latestBookAuthor;
                                    clubCount = clubList[index].clubCount ?? '';
                                  } else if (clubList[index].clubType ==
                                      ClubType.standing) {
                                    selectedBook =
                                        clubList[index].latestBookName;
                                    selectedBookAuthor =
                                        clubList[index].latestBookAuthor;
                                  }

                                  int currentDate =
                                      DateTime.now().millisecondsSinceEpoch;

                                  String meetingHeading = ((clubList[index]
                                                  .latestMeetingDate) !=
                                              null &&
                                          (clubList[index].latestMeetingDate ??
                                                  0) <=
                                              currentDate)
                                      ? 'Previous Meeting:'
                                      : ((clubList[index].latestBookName !=
                                              null))
                                          ? 'Next Meeting:'
                                          : 'First meeting not yet scheduled';
                                  String formattedDate = (meetingHeading ==
                                          'Next Meeting:')
                                      ? CommonHelper.getDayMonthYearDateFormat(
                                          clubList[index].latestMeetingDate)
                                      : (meetingHeading == 'Previous Meeting:')
                                          ? CommonHelper
                                              .getDayMonthYearDateFormat(
                                                  clubList[index]
                                                      .latestMeetingDate)
                                          : '';

                                  meetingTime =
                                      CommonHelper.getMeetingScheduleTime(
                                          clubList[index]
                                                  .latestMeetingStartTime ??
                                              0,
                                          clubList[index]
                                                  .latestMeetingEndTime ??
                                              0);

                                  return NetworkAwareTap(
                                    onTap: () {
                                      context.pushNamed(
                                        'club-details',
                                        extra: {
                                          'bookClubId': clubList[index].clubId,
                                          'bookClubName':
                                              clubList[index].clubName,
                                          'impromptuCount': clubCount,
                                        },
                                      );
                                    },
                                    child: ValueListenableBuilder(
                                      valueListenable:
                                          _newOpeningsMaxHeightNotifier,
                                      builder: (context, maxHeight, _) {
                                        return LayoutBuilder(
                                          builder: (context, constraints) {
                                            // Schedule the calculation for the next frame to ensure
                                            // all widgets are properly laid out
                                            if (mounted &&
                                                maxHeight <
                                                    constraints.maxHeight) {
                                              WidgetsBinding.instance
                                                  .addPostFrameCallback(
                                                (_) {
                                                  if (mounted) {
                                                    _calculateNewOpeningMaxHeight();
                                                  }
                                                },
                                              );
                                            }

                                            return ClipRect(
                                              clipBehavior:
                                                  Clip.antiAliasWithSaveLayer,
                                              child: SizedBox(
                                                key:
                                                    _newOpeningsItemKeys[index],
                                                height: maxHeight > 0
                                                    ? maxHeight
                                                    : null,
                                                child: Skeleton.replace(
                                                  replacement:
                                                      newClubOpeningSkeleton(
                                                    false,
                                                    homeController,
                                                    index,
                                                    authorName ?? '',
                                                    clubCount ?? '',
                                                    selectedBook ?? '',
                                                    meetingTime,
                                                    formattedDate,
                                                    meetingHeading,
                                                    selectedBookAuthor ?? '',
                                                    partCovered ?? '',
                                                  ),
                                                  child: newClubOpeningSkeleton(
                                                    true,
                                                    homeController,
                                                    index,
                                                    authorName ?? '',
                                                    clubCount ?? '',
                                                    selectedBook ?? '',
                                                    meetingTime,
                                                    formattedDate,
                                                    meetingHeading,
                                                    selectedBookAuthor ?? '',
                                                    partCovered ?? '',
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
                      )
                    : Skeleton.replace(
                        replacement: messageSkeleton("No new club openings"),
                        child: const NoDataWidget(
                          message: "No new club openings",
                        ),
                      ),
                const SizedBox(
                  height: 25,
                ),
                const Skeleton.replace(
                  replacement: Divider(
                    thickness: 2,
                    color: AppConstants.skeletonBackgroundColor,
                  ),
                  child: Divider(
                    thickness: 2,
                    color: AppConstants.primaryColor,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20.0, top: 15),
                  child: Row(
                    children: [
                      Text(
                        "Fellow Readers",
                        overflow: TextOverflow.ellipsis,
                        style: lbRegular.copyWith(
                          fontSize: 20,
                        ),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      RotationTransition(
                        turns: Tween(begin: 0.0, end: 1.0)
                            .animate(_fellowAnimationController),
                        child: NetworkAwareTap(
                          onTap: () async {
                            _fellowAnimationController.reset();
                            _fellowAnimationController.forward();
                            if (fellowReadList.isNotEmpty) {
                              _fellowScrollController.position
                                  .animateTo(
                                0.0,
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.easeOut,
                              )
                                  .then(
                                (value) async {
                                  fellowReadersLimit = 0;
                                  await getFellowReaders(true);
                                },
                              );
                            } else {
                              fellowReadersLimit = 0;
                              await getFellowReaders(true);
                            }

                            setState(() {});
                          },
                          child: const Icon(
                            Icons.autorenew_rounded,
                            size: 25,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                fellowReadList.isNotEmpty
                    ? SingleChildScrollView(
                        padding: const EdgeInsets.only(left: 10.0, right: 20),
                        scrollDirection: Axis.horizontal,
                        controller: _fellowScrollController,
                        child: Consumer<UserController>(
                          builder: (context, userController, child) {
                            return Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                fellowReadersLoading
                                    ? fellowReadList.length + 1
                                    : fellowReadList.length,
                                (index) {
                                  if (index == fellowReadList.length &&
                                      fellowReadersLoading) {
                                    return Padding(
                                      padding:
                                          const EdgeInsets.only(left: 10.0),
                                      child: Container(
                                        padding:
                                            const EdgeInsets.only(left: 10.0),
                                        height: _fellowReadersMaxHeightNotifier
                                            .value,
                                        child: const Center(
                                          child: CircularProgressIndicator(
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                      ),
                                    );
                                  }
                                  final authorList = fellowReadList
                                      .map((e) => e.userName ?? '')
                                      .toList();
                                  TextStyle textStyle = lbBold.copyWith(
                                      fontSize: 18 *
                                          MediaQuery.of(context)
                                              .textScaler
                                              .scale(1));

                                  double maxWidth =
                                      calculateMaxWidth(authorList, textStyle) *
                                          1.3;
                                  final userImage = image +
                                      (fellowReadList[index]
                                              .userProfilePicture ??
                                          '');

                                  final stringsList = <String>[];
                                  stringsList.add(
                                      fellowReadList[index].userName ?? '');
                                  stringsList.add(
                                      fellowReadList[index].bookAuthor ?? '');
                                  stringsList.add('Pune, MH');
                                  stringsList.add('Continue Reading');
                                  stringsList.add(
                                      fellowReadList[index].bookName ?? '');
                                  // final maxHeight = calculateTotalHeight(
                                  //   strings: stringsList,
                                  //   textStyle: textStyle,
                                  //   spacingBetweenLines: 10,
                                  // );

                                  return NetworkAwareTap(
                                    onTap: () {
                                      context.pushNamed(
                                        'club-member-profile',
                                        extra: {
                                          'userId':
                                              fellowReadList[index].userId,
                                          'userName':
                                              fellowReadList[index].userName
                                        },
                                      );
                                    },
                                    child: ValueListenableBuilder(
                                      valueListenable:
                                          _fellowReadersMaxHeightNotifier,
                                      builder: (context, maxHeight, _) {
                                        return LayoutBuilder(
                                          builder: (context, constraints) {
                                            WidgetsBinding.instance
                                                .addPostFrameCallback(
                                              (_) {
                                                if (mounted) {
                                                  _calculateFellowReaderMaxHeight();
                                                }
                                              },
                                            );
                                            // }

                                            return ClipRect(
                                              clipBehavior:
                                                  Clip.antiAliasWithSaveLayer,
                                              child: SizedBox(
                                                key: _fellowReadersItemKeys[
                                                    index],
                                                height: maxHeight > 0
                                                    ? maxHeight
                                                    : null,
                                                child: Skeleton.replace(
                                                  replacement:
                                                      fellowReaderSkeleton(
                                                    false,
                                                    index,
                                                    userImage,
                                                    maxWidth,
                                                  ),
                                                  child: fellowReaderSkeleton(
                                                    true,
                                                    index,
                                                    userImage,
                                                    maxWidth,
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
                      )
                    : Skeleton.replace(
                        replacement: messageSkeleton("No fellow readers"),
                        child: const NoDataWidget(
                          message: "No fellow readers",
                        ),
                      ),
                const SizedBox(height: 25),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget messageSkeleton(String message) {
    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 200,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Text(
                message,
                textAlign: TextAlign.start,
              ),
              Text(
                message,
                textAlign: TextAlign.start,
              ),
              Text(
                message,
                textAlign: TextAlign.start,
              ),
              Text(
                message,
                textAlign: TextAlign.start,
              ),
            ],
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: EdgeInsets.all(14),
                height: 50,
                width: 50,
                decoration: BoxDecoration(
                  color: AppConstants.skeletonforgroundColor,
                  borderRadius: BorderRadius.circular(50),
                ),
              ),
              Container(
                margin: EdgeInsets.all(14),
                height: 45,
                width: 80,
                decoration: BoxDecoration(
                  color: AppConstants.skeletonforgroundColor,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget newClubOpeningSkeleton(
    bool isBorder,
    BookClubController homeController,
    int index,
    String authorName,
    String clubCount,
    String selectedBook,
    String meetingTime,
    String formattedDate,
    String meetingHeading,
    String selectedBookAuthor,
    String partCovered,
  ) {
    return Container(
      padding: const EdgeInsets.all(14),
      margin: const EdgeInsets.only(left: 10),
      width: 320,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? Colors.transparent
            : AppConstants.skeletonBackgroundColor,
        border: isBorder
            ? Border.all(
                color: AppConstants.primaryColor,
                width: 1.5,
              )
            : Border.all(
                color: Colors.transparent,
              ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Wrap the text in a Flexible to prevent overflow
              Flexible(
                fit: FlexFit.tight,
                flex: 22,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MarqueeList(
                      children: [
                        Text(
                          clubList[index].clubName ?? '',
                          overflow: TextOverflow.ellipsis,
                          style: lbBold.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    if (clubList[index].clubType == ClubType.impromptu &&
                        authorName.isNotEmpty &&
                        clubCount.isNotEmpty) ...[
                      Text(
                        authorName,
                        overflow: TextOverflow.ellipsis,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        clubCount,
                        overflow: TextOverflow.ellipsis,
                        style: lbItalic.copyWith(
                          fontSize: 12,
                        ),
                      ),
                    ] else ...[
                      const SizedBox.shrink(),
                    ],
                  ],
                ),
              ),
              const SizedBox(width: 5),
              const Spacer(),
              // Ensure the image does not exceed space
              SizedBox(
                height: 50,
                width: 50,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: Image.asset(
                    //clubList[index].clubLogo ?? '',
                    AppConstants.clubOpeningLogoImagePath,
                    fit: BoxFit.cover,
                    filterQuality: FilterQuality.high,
                  ),
                ),
              ),
            ],
          ),
          (clubList[index].clubType == ClubType.standing)
              ? const SizedBox(height: 0)
              : const SizedBox(height: 25),
          // const SizedBox(height: 31),
          (clubList[index].clubType == ClubType.impromptu)
              ? const SizedBox(
                  height: 4,
                )
              : const SizedBox.shrink(),
          Text(
            meetingHeading,
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(fontSize: 12),
          ),
          // if (meetingHeading != "No new meeting scheduled") ...[
          if (clubList[index].clubType == ClubType.standing) ...[
            const SizedBox(
              height: 5,
            ),
            MarqueeList(
              children: [
                Text(
                  selectedBook,
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            Text(
              selectedBookAuthor,
              overflow: TextOverflow.ellipsis,
              style: lbRegular.copyWith(fontSize: 12),
            ),
          ] else ...[
            const SizedBox.shrink(),
          ],
          const SizedBox(height: 5),
          Text(
            partCovered,
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(fontSize: 12),
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    formattedDate,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(fontSize: 12),
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  Text(
                    (meetingHeading == 'First meeting not yet scheduled')
                        ? ''
                        : meetingTime,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(fontSize: 12),
                  ),
                ],
              ),
              const Spacer(),
              NetworkAwareTap(
                onTap: () {
                  /// SHOW BOTTOM SHEET
                  if ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0) {
                    matchedMeetings = clubList[index].matchedMeetings;
                    clubMatchBottomSheet(index).then((_) {
                      getNewClubOpenings(false);
                      setState(() {});
                    });
                  } else {
                    log("No Match Found");
                  }
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0)
                        ? AppConstants.textGreenColor
                        : AppConstants.isActiveRequestColor,
                  ),
                  child: Text(
                    ((clubList[index].isCurrentlyReadingMatch ?? 0) == 0)
                        ? "No Match"
                        : ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0 &&
                                (clubList[index].isCurrentlyReadingMatch ?? 0) <
                                    2)
                            ? "${clubList[index].isCurrentlyReadingMatch ?? 0} Match"
                            : "${clubList[index].isCurrentlyReadingMatch ?? 0} Matches",
                    overflow: TextOverflow.ellipsis,
                    style: lbBold.copyWith(
                      fontSize: 12,
                      color:
                          ((clubList[index].isCurrentlyReadingMatch ?? 0) > 0)
                              ? AppConstants.primaryColor
                              : Colors.black38,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  double calculateMaxWidth(List<String> strings, TextStyle textStyle) {
    double maxWidth = 0.0;

    for (var text in strings) {
      // Use TextPainter to measure the width of the text
      final TextPainter textPainter = TextPainter(
        text: TextSpan(text: text, style: textStyle),
        textDirection: TextDirection.ltr,
      )..layout();

      // Update maxWidth if this text's width is larger
      maxWidth = maxWidth > textPainter.width ? maxWidth : textPainter.width;
    }

    return maxWidth;
  }

  double calculateTotalHeight({
    required List<String> strings,
    required TextStyle textStyle,
    required double spacingBetweenLines,
  }) {
    double totalHeight = 0.0;

    for (var text in strings) {
      // Use TextPainter to measure the height of the text
      final TextPainter textPainter = TextPainter(
        text: TextSpan(text: text, style: textStyle),
        textDirection: TextDirection.ltr,
      )..layout();

      // Add the height of the current text
      totalHeight += textPainter.height;

      // Add spacing between lines (except after the last line)
      if (text != strings.last) {
        totalHeight += spacingBetweenLines;
      }
    }

    return totalHeight;
  }

  Widget fellowReaderSkeleton(
    bool isBorder,
    int index,
    String userImage,
    double maxWidth,
  ) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 320,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? Colors.transparent
            : AppConstants.skeletonBackgroundColor,
        border: isBorder
            ? Border.all(
                color: AppConstants.primaryColor,
                width: 1.5,
              )
            : Border.all(
                color: Colors.transparent,
              ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MarqueeList(
                        children: [
                          Text(
                            fellowReadList[index].userName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 3,
                      ),
                      Text(
                        fellowReadList[index].userLocation ?? '',
                        overflow: TextOverflow.ellipsis,
                        style: lbBold.copyWith(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: CustomCachedNetworkImage(
                    imageUrl: userImage,
                    width: 45,
                    height: 45,
                    errorImage: AppConstants.profileLogoImagePath,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 15,
            ),
            // if (fellowReadList[index].bookName?.isNotEmpty ?? false) ...[
            Text(
              (fellowReadList[index].bookName?.isNotEmpty ?? false)
                  ? "Currently Reading:"
                  : "No book in currently reading",
              overflow: TextOverflow.ellipsis,
              style: lbRegular.copyWith(
                fontSize: 12,
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MarqueeList(
                        children: [
                          Text(
                            fellowReadList[index].bookName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      MarqueeList(
                        children: [
                          Text(
                            fellowReadList[index].bookAuthor ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbRegular.copyWith(
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 15),
                NetworkAwareTap(
                  onTap: () {
                    /// SHOW BOTTOM SHEET
                    if (((fellowReadList[index].totalMatches ?? 0) > 0)) {
                      fellowReaderMatchesBottomSheet(
                        fellowReadList[index].currentlyReadingBooks ?? [],
                        fellowReadList[index].toBeReadBooks ?? [],
                        fellowReadList[index].fiveStarMatchBooks ?? [],
                        fellowReadList[index].userName ?? '',
                        fellowReadList[index].userId ?? 0,
                      );
                    } else {
                      log("NO MATCHES");
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: ((fellowReadList[index].totalMatches ?? 0) > 0)
                          ? AppConstants.textGreenColor
                          : AppConstants.isActiveRequestColor,
                    ),
                    child: Text(
                      ((fellowReadList[index].totalMatches ?? 0) == 0)
                          ? "No Match"
                          : ((fellowReadList[index].totalMatches ?? 0) > 0 &&
                                  (fellowReadList[index].totalMatches ?? 0) < 2)
                              ? "${fellowReadList[index].totalMatches ?? 0} Match"
                              : "${fellowReadList[index].totalMatches ?? 0} Matches",
                      overflow: TextOverflow.ellipsis,
                      style: lbBold.copyWith(
                        fontSize: 12,
                        color: ((fellowReadList[index].totalMatches ?? 0) > 0)
                            ? AppConstants.primaryColor
                            : Colors.black38,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> fellowReaderMatchesBottomSheet(
    List<int> cIds,
    List<int> toBeReadIds,
    List<int> starIds,
    String userName,
    int userId,
  ) async {
    return showModalBottomSheet(
      barrierColor: Colors.white60,
      isScrollControlled: true,
      useRootNavigator: true,
      useSafeArea: true,
      constraints: BoxConstraints(
        minHeight: MediaQuery.of(context).size.height * 0.3,
      ),
      context: context,
      builder: (context) {
        return FellowReaderMatchesBottomSheet(
          currentlyReadMatch: cIds,
          fivestarMatch: starIds,
          tobeReadMatch: toBeReadIds,
          userName: userName,
          userId: userId,
        );
      },
    );
  }

  Future<void> clubMatchBottomSheet(int index) async {
    log("Matches : ${matchedMeetings?.length ?? 0}");
    return showModalBottomSheet(
      barrierColor: Colors.white60,
      isScrollControlled: true,
      useRootNavigator: true,
      context: context,
      builder: (context) {
        return ClubOpeningMatchesBottomSheet(
          clubId: clubList[index].clubId ?? 0,
          clubName: clubList[index].clubName ?? '',
          memberRequestPrompt: clubList[index].memberRequestPrompt ?? '',
          matchedMeetings: matchedMeetings ?? [],
          clubList: clubList[index],
        );
      },
    );
  }

  List<ClubCharterModel> profileInfoList = [
    ClubCharterModel(
      rules: "Fill in your profile.",
    ),
    ClubCharterModel(
      rules: 'Update your bookcase.',
    ),
  ];

  List<ClubCharterModel> clubInfoList = [
    ClubCharterModel(
      rules: 'If you want, create your own club to become a “Club Leader.”',
    ),
  ];

  List<ClubCharterModel> homeScreenInfoList = [
    ClubCharterModel(
      rules:
          'Scroll the “New Club Openings” & “Fellow Readers” home page feeds.',
    ),
    ClubCharterModel(
      rules: 'Discover people & clubs with similar interests.',
    ),
    ClubCharterModel(
      rules: 'Vibe? Request to join a club or invite people to yours.',
    ),
  ];

  Future<void> showInfoDialog() async {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: textWidget18('Welcome to El Junto!'),
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      textWidget12('To get started:\n'),
                      textWidget12('“Profile” Page:'),
                      Column(
                        children: profileInfoList.map((e) {
                          return Padding(
                            padding: const EdgeInsets.only(left: 3),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    "•",
                                    textAlign: TextAlign.start,
                                    style: lbRegular.copyWith(
                                      fontSize: 15,
                                      height: 1.3,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 5),
                                Expanded(
                                  child: textWidget12(
                                    e.rules ?? '',
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                      textWidget12('\n“Clubs” Page:'),
                      Column(
                        children: clubInfoList.map((e) {
                          return Padding(
                            padding: const EdgeInsets.only(left: 3),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    "•",
                                    textAlign: TextAlign.start,
                                    style: lbRegular.copyWith(
                                      fontSize: 15,
                                      // height: 0.8,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 5),
                                Expanded(
                                  child: textWidget12(
                                    e.rules ?? '',
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                      textWidget12(
                        '\n“Home” & “Search” Pages:',
                      ),
                      Column(
                        children: homeScreenInfoList.map((e) {
                          return Padding(
                            padding: const EdgeInsets.only(left: 3),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Align(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    "•",
                                    textAlign: TextAlign.start,
                                    style: lbRegular.copyWith(
                                      fontSize: 15,
                                      height: 1.3,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 5),
                                Expanded(
                                  child: textWidget12(
                                    e.rules ?? '',
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 25),
                      Center(
                        child: NetworkAwareTap(
                          onTap: () {
                            context.pop();
                          },
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width / 3.5,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.textGreenColor,
                            ),
                            child: Center(
                              child: textWidget18(
                                "OK",
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget textWidget18(String title) {
    return Text(
      title,
      textAlign: TextAlign.center,
      // overflow: TextOverflow.ellipsis,
      style: lbRegular.copyWith(
        fontSize: 18,
      ),
    );
  }

  Widget textWidget12(String title) {
    return Text(
      title,
      textAlign: TextAlign.start,
      style: lbRegular.copyWith(
        fontSize: 12,
      ),
    );
  }
}
