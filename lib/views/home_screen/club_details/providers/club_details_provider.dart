import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/generic_messages.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/home_model/home_screen2_model/meeting_model.dart';
import 'package:eljunto/models/home_model/home_screen2_model/member_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// Dedicated provider for Club Details screen (non-member view)
/// Implements targeted refresh methods and proper state management
class ClubDetailsProvider with ChangeNotifier {
  // Dependencies
  BookClubController? _bookClubController;

  // State variables
  BookClubModel? _bookClubModel;
  List<MemberModel>? _memberList;
  List<MeetingList>? _upcomingMeetings;
  List<MeetingList>? _previousMeetings;

  // User data
  int? _loggedInUserId;
  String? _userName;

  // Loading states
  bool _isInitialLoading = false;
  bool _isMembersLoading = false;
  bool _isUpcomingMeetingsLoading = false;
  bool _isPreviousMeetingsLoading = false;
  bool _isJoinRequestLoading = false;

  // Pagination
  int _upcomingMeetingLimit = 10;
  int _previousMeetingLimit = 10;
  int _upcomingMeetingCount = 0;
  int _previousMeetingCount = 0;
  final int _offset = 0;

  // Join club validation
  bool _showValidationMessage = false;

  // Getters
  BookClubModel? get bookClubModel => _bookClubModel;
  List<MemberModel>? get memberList => _memberList;
  List<MeetingList>? get upcomingMeetings => _upcomingMeetings;
  List<MeetingList>? get previousMeetings => _previousMeetings;

  int? get loggedInUserId => _loggedInUserId;
  String? get userName => _userName;

  bool get isInitialLoading => _isInitialLoading;
  bool get isMembersLoading => _isMembersLoading;
  bool get isUpcomingMeetingsLoading => _isUpcomingMeetingsLoading;
  bool get isPreviousMeetingsLoading => _isPreviousMeetingsLoading;
  bool get isJoinRequestLoading => _isJoinRequestLoading;

  int get upcomingMeetingCount => _upcomingMeetingCount;
  int get previousMeetingCount => _previousMeetingCount;

  bool get showValidationMessage => _showValidationMessage;

  BookClubController? get bookClubController => _bookClubController;

  /// Initialize the provider with required dependencies
  void initialize(BuildContext context) {
    _bookClubController =
        Provider.of<BookClubController>(context, listen: false);
  }

  /// Initialize user data from local storage
  Future<void> initializeUserData() async {
    _userName = await CommonHelper.getLoggedinUserName();
    _loggedInUserId = await CommonHelper.getLoggedInUserId();
    notifyListeners();
  }

  /// Load all initial data for the club details screen
  Future<void> loadInitialData(int bookClubId, BuildContext context) async {
    _isInitialLoading = true;
    notifyListeners();

    try {
      await initializeUserData();

      // Load all data concurrently for better performance
      await Future.wait([
        getClubDetails(bookClubId, context),
        getBookClubMembers(bookClubId, context),
        getUpcomingMeetings(bookClubId, false, context),
        getPreviousMeetings(bookClubId, false, context),
      ]);
    } catch (e) {
      log('Error loading initial data: $e');
    } finally {
      _isInitialLoading = false;
      notifyListeners();
    }
  }

  /// Get club details - targeted refresh method
  Future<void> getClubDetails(int bookClubId, BuildContext context) async {
    try {
      final responseMap = await _bookClubController?.getBookClubs(
        '',
        null,
        bookClubId,
        context,
        _offset,
        10,
      );

      if (responseMap?["statusCode"] == 200) {
        log(responseMap?["data"].toString() ?? '');
        List<dynamic> data = responseMap?["data"] ?? [];
        if (data.isNotEmpty) {
          _bookClubModel = BookClubModel.fromJson(data[0]);
          notifyListeners();
        }
      }
    } catch (e) {
      log('Error getting club details: $e');
    }
  }

  /// Get book club members - targeted refresh method
  Future<void> getBookClubMembers(int bookClubId, BuildContext context) async {
    _isMembersLoading = true;
    notifyListeners();

    try {
      final responseMap = await _bookClubController?.getBookClubMembers(
        bookClubId,
        context,
      );

      if (responseMap?["statusCode"] == 200) {
        log(responseMap?["data"].toString() ?? '');
        _memberList = (responseMap?["data"] as List?)
            ?.map((item) => MemberModel.fromJson(item))
            .toList();
        notifyListeners();
      }
    } catch (e) {
      log('Error getting book club members: $e');
    } finally {
      _isMembersLoading = false;
      notifyListeners();
    }
  }

  /// Get upcoming meetings with pagination support
  Future<void> getUpcomingMeetings(
      int bookClubId, bool isLoadMore, BuildContext context) async {
    if (isLoadMore &&
        (_upcomingMeetings?.length ?? 0) >= _upcomingMeetingCount) {
      return; // No more data to load
    }

    _isUpcomingMeetingsLoading = true;
    if (isLoadMore) {
      _upcomingMeetingLimit += 10;
    }
    notifyListeners();

    try {
      await _bookClubController?.getBookClubUpcomingMeetings(
        bookClubId,
        _upcomingMeetingLimit,
        _offset,
        context,
      );

      _upcomingMeetingCount =
          _bookClubController?.upcomingMeetingModel?.count ?? 0;
      _upcomingMeetings = _bookClubController?.upcomingMeetingModel?.data;
      notifyListeners();
    } catch (e) {
      log('Error getting upcoming meetings: $e');
    } finally {
      _isUpcomingMeetingsLoading = false;
      notifyListeners();
    }
  }

  /// Get previous meetings with pagination support
  Future<void> getPreviousMeetings(
      int bookClubId, bool isLoadMore, BuildContext context) async {
    if (isLoadMore &&
        (_previousMeetings?.length ?? 0) >= _previousMeetingCount) {
      return; // No more data to load
    }

    _isPreviousMeetingsLoading = true;
    if (isLoadMore) {
      _previousMeetingLimit += 10;
    }
    notifyListeners();

    try {
      await _bookClubController?.getBookClubPreviousMeetings(
        bookClubId,
        _previousMeetingLimit,
        _offset,
        context,
      );

      _previousMeetingCount =
          _bookClubController?.previousMeetingModel?.count ?? 0;
      _previousMeetings = _bookClubController?.previousMeetingModel?.data;
      notifyListeners();
    } catch (e) {
      log('Error getting previous meetings: $e');
    } finally {
      _isPreviousMeetingsLoading = false;
      notifyListeners();
    }
  }

  /// Submit join club request
  Future<bool> submitJoinRequest(
      int bookClubId, String requestMessage, BuildContext context) async {
    if (requestMessage.isEmpty) {
      _showValidationMessage = true;
      notifyListeners();
      return false;
    }

    _isJoinRequestLoading = true;
    _showValidationMessage = false;
    notifyListeners();

    try {
      final payload = {
        "bookClubId": bookClubId,
        "userId": _loggedInUserId,
        "userType": ClubMemberType.member,
        "initiatedBy": _loggedInUserId,
        "requestMessage": requestMessage
      };

      final responseMap =
          await _bookClubController?.addMember(payload, context);

      if (responseMap?["statusCode"] == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log('Error submitting join request: $e');
      return false;
    } finally {
      _isJoinRequestLoading = false;
      notifyListeners();
    }
  }

  /// Clear validation message
  void clearValidationMessage() {
    _showValidationMessage = false;
    notifyListeners();
  }

  /// Check if club has vacancies
  bool get hasVacancies => (_bookClubModel?.totalVacancies ?? 0) > 0;

  /// Get success message for join request
  String get joinRequestSuccessMessage => GenericMessages.joinRequestSuccess;

  /// Refresh specific section
  Future<void> refreshSection(
      String section, int bookClubId, BuildContext context) async {
    switch (section) {
      case 'club_details':
        await getClubDetails(bookClubId, context);
        break;
      case 'members':
        await getBookClubMembers(bookClubId, context);
        break;
      case 'upcoming_meetings':
        _upcomingMeetingLimit = 10; // Reset pagination
        await getUpcomingMeetings(bookClubId, false, context);
        break;
      case 'previous_meetings':
        _previousMeetingLimit = 10; // Reset pagination
        await getPreviousMeetings(bookClubId, false, context);
        break;
    }
  }
}
