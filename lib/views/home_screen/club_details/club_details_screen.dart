import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:eljunto/views/home_screen/club_details/providers/club_details_provider.dart';
import 'package:eljunto/views/home_screen/club_details/widgets/club_discovery_header_section.dart';
import 'package:eljunto/views/home_screen/club_details/widgets/club_public_members_section.dart';
import 'package:eljunto/views/home_screen/club_details/widgets/public_previous_meetings_section.dart';
import 'package:eljunto/views/home_screen/club_details/widgets/public_upcoming_meetings_section.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

/// Refactored Club Details Screen using Provider pattern
/// Uses modular widget structure with targeted refresh methods and proper separation of concerns
class ClubDetails extends StatefulWidget {
  final int bookClubId;
  final String? bookClubName;
  final String? impromptuCount;

  const ClubDetails({
    super.key,
    required this.bookClubId,
    this.bookClubName,
    this.impromptuCount,
  });

  @override
  State<ClubDetails> createState() => _ClubDetailsState();
}

class _ClubDetailsState extends State<ClubDetails> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<ClubDetailsProvider>(context, listen: false);
      provider.initialize(context);
      provider.loadInitialData(widget.bookClubId, context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ClubDetailsProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          appBar: _buildAppBar(),
          body: Container(
            width: MediaQuery.sizeOf(context).width,
            height: MediaQuery.sizeOf(context).height,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  AppConstants.bgImagePath,
                ),
                filterQuality: FilterQuality.high,
                fit: BoxFit.fitWidth,
              ),
            ),
            child: RefreshIndicator(
              onRefresh: () => _handleRefresh(provider),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Skeletonizer(
                  effect: const SoldColorEffect(
                    color: AppConstants.skeletonforgroundColor,
                    lowerBound: 0.1,
                    upperBound: 0.5,
                  ),
                  containersColor: AppConstants.skeletonBackgroundColor,
                  enabled: provider.isInitialLoading,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 25),

                      // Club Discovery Header Section
                      ClubDiscoveryHeaderSection(bookClubId: widget.bookClubId),

                      const SizedBox(height: 25),

                      // Club Public Members Section
                      ClubPublicMembersSection(bookClubId: widget.bookClubId),

                      const SizedBox(height: 25),

                      // Public Upcoming Meetings Section
                      PublicUpcomingMeetingsSection(
                          bookClubId: widget.bookClubId),

                      const SizedBox(height: 25),

                      // Public Previous Meetings Section
                      PublicPreviousMeetingsSection(
                          bookClubId: widget.bookClubId),

                      const SizedBox(height: 25),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(80),
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1.5,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
        child: PreviousScreenAppBar(
          bookName: widget.bookClubName,
          isSetProfile: true,
          impromptuCount: widget.impromptuCount,
        ),
      ),
    );
  }

  /// Handle pull-to-refresh
  Future<void> _handleRefresh(ClubDetailsProvider provider) async {
    await provider.loadInitialData(widget.bookClubId, context);
  }
}
