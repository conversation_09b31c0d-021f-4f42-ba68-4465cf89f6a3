import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/views/home_screen/club_details/providers/club_details_provider.dart';

/// Widget for displaying public previous meetings section
class PublicPreviousMeetingsSection extends StatefulWidget {
  final int bookClubId;

  const PublicPreviousMeetingsSection({
    super.key,
    required this.bookClubId,
  });

  @override
  State<PublicPreviousMeetingsSection> createState() =>
      _PublicPreviousMeetingsSectionState();
}

class _PublicPreviousMeetingsSectionState
    extends State<PublicPreviousMeetingsSection> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll events for pagination
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      final provider = Provider.of<ClubDetailsProvider>(context, listen: false);
      if (!provider.isPreviousMeetingsLoading &&
          (provider.previousMeetings?.length ?? 0) <
              provider.previousMeetingCount) {
        CommonHelper.networkClose(
          provider.getPreviousMeetings(widget.bookClubId, true, context),
          context,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ClubDetailsProvider>(
      builder: (context, provider, child) {
        final previousMeetings = provider.previousMeetings;

        if (previousMeetings?.isEmpty ?? true) {
          return Column(
            children: [
              const Divider(
                thickness: 2,
                color: AppConstants.primaryColor,
              ),
              const SizedBox(height: 25),
              Skeleton.replace(
                replacement: Container(
                  padding: const EdgeInsets.only(left: 20),
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  height: 50,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    color: AppConstants.skeletonBackgroundColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "No previous meetings",
                      textAlign: TextAlign.start,
                    ),
                  ),
                ),
                child: const NoDataWidget(
                  message: "No previous meetings",
                ),
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Divider(
              thickness: 2,
              color: AppConstants.primaryColor,
            ),
            const SizedBox(height: 25),
            
            // Section title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Text(
                "Previous Meetings",
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                  fontSize: 20,
                ),
              ),
            ),
            const SizedBox(height: 10),
            
            // Meetings list
            SizedBox(
              height: 170,
              child: Consumer<BookClubController>(
                builder: (context, bookClubController, child) {
                  return ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.only(
                        left: 10, right: 20, bottom: 25),
                    scrollDirection: Axis.horizontal,
                    itemCount: provider.isPreviousMeetingsLoading
                        ? (previousMeetings?.length ?? 0) + 1
                        : previousMeetings?.length ?? 0,
                    itemBuilder: (context, index) {
                      if (index == previousMeetings?.length &&
                          provider.isPreviousMeetingsLoading) {
                        return const Padding(
                          padding: EdgeInsets.only(left: 10.0),
                          child: Center(
                            child: CircularProgressIndicator(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        );
                      }

                      final meeting = previousMeetings?[index];
                      if (meeting == null) return const SizedBox.shrink();

                      final formattedDate =
                          CommonHelper.getDayMonthYearDateFormat(
                              meeting.meetingDate);

                      return Skeleton.replace(
                        replacement: _buildPreviousMeetingCard(
                          true,
                          meeting,
                          formattedDate,
                        ),
                        child: _buildPreviousMeetingCard(
                          false,
                          meeting,
                          formattedDate,
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build individual previous meeting card
  Widget _buildPreviousMeetingCard(
    bool isSkeleton,
    dynamic meeting,
    String formattedDate,
  ) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 250,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isSkeleton
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  meeting?.bookName ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 5),
            MarqueeList(
              children: [
                Text(
                  meeting?.bookAuthor ?? '',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 5),
            MarqueeList(
              children: [
                Text(
                  meeting?.partOfBookCovered ?? '',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 21),
            Text(
              formattedDate,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: lbItalic.copyWith(
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
