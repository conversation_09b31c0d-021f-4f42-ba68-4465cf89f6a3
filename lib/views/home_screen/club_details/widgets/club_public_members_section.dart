import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:eljunto/views/home_screen/club_details/providers/club_details_provider.dart';

/// Widget for displaying public club members section
class ClubPublicMembersSection extends StatelessWidget {
  final int bookClubId;

  const ClubPublicMembersSection({
    super.key,
    required this.bookClubId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ClubDetailsProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Text(
                "Members",
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                  fontSize: 20,
                ),
              ),
            ),
            const SizedBox(height: 10),
            
            // Members list
            SizedBox(
              height: 112,
              child: ListView.builder(
                padding: const EdgeInsets.only(left: 10, right: 20),
                scrollDirection: Axis.horizontal,
                itemCount: provider.memberList?.length ?? 0,
                itemBuilder: (context, index) {
                  final member = provider.memberList?[index];
                  if (member == null) return const SizedBox.shrink();
                  
                  final memberProfilePicture = member.userProfilePicture != null
                      ? Config.imageBaseUrl + member.userProfilePicture!
                      : AppConstants.profileLogoImagePath;

                  return NetworkAwareTap(
                    onTap: () {
                      context.pushNamed(
                        'club-member-profile',
                        extra: {
                          'userId': member.userId,
                          'userName': member.userName,
                        },
                      );
                    },
                    child: Skeleton.replace(
                      replacement: _buildMemberCard(
                        true,
                        member,
                        memberProfilePicture,
                      ),
                      child: _buildMemberCard(
                        false,
                        member,
                        memberProfilePicture,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build individual member card
  Widget _buildMemberCard(
    bool isSkeleton,
    dynamic member,
    String memberProfilePicture,
  ) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isSkeleton
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(
          left: 14.0,
          top: 14,
          bottom: 14,
          right: 14,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                // Leader star indicator
                if (member?.userType?.toUpperCase() ==
                    ClubMemberType.leader.toUpperCase()) ...[
                  Align(
                    alignment: Alignment.centerLeft,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(50),
                      child: Image.asset(
                        AppConstants.leaderStar,
                        height: 43,
                        width: 43,
                        fit: BoxFit.cover,
                        filterQuality: FilterQuality.high,
                      ),
                    ),
                  ),
                ],
                // Member profile picture
                Align(
                  alignment: Alignment.center,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(50),
                    child: CustomCachedNetworkImage(
                      imageUrl: memberProfilePicture,
                      width: 45,
                      height: 45,
                      errorImage: AppConstants.profileLogoImagePath,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              member?.userName ?? '',
              textAlign: TextAlign.start,
              overflow: TextOverflow.ellipsis,
              style: lbBold.copyWith(
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
