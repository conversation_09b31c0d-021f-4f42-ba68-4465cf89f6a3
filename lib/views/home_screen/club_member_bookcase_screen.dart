import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../models/book_case_model.dart';
import '../../reusableWidgets/profile_appbar.dart';

class ClubMemberBookCase extends StatefulWidget {
  final String? userName;
  final String? userHandle;
  final bool? userClubInvitation;
  final String? userProfilePicture;
  final int? userId;
  const ClubMemberBookCase({
    super.key,
    this.userName,
    this.userHandle,
    this.userClubInvitation,
    this.userId,
    this.userProfilePicture,
  });

  @override
  State<ClubMemberBookCase> createState() => _ClubMemberBookCaseState();
}

class _ClubMemberBookCaseState extends State<ClubMemberBookCase> {
  List<BookCaseModel>? bookCase = [];
  TextEditingController sortingController = TextEditingController();
  bool completeBookLoading = true;
  int offset = 0;
  int completeBookLimit = 10;
  int completeBookcount = 0;
  bool isLoading = false;
  final ScrollController _bookCaseScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _bookCaseScrollController.addListener(_bookCaseOnScroll);
    initializeData();
    sortingController.text = BookCaseSortingOptions.title;
  }

  // String _selectedOption = 'Title A-Z';

  final List<String> _options = [
    /*  'Title A-Z',
    'Date Completed',
    'Author',
    'Rating' */
    BookCaseSortingOptions.title,
    BookCaseSortingOptions.completionDate,
    BookCaseSortingOptions.author,
    BookCaseSortingOptions.ratings
  ];
  int? noOfBooks;

  Future<void> initializeData() async {
    setState(() {
      isLoading = true;
    });
    await getBookCaseByUserId(false).then((_) => setState(() {
          isLoading = false;
        }));
  }

  sortBookCase(String sortOption) {
    setState(() {
      bookCase = CommonHelper.sortBookCaseList(bookCase!, sortOption);
    });
  }

  void _bookCaseOnScroll() {
    if (_bookCaseScrollController.position.pixels >=
            _bookCaseScrollController.position.maxScrollExtent &&
        !completeBookLoading &&
        (bookCase?.length ?? 0) < (completeBookcount)) {
      CommonHelper.networkClose(getBookCaseByUserId(true), context);
    }
  }

  Future<void> getBookCaseByUserId(bool isMore) async {
    if ((bookCase?.length ?? 0) <= completeBookcount || isMore) {
      completeBookLoading = true;

      if (isMore) {
        completeBookLimit += 10;
      }
    }
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .allBooksRead(widget.userId ?? 0, completeBookLimit, offset, false,
              false, context)
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookList = [];
          completeBookcount = responseMap['count'];
          bookList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
          var result =
              CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);

          if (result.isNotEmpty) {
            // isLoading = false;
            bookCase = result[2];
            bookCase = CommonHelper.sortBookCaseList(
                bookCase!, sortingController.text);
            if (bookCase != null) {
              noOfBooks = bookCase?.length;
            } else {
              noOfBooks = 0;
            }
          }

          if (result.length >= completeBookcount) {
            completeBookLoading = false;
          }
          // isLoading = false;
        } else {
          noOfBooks = 0;
        }
      }).whenComplete(() {
        completeBookLoading = false;
      });
    } catch (e) {
      log(e.toString());
    }
  }

  @override
  void dispose() {
    _bookCaseScrollController.dispose();
    super.dispose();
  }

  List<String> emptyList = [''];
  @override
  Widget build(BuildContext context) {
    // print("Count : $noOfBooks");
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: ProfileAppBar(
            userName: widget.userName,
            userHandle: widget.userHandle,
            isOpenToClubInvitation: widget.userClubInvitation ?? false,
            userOwnProfile: false,
            userProfilePicture: widget.userProfilePicture,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          switchAnimationConfig: SwitchAnimationConfig(
            duration: const Duration(seconds: 1),
            switchInCurve: Curves.easeIn,
            switchOutCurve: Curves.easeOut,
            transitionBuilder: (child, animation) {
              return AnimatedSwitcher.defaultTransitionBuilder(
                  child, animation);
            },
          ),
          effect: const ShimmerEffect(
            baseColor: AppConstants.skeletonBackgroundColor,
            highlightColor: AppConstants.skeletonforgroundColor,
            duration: Duration(seconds: 1),
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isLoading,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Row(
                  children: [
                    Text(
                      "All Books Read",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 20,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      "$completeBookcount-Books",
                      textAlign: TextAlign.center,
                      style: lbItalic.copyWith(
                        fontSize: 20,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              if (bookCase == null || bookCase!.isEmpty) ...[
                const NoDataWidget(message: "No books in bookcase")
              ] else ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: SizedBox(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 2.3,
                    child: DropdownMenu(
                      inputDecorationTheme: const InputDecorationTheme(
                        alignLabelWithHint: true,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 10,
                        ),
                        filled: true,
                        fillColor: AppConstants.backgroundColor,
                        focusColor: AppConstants.primaryColor,
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(10),
                          ),
                        ),
                      ),
                      menuStyle: const MenuStyle(
                        shape: WidgetStatePropertyAll(
                          RoundedRectangleBorder(
                            side: BorderSide(
                              color: AppConstants.primaryColor,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.all(
                              Radius.circular(10),
                            ),
                          ),
                        ),
                        padding: WidgetStatePropertyAll(EdgeInsets.zero),
                        backgroundColor: WidgetStatePropertyAll(
                          AppConstants.backgroundColor,
                        ),
                      ),
                      expandedInsets: EdgeInsets.zero,
                      controller: sortingController,
                      requestFocusOnTap: false,
                      textStyle: lbBold.copyWith(
                        fontSize: 12,
                      ),
                      onSelected: (newValue) async {
                        sortingController.text = newValue.toString();
                        sortBookCase(sortingController.text);
                        const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: AppConstants.primaryColor,
                        );
                      },
                      dropdownMenuEntries: _options.map((role) {
                        return DropdownMenuEntry(
                          style: TextButton.styleFrom(
                            visualDensity: VisualDensity.comfortable,
                            side: const BorderSide(
                              width: 0.5,
                              color: AppConstants.primaryColor,
                            ),
                            textStyle: lbRegular.copyWith(
                              fontSize: 12,
                            ),
                          ),
                          value: role,
                          label: role,
                        );
                      }).toList(),
                      menuHeight: 200,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Expanded(
                  child: Consumer<BookCaseController>(
                      builder: (context, bookCaseController, child) {
                    return GridView.builder(
                      controller: _bookCaseScrollController,
                      padding: const EdgeInsets.only(
                          left: 20, right: 20, bottom: 25),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 15,
                        mainAxisSpacing: 15,
                        childAspectRatio: 0.80,
                      ),
                      itemCount: bookCase?.length,
                      itemBuilder: (BuildContext context, int index) {
                        print(
                            "Bool : ${completeBookcount <= (bookCase?.length ?? 0) && completeBookLoading}");
                        if (index == bookCase?.length && completeBookLoading) {
                          return const Padding(
                            padding: EdgeInsets.only(left: 10.0),
                            child: Center(
                              child: CircularProgressIndicator(
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          );
                        }
                        String formattedDate =
                            CommonHelper.getMonthYearDateFormat(
                                bookCase?[index].readingCompleteDate);
                        return Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: AppConstants.primaryColor,
                              width: 1.5,
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                MarqueeList(
                                  children: [
                                    Text(
                                      bookCase?[index].bookName ?? '',
                                      textAlign: TextAlign.start,
                                      overflow: TextOverflow.ellipsis,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                                MarqueeList(
                                  children: [
                                    Text(
                                      bookCase?[index].bookAuthor ?? '',
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.ellipsis,
                                      style: lbRegular.copyWith(
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 15,
                                ),
                                RatingBar(
                                  ignoreGestures: true,
                                  itemCount: 5,
                                  itemSize: 22,
                                  allowHalfRating: true,
                                  initialRating: bookCase?[index].ratings ?? 0,
                                  minRating: 0,
                                  ratingWidget: RatingWidget(
                                      full: const Icon(
                                        Icons.star,
                                        color: AppConstants.textGreenColor,
                                      ),
                                      half: const Icon(
                                        Icons.star_half,
                                        color: AppConstants.textGreenColor,
                                      ),
                                      empty: const Icon(
                                        Icons.star_border_outlined,
                                        color: AppConstants.textGreenColor,
                                      )),
                                  onRatingUpdate: (double value) {},
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                Row(
                                  children: [
                                    // bookCase?[index].topShelf == true
                                    //     ? Image.asset(
                                    //         AppConstants.topShelfBookIcon,
                                    //         height: 20,
                                    //         width: 22,
                                    //         filterQuality:
                                    //             FilterQuality.high,
                                    //       )
                                    //     : const SizedBox.shrink(),
                                    // bookCase?[index].topShelf == true
                                    //     ? const SizedBox(
                                    //         width: 10,
                                    //       )
                                    //     : const SizedBox.shrink(),
                                    bookCase?[index].is_currently_reading ==
                                            true
                                        ? Image.asset(
                                            AppConstants.currentlyReadingIcon,
                                            height: 20,
                                            width: 22,
                                            filterQuality: FilterQuality.high,
                                          )
                                        : const SizedBox(
                                            height: 20,
                                          ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    NetworkAwareTap(
                                      onTap: () {
                                        readReview(
                                          bookCase![index].bookName ?? "",
                                          bookCase![index].bookAuthor ?? "",
                                          bookCase![index].review ??
                                              "No review",
                                        );
                                      },
                                      child: Text(
                                        'Read Review',
                                        textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                        style: lbItalic.copyWith(
                                          decoration: TextDecoration.underline,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 15,
                                ),
                                Text(
                                  "Completed: $formattedDate",
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                  style: lbRegular.copyWith(
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  }),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void readReview(String bookName, String author, String review) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              // crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: NetworkAwareTap(
                    onTap: () {
                      context.pop();
                    },
                    child: Container(
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(top: 10, right: 10),
                      child: Image.asset(
                        AppConstants.closePopupImagePath,
                        height: 30,
                        width: 30,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Text(
                    "Review:",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    "$bookName, $author",
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(right: 20, left: 30),
                  child: TextFormField(
                    initialValue: review,
                    maxLines: 4,
                    readOnly: true,
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: AppConstants.backgroundColor,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      hintText: "No review",
                      hintStyle: lbRegular.copyWith(
                        fontSize: 12,
                      ),
                    ),
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    // margin: const EdgeInsets.symmetric(horizontal: 100),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
