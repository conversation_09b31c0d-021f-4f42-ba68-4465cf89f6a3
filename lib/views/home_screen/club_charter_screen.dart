import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../constants/constants.dart';
import '../../reusableWidgets/no_data_widget.dart';
import '../../reusableWidgets/previous_screen_appbar.dart';

class ClubCharter extends StatefulWidget {
  final String? bookClubName;
  final String? clubCharter;
  const ClubCharter({
    super.key,
    required this.bookClubName,
    required this.clubCharter,
  });

  @override
  State<ClubCharter> createState() => _ClubCharterState();
}

class _ClubCharterState extends State<ClubCharter> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: Color.fromRGBO(37, 57, 67, 1),
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.bookClubName,
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              "assets/images/PaperBackground.png",
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 20.0, top: 25),
              child: Text(
                "Club Charter",
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                  fontSize: 20,
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            widget.clubCharter?.isNotEmpty ?? false
                ? Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Text(
                      //"Heart. Grit. Friendships. My favorite book. I listen to it every year on my audio cassette.",
                      widget.clubCharter ?? '',
                      textAlign: TextAlign.start,
                      style: lbRegular.copyWith(fontSize: 14),
                    ),
                  )
                : const NoDataWidget(
                    message: "No Club Charter",
                  ),
            const SizedBox(
              height: 25,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: NetworkAwareTap(
                onTap: () {
                  context.pop();
                },
                child: Container(
                  height: 45,
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(49),
                    color: AppConstants.textGreenColor,
                  ),
                  child: Center(
                    child: Text(
                      "Go Back",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 25,
            ),
          ],
        ),
      ),
    );
  }
}
