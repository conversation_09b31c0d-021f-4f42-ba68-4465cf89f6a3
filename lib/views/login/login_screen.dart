// import 'dart:nativewrappers/_internal/vm/lib/developer.dart';
import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/app_version_controller.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:eljunto/services/deep_link_service.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../constants/constants.dart';
import '../../controller/inapp_purchase_controller.dart';
import '../../controller/login_controller.dart';
import '../../reusableWidgets/contact_us.dart';
import '../../reusableWidgets/custom_text_widget.dart';
import '../../services/notification_service.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool showPassword = true;
  bool loginSuccess = false;
  String? appVersion, appName;
  String errorMessage = '';
  bool isLoading = false;
  bool isFieldEnabled = true;
  late AppVersionProvider _appVersionProvider;

  NotificationServices notificationServices = NotificationServices();
  AppVersionProvider? versionProvider;
  SubscriptionController? subscriptionController;
  String? fcmToken;
  String? deviceId;

  @override
  void initState() {
    subscriptionController =
        Provider.of<SubscriptionController>(context, listen: false);

    versionProvider = Provider.of<AppVersionProvider>(context, listen: false);
    // getFCMToken();
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      deviceId = await CommonHelper.getDeviceName();

      clearValidationMessage();
      if (mounted) {
        _appVersionProvider =
            Provider.of<AppVersionProvider>(context, listen: false);
      }
      checkForUpdates();
    });
    notificationServices.isTokenRefresh(context);

    notificationServices.getDeviceToken().then((value) async {
      if (value.isNotEmpty) {
        fcmToken = value;
      } else {
        fcmToken = await notificationServices.getDeviceToken();
      }
      SharedPreferences pref = await SharedPreferences.getInstance();
      pref.setString('fcmToken', fcmToken!);
      await getFcmToken();
    });
  }

  Future<void> checkForUpdates() async {
    await _appVersionProvider.checkForUpdate(context);
  }

  Future<void> getFcmToken() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    String token = pref.getString('fcmToken') ?? '';
    log('In Login FCM Token: $token');
  }

  void _login() async {
    isLoading = true;
    isFieldEnabled = false;
    clearValidationMessage();
    if (fcmToken == null) {
      fcmToken = await notificationServices.getDeviceToken();
      SharedPreferences pref = await SharedPreferences.getInstance();
      pref.setString('fcmToken', fcmToken!);
    } else {
      Provider.of<LoginController>(context, listen: false)
          .loginFunction(
              _emailController.text.toLowerCase().trim(),
              _passwordController.text.trim(),
              fcmToken ?? '',
              deviceId ?? '',
              context)
          .then((value) async {
        if (value) {
          // ADD API CALL TO GET SUBSCRIPTION DETAILS 17 FEB 2025
          if (mounted) {
            await Provider.of<SubscriptionController>(context, listen: false)
                .getSubscriptionDetails(context);
          }
          if (mounted) {
            await Provider.of<InAppPurchaseController>(context, listen: false)
                .fetchProducts(context);
          }
          await isActiveSubscription();

          log("Subscription Details : ${subscriptionController?.subscriptionDetailList}");
          isLoading = false;
          SharedPreferences pref = await SharedPreferences.getInstance();
          bool? isProfileSetUp = pref.getBool('isUserNameAvailable');
          pref.setBool('isFirstTimeUser', false);
          if (mounted) {
            notificationServices.checkLoginStatusAndInit(context);
            onLoginSuccess(context, isProfileSetUp ?? false);
          }
        } else {
          isFieldEnabled = true;
          isLoading = false;
          setState(() {
            errorMessage = Provider.of<LoginController>(context, listen: false)
                .errorMessage;

            passwordValidation = false;
            emailpassValidation = false;
            emailValidation = false;
            emailpasswordInvalid = false;
          });
        }
      });
    }
  }

  Future<void> isActiveSubscription() async {
    await subscriptionController?.isActiveSubscription();
  }

  Future<void> onLoginSuccess(BuildContext context, bool isProfileSetUp) async {
    // Normal login operations...
    isFieldEnabled = true;

    // Check for pending deep links
    DeepLinkService deepLinkService = DeepLinkService();
    bool hasPendingLink = await deepLinkService.hasPendingDeepLink();

    log("HasPending Link : $hasPendingLink");

    bool shouldNavigate = true;
    String? route;
    Map<String, dynamic>? extraData;

    if (hasPendingLink) {
      Uri? pendingDeepLink = await deepLinkService.consumePendingDeepLink();
      if (pendingDeepLink != null) {
        log('Processing pending deep link after login: ${pendingDeepLink.toString()}');

        if (context.mounted) {
          // Set up deep link navigation
          route = pendingDeepLink.toString();
          shouldNavigate = true;
        }
      }
    } else if (context.mounted) {
      // No pending deep links, set up normal navigation
      if (isProfileSetUp) {
        if (subscriptionController?.verifySubscriptionModel?.data?.usubStatus ==
            'ACTIVE') {
          route = 'Home';
          shouldNavigate = true;
        } else {
          route = 'subscription';
          shouldNavigate = true;
        }
      } else {
        route = 'set-name-handle';
        extraData = {'email': _emailController.text};
        shouldNavigate = true;
      }
    }

    // Perform navigation if needed
    if (shouldNavigate && context.mounted && route != null) {
      // Perform the actual navigation
      if (route.startsWith('/')) {
        // Deep link navigation
        context.go(route);
      } else if (extraData != null) {
        // Named route with extra data
        context.pushNamed(route, extra: extraData);
      } else {
        // Simple named route
        context.goNamed(route);
      }

      return;
    }
    // If we reach here, no navigation happened, so clear fields normally
    clearController();
  }

  void clearController() {
    setState(() {
      _emailController.clear();
      _passwordController.clear();
      emailpassValidation = false;
      emailValidation = false;
      passwordValidation = false;
      emailpasswordInvalid = false;
      errorMessage = '';
    });
  }

  Future<void> clearLocalStorage() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  Future<void> clearValidationMessage() async {
    setState(() {
      errorMessage = '';
      passwordValidation = false;
      emailpassValidation = false;
      emailValidation = false;
      emailpasswordInvalid = false;
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();

    super.dispose();
  }

  bool emailpassValidation = false;
  bool emailValidation = false;
  bool passwordValidation = false;
  bool emailpasswordInvalid = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          // color: Colors.white,
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                toolbarHeight: 140,
                shape: const Border(
                  bottom: BorderSide(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
                centerTitle: true,
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      AppConstants.elJuntoLogo,
                      height: 100,
                      width: 80,
                      filterQuality: FilterQuality.high,
                      fit: BoxFit.contain,
                    ),
                    const VersionDisplay(),
                    const SizedBox(height: 25),
                  ],
                ),
                automaticallyImplyLeading: false,
                backgroundColor: AppConstants.textGreenColor,
              ),
              body: Form(
                key: _formKey,
                child: ListView(
                  physics: PageScrollPhysics(),
                  // clipBehavior: Clip.none,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        children: <Widget>[
                          const SizedBox(height: 25),
                          Text(
                            'Login',
                            style: lbRegular.copyWith(
                              fontSize: 24,
                              height: 1.24,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 25),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text("Email:",
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  )),
                            ],
                          ),
                          const SizedBox(height: 10),
                          TextFormField(
                            controller: _emailController,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                            ),
                            keyboardType: TextInputType.emailAddress,
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.all(10),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                  color: AppConstants.primaryColor,
                                  width: 1.5,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                  color: Colors.black38,
                                  width: 1.5,
                                ),
                              ),
                              filled: true,
                              fillColor: Colors.white.withOpacity(0.8),
                              errorStyle: errorMsg,
                            ),
                            enabled: isFieldEnabled,
                            // onChanged: (value) {
                            //   setState(() {
                            //     errorMessage = '';
                            //   });
                            // },
                          ),
                          const SizedBox(height: 25),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                "Password:",
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          TextFormField(
                            controller: _passwordController,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                            ),
                            decoration: InputDecoration(
                              filled: true,
                              fillColor: Colors.white.withOpacity(0.8),
                              errorStyle: errorMsg,
                              contentPadding: const EdgeInsets.all(10),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                  color: AppConstants.primaryColor,
                                  width: 1.5,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                  color: Colors.black38,
                                  width: 1.5,
                                ),
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                    showPassword
                                        ? Icons.visibility_off
                                        : Icons.visibility,
                                    color: AppConstants.primaryColor),
                                onPressed: () {
                                  setState(() {
                                    showPassword = !showPassword;
                                  });
                                },
                              ),
                              enabled: isFieldEnabled,
                            ),
                            obscureText: showPassword,
                            // onChanged: (value) {
                            //   setState(() {
                            //     errorMessage = '';
                            //   });
                            // },
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              NetworkAwareTap(
                                onTap: () {
                                  clearController();
                                  setState(() {});
                                  context.pushNamed('sign-up', extra: {
                                    "isForgotPassword": true,
                                  });
                                },
                                child: Text(
                                  'Forgot Password?',
                                  style: lbBold.copyWith(
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              // const Spacer(),
                              if (emailpassValidation)
                                Flexible(
                                  child: Text(
                                    "*Enter email & password",
                                    overflow: TextOverflow.ellipsis,
                                    style: errorMsg,
                                  ),
                                ),
                              // emailpassValidation
                              //     ? Text(
                              //         "*Enter email & password",
                              //         overflow: TextOverflow.ellipsis,
                              //         style: errorMsg,
                              //       )
                              //     : const SizedBox.shrink(),
                              if (emailValidation || passwordValidation)
                                Flexible(
                                  child: Text(
                                    "*Enter email & password",
                                    overflow: TextOverflow.ellipsis,
                                    style: errorMsg,
                                  ),
                                  // : const SizedBox.shrink(),
                                ),
                              // if (passwordValidation)
                              //   Flexible(
                              //     child: Text(
                              //       "*Enter password",
                              //       overflow: TextOverflow.ellipsis,
                              //       style: errorMsg,
                              //     ),
                              //     // : const SizedBox.shrink(),
                              //   ),
                              if (emailpasswordInvalid)
                                Flexible(
                                  child: Text(
                                    "*Email/password incorrect",
                                    overflow: TextOverflow.ellipsis,
                                    style: errorMsg,
                                  ),
                                  // : const SizedBox.shrink(),
                                ),
                              if (errorMessage != '')
                                // if (errorMessage
                                //     .toLowerCase()
                                //     .contains('Invalid'))
                                Flexible(
                                  child: Text(
                                    "*$errorMessage",
                                    overflow: TextOverflow.ellipsis,
                                    style: errorMsg,
                                  ),
                                  // : const SizedBox.shrink(),
                                ),
                            ],
                          ),
                          const SizedBox(height: 25),
                          if (errorMessage != '')
                            if (errorMessage.toLowerCase().contains('Your') ||
                                errorMessage.toLowerCase().contains('Account'))
                              Text(
                                errorMessage,
                                overflow: TextOverflow.ellipsis,
                                style: errorMsg,
                              ),
                          CustomLoaderButton(
                            // loginText: 'Login',
                            buttonWidth: isLoading
                                ? 45.0
                                : MediaQuery.of(context).size.width,
                            buttonRadius: 30.0,
                            buttonChild: isLoading
                                ? const CircularProgressIndicator(
                                    valueColor:
                                        AlwaysStoppedAnimation(Colors.white),
                                    strokeWidth: 3.0,
                                  )
                                : Text(
                                    'Login',
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                            buttonPressed: () {
                              bool validation =
                                  _formKey.currentState!.validate();

                              if (EmailValidator.validate(
                                      _emailController.text) &&
                                  _passwordController.text.isEmpty &&
                                  validation) {
                                setState(() {
                                  emailpassValidation = true;
                                  emailValidation = false;
                                  emailpasswordInvalid = false;
                                  passwordValidation = false;
                                  errorMessage = '';
                                });
                              } else if (!EmailValidator.validate(
                                      _emailController.text) &&
                                  validation) {
                                setState(() {
                                  emailValidation = true;
                                  emailpassValidation = false;
                                  emailpasswordInvalid = false;
                                  passwordValidation = false;
                                  errorMessage = '';
                                });
                              } else if (_passwordController.text.isEmpty &&
                                  validation) {
                                setState(() {
                                  passwordValidation = true;
                                  emailpassValidation = false;
                                  emailValidation = false;
                                  emailpasswordInvalid = false;
                                  errorMessage = '';
                                });
                              } else {
                                _login();
                              }
                            },
                          ),
                          const SizedBox(height: 25),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                "Don't have an account? ",
                                style: lbRegular.copyWith(fontSize: 14),
                              ),
                              NetworkAwareTap(
                                onTap: () {
                                  isLoading = false;
                                  clearController();
                                  context.pushNamed('sign-up', extra: {
                                    "isForgotPassword": false,
                                  });
                                },
                                child: Text(
                                  'Sign Up',
                                  style: lbBold.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.textGreenColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    const ContactUs(),
                  ],
                ),
              ),
            ),
            NoConnectionTag(bottomPosition: 70),
          ],
        ),
      ),
    );
  }
}
