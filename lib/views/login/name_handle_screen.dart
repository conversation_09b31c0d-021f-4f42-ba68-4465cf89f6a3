import 'dart:developer';

import 'package:eljunto/controller/login_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../constants/constants.dart';
import '../../../reusableWidgets/custom_text_widget.dart';
import '../../constants/text_style.dart';
import '../../reusableWidgets/custom_button.dart';

class NameAndHandlePage extends StatefulWidget {
  final String email;
  final String token;
  final String otp;

  const NameAndHandlePage({
    super.key,
    required this.email,
    required this.token,
    required this.otp,
  });

  @override
  State createState() => _NameAndHandlePageState();
}

class _NameAndHandlePageState extends State<NameAndHandlePage> {
  final _nameFormKey = GlobalKey<FormState>();
  final _handleFormKey = GlobalKey<FormState>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController locationController = TextEditingController();
  final TextEditingController bioController = TextEditingController();
  final TextEditingController handleController = TextEditingController();

  String formattedHandler = '';
  bool _isHandlerAvailable = false;
  bool emptyHandle = false;
  bool _nameSet = false;
  String? appName, appVersion;
  String errorMessage = '';
  bool setNameFlag = false;
  bool setLocationFlag = false;
  bool setBioFlag = false;
  bool handlerFlag = false;
  bool isLoading = false;
  bool isFieldEnabled = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    nameController.dispose();
    locationController.dispose();
    bioController.dispose();
    handleController.dispose();
    super.dispose();
  }

  void _submitName() {
    bool validation = _nameFormKey.currentState!.validate();

    isLoading = true;
    if (validation &&
        nameController.text.trim().isNotEmpty &&
        locationController.text.trim().isNotEmpty &&
        bioController.text.trim().isNotEmpty) {
      setState(() {
        _nameSet = true;
        setNameFlag = false;
        setLocationFlag = false;
        setBioFlag = false;
      });
      isFieldEnabled = true;

      isLoading = false;
    } else {
      setState(() {
        isLoading = false;
        // _nameSet = false;
        // setNameFlag = true;
      });
    }
  }

  void _submitHandle() {
    if (_handleFormKey.currentState!.validate() &&
        handleController.text.trim().isNotEmpty &&
        _isHandlerAvailable) {
      setState(() {
        handlerFlag = false;
      });

      log('Name : ${nameController.text}');
      log('Handler : $formattedHandler');
      isLoading = true;
      isFieldEnabled = false;

      Provider.of<LoginController>(context, listen: false)
          .setNameAndHandle(
        widget.email,
        widget.token,
        widget.otp,
        nameController.text.trim(),
        formattedHandler.trim(),
        locationController.text.trim(),
        bioController.text.trim(),
        context,
      )
          .then((responseMap) async {
        await saveLocally();
        if (responseMap["statusCode"] == 200) {
          if (mounted) {
            context.goNamed('subscription');
            // context.pushNamed('profilesetup');
          }
          isFieldEnabled = true;

          isLoading = false;
        } else {
          setState(() {
            isFieldEnabled = true;
            isLoading = false;
            errorMessage = responseMap['error'];
          });
        }
      });
    } else {
      // setState(() {
      //   handlerFlag = true;
      // });
    }
  }

  Future<void> saveLocally() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setString('userName', nameController.text);
    pref.setString('userHandle', formattedHandler);
    pref.setBool('isFirstTimeUser', true);
    pref.setBool("isUserBioAndLocationAvailable", true);
  }

  void checkHandler(String handler) {
    if (handler.trim().isNotEmpty) {
      formattedHandler = '@${handler.trim()}';
      setState(() {
        handlerFlag = false;
      });

      Provider.of<LoginController>(context, listen: false)
          .checkAavailabilityOfHandle(
              widget.email, formattedHandler.trim(), context)
          .then((responseMap) {
        if (responseMap["statusCode"] == 200) {
          // setState(() {
          _isHandlerAvailable = true;
          // });
        } else {
          // setState(() {
          errorMessage = responseMap['error'];
          handlerFlag = false;
          _isHandlerAvailable = false;
          // });
        }
        setState(() {});
        log("Handle Available $_isHandlerAvailable");
      });
    } else {
      handlerFlag = true;

      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: PopScope(
          canPop: false,
          child: Stack(
            children: [
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  toolbarHeight: 140,
                  shape: const Border(
                    bottom: BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                  ),
                  automaticallyImplyLeading: false,
                  backgroundColor: AppConstants.textGreenColor,
                  centerTitle: true,
                  title: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // const SizedBox(
                      //   height: 20,
                      // ),
                      Image.asset(
                        AppConstants.elJuntoLogo,
                        height: 100,
                        width: 80,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.contain,
                      ),
                      // SizedBox(height: 8),
                      const VersionDisplay(),
                      const SizedBox(height: 25),
                    ],
                  ),
                ),
                body: ListView(
                  //crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const SizedBox(
                      height: 25,
                    ),
                    Text(
                      'Create New Account',
                      style: lbRegular.copyWith(
                        fontSize: 24,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (!_nameSet) ...[
                      Form(
                        key: _nameFormKey,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 19.0),
                          child: Column(
                            children: [
                              const SizedBox(
                                height: 25,
                              ),

                              textWidget("Name"),
                              const SizedBox(
                                height: 10,
                              ),
                              Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  TextFormField(
                                    controller: nameController,
                                    keyboardType: TextInputType.text,
                                    textCapitalization:
                                        TextCapitalization.sentences,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                    maxLength: 50,
                                    decoration: InputDecoration(
                                      counterStyle: lbRegular.copyWith(
                                        fontSize: 14,
                                      ),
                                      contentPadding: const EdgeInsets.all(10),
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(5.0),
                                        borderSide: const BorderSide(
                                            color: AppConstants.primaryColor,
                                            width: 2.5),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white.withOpacity(0.8),
                                      errorStyle: errorMsg,
                                    ),
                                    onChanged: (value) {
                                      if (value.trim().isEmpty) {
                                        setNameFlag = true;
                                      } else {
                                        setNameFlag = false;
                                      }
                                      setState(() {});
                                    },
                                    validator: (value) {
                                      setNameFlag = false;
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        setState(() {
                                          setNameFlag = true;
                                        });
                                        return null;
                                      } else {
                                        setState(() {
                                          setNameFlag = false;
                                        });
                                        return null;
                                      }
                                    },
                                  ),
                                  Positioned(
                                    top: 55,
                                    left: 0,
                                    right: 0,
                                    child: setNameFlag
                                        ? Text(
                                            "*Enter name",
                                            style: errorMsg,
                                          )
                                        : const SizedBox.shrink(),
                                  ),
                                ],
                              ),

                              setNameFlag
                                  ? const SizedBox(
                                      height: 25,
                                    )
                                  : const SizedBox.shrink(),
                              textWidget(
                                "Location",
                              ),

                              const SizedBox(
                                height: 10,
                              ),

                              /// TEXTFORMFIELD FOR LOCATION

                              Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  TextFormField(
                                    controller: locationController,
                                    keyboardType: TextInputType.text,
                                    textCapitalization:
                                        TextCapitalization.sentences,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                    // maxLength: 50,
                                    decoration: InputDecoration(
                                      suffixIcon: NetworkAwareTap(
                                        onTap: () {},
                                        child: Image.asset(
                                          'assets/icons/Edit.png',
                                          filterQuality: FilterQuality.high,
                                          fit: BoxFit.cover,
                                          height: 38,
                                          width: 38,
                                        ),
                                      ),
                                      counterStyle: lbRegular.copyWith(
                                        fontSize: 14,
                                      ),
                                      contentPadding: const EdgeInsets.all(10),
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(5.0),
                                        borderSide: const BorderSide(
                                            color: AppConstants.primaryColor,
                                            width: 2.5),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white.withOpacity(0.8),
                                      errorStyle: errorMsg,
                                    ),
                                    onChanged: (value) {
                                      if (value.trim().isEmpty) {
                                        setLocationFlag = true;
                                      } else {
                                        setLocationFlag = false;
                                      }
                                      setState(() {});
                                    },
                                    validator: (value) {
                                      setLocationFlag = false;
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        setState(() {
                                          setLocationFlag = true;
                                        });
                                        return null;
                                      } else {
                                        setState(() {
                                          setLocationFlag = false;
                                        });
                                        return null;
                                      }
                                    },
                                  ),
                                  Positioned(
                                    top: 55,
                                    left: 0,
                                    right: 0,
                                    child: setLocationFlag
                                        ? Text(
                                            "*Enter location",
                                            style: errorMsg,
                                          )
                                        : const SizedBox.shrink(),
                                  ),
                                ],
                              ),

                              setLocationFlag
                                  ? const SizedBox(
                                      height: 50,
                                    )
                                  : const SizedBox(
                                      height: 25,
                                    ),
                              textWidget(
                                "Bio",
                              ),
                              const SizedBox(
                                height: 10,
                              ),

                              /// TEXTFORMFIELD FOR BIO

                              Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  TextFormField(
                                    controller: bioController,
                                    keyboardType: TextInputType.text,
                                    textCapitalization:
                                        TextCapitalization.sentences,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                    maxLines: null,
                                    maxLength: 150,
                                    decoration: InputDecoration(
                                      suffixIcon: NetworkAwareTap(
                                        onTap: () {},
                                        child: Image.asset(
                                          'assets/icons/Edit.png',
                                          filterQuality: FilterQuality.high,
                                          fit: BoxFit.cover,
                                          height: 38,
                                          width: 38,
                                        ),
                                      ),
                                      counterStyle: lbRegular.copyWith(
                                        fontSize: 14,
                                      ),
                                      contentPadding: const EdgeInsets.all(10),
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(5.0),
                                        borderSide: const BorderSide(
                                            color: AppConstants.primaryColor,
                                            width: 2.5),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white.withOpacity(0.8),
                                      errorStyle: errorMsg,
                                    ),
                                    onChanged: (value) {
                                      if (value.trim().isEmpty) {
                                        setBioFlag = true;
                                      } else {
                                        setBioFlag = false;
                                      }
                                      setState(() {});
                                    },
                                    validator: (value) {
                                      setBioFlag = false;
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        setState(() {
                                          setBioFlag = true;
                                        });
                                        return null;
                                      } else {
                                        setState(() {
                                          setBioFlag = false;
                                        });
                                        return null;
                                      }
                                    },
                                  ),
                                  Positioned(
                                    top: 55,
                                    left: 0,
                                    right: 0,
                                    child: setBioFlag
                                        ? Text(
                                            "*Enter bio",
                                            style: errorMsg,
                                          )
                                        : const SizedBox.shrink(),
                                  ),
                                ],
                              ),
                              setBioFlag
                                  ? const SizedBox(
                                      height: 50,
                                    )
                                  : const SizedBox(
                                      height: 25,
                                    ),
                              CustomLoaderButton(
                                // loginText: 'Login',
                                buttonWidth: isLoading
                                    ? 45.0
                                    : MediaQuery.of(context).size.width,
                                buttonRadius: 30.0,
                                buttonChild: isLoading
                                    ? const CircularProgressIndicator(
                                        valueColor: AlwaysStoppedAnimation(
                                            Colors.white),
                                        strokeWidth: 3.0,
                                      )
                                    : Text(
                                        'Next',
                                        style: lbBold.copyWith(
                                          fontSize: 18,
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                buttonPressed: _submitName,
                              ),
                              const SizedBox(height: 25),
                              const ContactUs(),
                            ],
                          ),
                        ),
                      ),
                    ],
                    if (_nameSet) ...[
                      Form(
                        key: _handleFormKey,
                        child: Padding(
                          padding: const EdgeInsets.all(19.0),
                          child: Column(
                            children: [
                              const SizedBox(
                                height: 25,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Text(
                                      "What is your @handle?: \n\nThis is a unique identifier meant to distinguish you from others with the  same name.\n\n Your @ handle is permanent so choose wisely!",
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              const SizedBox(
                                height: 25,
                              ),
                              Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  TextFormField(
                                    controller: handleController,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                    onChanged: (value) {
                                      log("Value HandleController : $value");
                                      if (value.trim().isNotEmpty) {
                                        checkHandler(value);
                                      } else {
                                        emptyHandle = true;
                                        _isHandlerAvailable = false;
                                      }
                                      setState(() {});
                                    },
                                    keyboardType: TextInputType.text,
                                    textCapitalization:
                                        TextCapitalization.sentences,
                                    maxLength: 15,
                                    decoration: InputDecoration(
                                      prefix: Text(
                                        "@",
                                        style: lbRegular.copyWith(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w400,
                                          color:
                                              const Color.fromRGBO(0, 0, 0, 1),
                                        ),
                                      ),
                                      counterStyle: lbRegular.copyWith(
                                        fontSize: 14,
                                        color: AppConstants.primaryColor,
                                      ),
                                      contentPadding: const EdgeInsets.all(10),
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(5.0),
                                        borderSide: const BorderSide(
                                          color: AppConstants.primaryColor,
                                          width: 2.5,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: AppConstants.primaryColor,
                                          width: 1.5,
                                        ),
                                      ),
                                      disabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: Colors.black38,
                                          width: 1.5,
                                        ),
                                      ),
                                      enabled: isFieldEnabled,

                                      filled: true,
                                      fillColor: Colors.white.withOpacity(0.8),
                                      errorStyle: errorMsg,
                                      // errorText: ,
                                    ),
                                    validator: (value) {
                                      if (value == null ||
                                          value.trim().isEmpty) {
                                        setState(() {
                                          handlerFlag = true;
                                        });
                                        return null;
                                      }
                                      return null;
                                    },
                                  ),
                                  Positioned(
                                    left: 0,
                                    right: 0,
                                    top: 55,
                                    child: handlerFlag
                                        ? Text(
                                            '*Enter handle',
                                            overflow: TextOverflow.clip,
                                            style: lbBold.copyWith(
                                              fontSize: 14,
                                              color: AppConstants.redColor,
                                            ),
                                          )
                                        : const SizedBox.shrink(),
                                  ),
                                  Positioned(
                                    left: 0,
                                    right: 0,
                                    top: 55,
                                    child: handleController.text
                                                .trim()
                                                .isNotEmpty &&
                                            !_isOnlySpaces()
                                        ? _buildCheckHandlerMessage()
                                        : const SizedBox.shrink(),
                                  ),
                                ],
                              ),
                              // Row(
                              //   mainAxisAlignment: MainAxisAlignment.end,
                              //   children: [
                              //     handlerFlag
                              //         ? Text(
                              //             "*Enter handle",
                              //             style: errorMsg,
                              //           )
                              //         : const SizedBox.shrink(),
                              //   ],
                              // ),

                              const SizedBox(height: 25),
                              CustomLoaderButton(
                                // loginText: 'Login',
                                buttonWidth: isLoading
                                    ? 45.0
                                    : MediaQuery.of(context).size.width,
                                buttonRadius: 30.0,
                                buttonChild: isLoading
                                    ? const CircularProgressIndicator(
                                        valueColor: AlwaysStoppedAnimation(
                                            Colors.white),
                                        strokeWidth: 3.0,
                                      )
                                    : Text(
                                        'Next',
                                        style: lbBold.copyWith(
                                          fontSize: 18,
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                buttonPressed: _submitHandle,
                              ),
                              const SizedBox(height: 25),
                              const ContactUs(),
                            ],
                          ),
                        ),
                      )
                    ]
                  ],
                ),
              ),
              NoConnectionTag(bottomPosition: 70),
            ],
          ),
        ),
      ),
    );
  }

  Widget textWidget(String title) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          "$title:",
          style: lbRegular.copyWith(
            fontSize: 18,
          ),
        ),
      ],
    );
  }

  Widget _buildCheckHandlerMessage() {
    return Column(
      children: _isHandlerAvailable
          ? [
              _buildHandlerMessage('Avaliable', _isHandlerAvailable,
                  handleController.text.isNotEmpty)
            ]
          : [
              _buildHandlerMessage('Not available', _isHandlerAvailable,
                  handleController.text.isNotEmpty)
            ],
    );
  }

  Widget _buildHandlerMessage(String message, bool isValid, bool shouldShow) {
    if (!shouldShow) {
      return const SizedBox.shrink();
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.error,
          size: 20,
          color: isValid ? Colors.green : Colors.red,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(message, style: lbItalic),
        ),
      ],
    );
  }

  bool _isOnlySpaces() {
    return handleController.text.trim().isEmpty;
  }
}
