import 'dart:developer';

import 'package:eljunto/controller/login_controller.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../constants/constants.dart';
import '../../../reusableWidgets/custom_text_widget.dart';
import '../../constants/common_helper.dart';
import '../../constants/text_style.dart';
import '../../controller/inapp_purchase_controller.dart';
import '../../reusableWidgets/custom_button.dart';
import '../../services/notification_service.dart';

class SetPasswordPage extends StatefulWidget {
  final String email;
  final String token;
  final String otp;
  final bool? isForgotPassword;

  const SetPasswordPage({
    super.key,
    required this.email,
    required this.token,
    required this.otp,
    this.isForgotPassword,
  });

  @override
  State createState() => _SetPasswordPageState();
}

class _SetPasswordPageState extends State<SetPasswordPage> {
  final _passwordFormKey = GlobalKey<FormState>();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _confirmPasswordFocusNode = FocusNode();

  bool _isPasswordValid = false;
  bool _isPasswordComplex = false;
  bool _isPasswordMatch = false;
  bool showPassword = true;
  bool showConfirmPassword = true;
  bool setPasswordFlag = false;
  bool confirmPasswordFlag = false;
  String? appName, appVersion;
  String errorMessage = '';
  bool isLoading = false;
  bool isFieldEnabled = true;
  NotificationServices notificationServices = NotificationServices();
  String? deviceId;

  @override
  void initState() {
    notificationServices.getDeviceToken().then((value) async {
      if (value.isNotEmpty) {
        fcmToken = value;
      } else {
        fcmToken = await notificationServices.getDeviceToken();
      }
    });
    log('FCM Token : $fcmToken');
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      deviceId = await CommonHelper.getDeviceName();
    });

    super.initState();
  }

  String? fcmToken;

  Future<void> getFcmToken() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    fcmToken = pref.getString('fcmToken');
    log("Local Database Token : $fcmToken");
  }

  void changePassword() {
    bool validation = _passwordFormKey.currentState!.validate();
    if (_passwordController.text.isEmpty &&
        _confirmPasswordController.text.isEmpty &&
        validation) {
      setState(() {
        setPasswordFlag = true;
        confirmPasswordFlag = true;
      });
    } else if (_passwordController.text.isEmpty && validation) {
      setState(() {
        setPasswordFlag = true;
      });
    } else if (_confirmPasswordController.text.isEmpty && validation) {
      setState(() {
        confirmPasswordFlag = true;
      });
    }
    isLoading = true;
    isFieldEnabled = false;
    errorMessage = '';
    setState(() {});
    final loginController =
        Provider.of<LoginController>(context, listen: false);
    if (validation) {
      if (_isPasswordValid && _isPasswordComplex && _isPasswordMatch) {
        loginController
            .reSetPassword(widget.email, widget.token, widget.otp,
                _passwordController.text, context)
            .then((value) {
          if (value) {
            if (mounted) {
              context.goNamed('login');
            }
            isFieldEnabled = true;
            isLoading = false;
            errorMessage = loginController.errorMessage;
            log('Error Message : $errorMessage');
            setState(() {});
          }
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } else {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _submitForm() async {
    bool validation = _passwordFormKey.currentState!.validate();
    if (_passwordController.text.isEmpty &&
        _confirmPasswordController.text.isEmpty &&
        validation) {
      setState(() {
        setPasswordFlag = true;
        confirmPasswordFlag = true;
      });
    }

    if (validation) {
      if (_isPasswordValid && _isPasswordComplex && _isPasswordMatch) {
        if (fcmToken == null) {
          fcmToken = await notificationServices.getDeviceToken();
          log('Getting FCM Token : $fcmToken');
          SharedPreferences pref = await SharedPreferences.getInstance();
          pref.setString('fcmToken', fcmToken!);
        } else {
          isLoading = true;
          isFieldEnabled = false;
          setState(() {});
          Provider.of<LoginController>(context, listen: false)
              .setPassword(
                  widget.email,
                  widget.token,
                  widget.otp,
                  _passwordController.text,
                  fcmToken ?? '',
                  deviceId ?? '',
                  context)
              .then((responseMap) async {
            if (responseMap["statusCode"] == 200) {
              if (mounted) {
                if (mounted) {
                  await Provider.of<SubscriptionController>(context,
                          listen: false)
                      .getSubscriptionDetails(context);
                }
                if (mounted) {
                  await Provider.of<InAppPurchaseController>(context,
                          listen: false)
                      .fetchProducts(context);
                }
                if (mounted) {
                  context.pushReplacementNamed(
                    'set-name-handle',
                    extra: {
                      'email': widget.email,
                      'token': widget.token,
                      'otp': widget.otp
                    },
                  );
                }
              }
              isFieldEnabled = true;

              isLoading = false;
            } else {
              setState(() {
                isFieldEnabled = true;
                isLoading = false;
                errorMessage = responseMap['error'];
              });
            }
          });
        }
      } else {}
    }
  }

  bool isPasswordComplex(String password) {
    final RegExp hasUppercase = RegExp(r'[A-Z]');
    final RegExp hasLowercase = RegExp(r'[a-z]');
    final RegExp hasDigit = RegExp(r'[0-9]');
    final RegExp hasSymbol = RegExp(r'[\W_]');

    int criteriaMet = 0;

    if (hasUppercase.hasMatch(password)) criteriaMet++;
    if (hasLowercase.hasMatch(password)) criteriaMet++;
    if (hasDigit.hasMatch(password)) criteriaMet++;
    if (hasSymbol.hasMatch(password)) criteriaMet++;

    return criteriaMet >= 3;
  }

  void _validatePassword(String password) {
    setState(() {
      setPasswordFlag = false;
      _isPasswordValid = password.length >= 8;
      /*  _isPasswordComplex =
          RegExp(r'(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[\W_]).{8,}')
              .hasMatch(password); */
      _isPasswordComplex = isPasswordComplex(password);
      _isPasswordMatch = password == _confirmPasswordController.text;
    });
  }

  void _validateConfirmPassword(String confirmPassword) {
    setState(() {
      confirmPasswordFlag = false;
      _isPasswordMatch = confirmPassword == _passwordController.text;
    });
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: PopScope(
          canPop: false,
          child: Stack(
            children: [
              Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  toolbarHeight: 140,
                  shape: const Border(
                    bottom: BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                  ),
                  automaticallyImplyLeading: false,
                  backgroundColor: AppConstants.textGreenColor,
                  centerTitle: true,
                  title: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppConstants.elJuntoLogo,
                        height: 100,
                        width: 80,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.contain,
                      ),
                      // SizedBox(height: 8),
                      const VersionDisplay(),
                      const SizedBox(height: 25),
                    ],
                  ),
                ),
                body: ListView(
                  children: <Widget>[
                    const SizedBox(
                      height: 25,
                    ),
                    Text(
                      widget.isForgotPassword ?? false
                          ? 'Reset Password'
                          : 'Create New Account',
                      style: lbRegular.copyWith(
                        fontSize: 24,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Form(
                      key: _passwordFormKey,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Column(
                          children: [
                            const SizedBox(height: 25),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  widget.isForgotPassword ?? false
                                      ? 'New password:'
                                      : "Set Password:",
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            SizedBox(
                              child: TextFormField(
                                controller: _passwordController,
                                focusNode: _passwordFocusNode,
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                ),
                                onChanged: _validatePassword,
                                decoration: InputDecoration(
                                  contentPadding: const EdgeInsets.all(10),
                                  //labelText: 'Password:',
                                  filled: true,
                                  fillColor: Colors.white.withOpacity(0.8),
                                  errorStyle: errorMsg,
                                  border: const OutlineInputBorder(),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: const BorderSide(
                                      color: AppConstants.primaryColor,
                                      width: 1.5,
                                    ),
                                  ),
                                  disabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: const BorderSide(
                                      color: Colors.black38,
                                      width: 1.5,
                                    ),
                                  ),
                                  suffixIcon: IconButton(
                                    icon: Icon(
                                      showPassword
                                          ? Icons.visibility_off
                                          : Icons.visibility,
                                      color: AppConstants.primaryColor,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        showPassword = !showPassword;
                                      });
                                    },
                                  ),
                                ),
                                enabled: isFieldEnabled,
                                obscureText: showPassword,
                                // validator: (value) {
                                //   if (value == null || value.isEmpty) {
                                //     return 'Please enter your password';
                                //   }
                                //   return null;
                                // },
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                setPasswordFlag
                                    ? Text(
                                        "*Enter password",
                                        style: errorMsg,
                                      )
                                    : const SizedBox.shrink(),
                              ],
                            ),
                            _buildPasswordValidationMessages(),
                            const SizedBox(height: 25),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Confirm Password:",
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            SizedBox(
                              child: TextFormField(
                                controller: _confirmPasswordController,
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                ),
                                onChanged: _validateConfirmPassword,
                                decoration: InputDecoration(
                                  contentPadding: const EdgeInsets.all(10),
                                  filled: true,
                                  fillColor: Colors.white.withOpacity(0.8),
                                  errorStyle: errorMsg,
                                  border: const OutlineInputBorder(),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: const BorderSide(
                                      color: AppConstants.primaryColor,
                                      width: 1.5,
                                    ),
                                  ),
                                  disabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: const BorderSide(
                                      color: Colors.black38,
                                      width: 1.5,
                                    ),
                                  ),
                                  suffixIcon: IconButton(
                                    icon: Icon(
                                      showConfirmPassword
                                          ? Icons.visibility_off
                                          : Icons.visibility,
                                      color: AppConstants.primaryColor,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        showConfirmPassword =
                                            !showConfirmPassword;
                                      });
                                    },
                                  ),
                                ),
                                enabled: isFieldEnabled,
                                obscureText: showConfirmPassword,
                                // validator: (value) {
                                //   if (value == null || value.isEmpty) {
                                //     return 'Please enter confirm password';
                                //   }
                                //   return null;
                                // },
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                confirmPasswordFlag
                                    ? Text(
                                        "*Confirm password",
                                        style: errorMsg,
                                      )
                                    : const SizedBox.shrink(),
                              ],
                            ),
                            _buildConfirmPasswordValidationMessage(),
                            errorMessage.isNotEmpty
                                ? Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          '* $errorMessage',
                                          style: lbRegular.copyWith(
                                            color: AppConstants.redColor,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                    ],
                                  )
                                : SizedBox.shrink(),
                            const SizedBox(height: 25),
                            // CustomButton(
                            //   text: 'Next',
                            //   onPressed: _submitForm,
                            // ),

                            CustomLoaderButton(
                              // loginText: 'Login',
                              buttonWidth: isLoading
                                  ? 45.0
                                  : MediaQuery.of(context).size.width,
                              buttonRadius: 30.0,
                              buttonChild: isLoading
                                  ? const CircularProgressIndicator(
                                      valueColor:
                                          AlwaysStoppedAnimation(Colors.white),
                                      strokeWidth: 3.0,
                                    )
                                  : Text(
                                      'Next',
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                              buttonPressed: widget.isForgotPassword ?? false
                                  ? changePassword
                                  : _submitForm,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 25),
                    const ContactUs(),
                  ],
                ),
              ),
              NoConnectionTag(bottomPosition: 70),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildValidationMessage(
      String message, bool isValid, bool shouldShow) {
    if (!shouldShow) {
      return const SizedBox.shrink();
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.error,
          color: isValid ? Colors.green : Colors.red,
          size: 18,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(message, style: lbItalic),
        ),
      ],
    );
  }

  Widget _buildPasswordValidationMessages() {
    return Column(
      children: [
        const SizedBox(
          height: 10,
        ),
        _buildValidationMessage(
          '8 Characters',
          _isPasswordValid,
          // _passwordController.text.isNotEmpty,
          true,
        ),
        const SizedBox(
          height: 10,
        ),
        _buildValidationMessage(
          '3 of the following: Capital letter, lowercase letter, number or symbol',
          _isPasswordComplex,
          // _passwordController.text.isNotEmpty,
          true,
        ),
      ],
    );
  }

  Widget _buildConfirmPasswordValidationMessage() {
    /* return Column(
      children: [_buildValidationMessage('Matches', _isPasswordMatch)],
    ); */
    /* return Column(
      children: _hasEnteredConfirmPassword
          ? [_buildValidationMessage('Matches', _isPasswordMatch, false)]
          : [],
    ); */
    return Column(
      children: _isPasswordMatch
          ? [
              const SizedBox(
                height: 10,
              ),
              _buildValidationMessage('Matches', _isPasswordMatch,
                  _confirmPasswordController.text.isNotEmpty)
            ]
          : [
              const SizedBox(
                height: 10,
              ),
              _buildValidationMessage('Password does not match',
                  _isPasswordMatch, _confirmPasswordController.text.isNotEmpty)
            ],
    );

    /* return Column(
      children: [
        _buildValidationMessage(
          'Matches',
          _isPasswordMatch,
          _confirmPasswordController.text.isNotEmpty,
        ),
      ],
    ); */
  }
}
