import 'dart:developer';

import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../constants/constants.dart';
import '../../controller/inapp_purchase_controller.dart';
import '../../reusableWidgets/custom_button.dart';
import '../../reusableWidgets/custom_unordered_list.dart';
import '../../reusableWidgets/subscription/subscription_list_ui.dart';

class ProfileSetupFinalStepPage extends StatefulWidget {
  const ProfileSetupFinalStepPage({super.key});
  @override
  State createState() => _ProfileSetupFinalStepPageState();
}

class _ProfileSetupFinalStepPageState extends State<ProfileSetupFinalStepPage> {
  bool _isFinalStepDone = false;
  InAppPurchaseController? inAppPurchaseController;

  @override
  void initState() {
    inAppPurchaseController =
        Provider.of<InAppPurchaseController>(context, listen: false);
    final purchaseUpdated =
        Provider.of<InAppPurchaseController>(context, listen: false)
            .inAppPurchase
            .purchaseStream;
    Provider.of<InAppPurchaseController>(context, listen: false)
        .streamSubscription = purchaseUpdated.listen((purchaseDetailsList) {
      log("In Purchase Stream: $purchaseDetailsList");
      if (mounted) {
        Provider.of<InAppPurchaseController>(context, listen: false)
            .handlePurchaseUpdates(purchaseDetailsList, context);
      }
    }, onDone: () {
      log("In Purchase Stream Done");
      Provider.of<InAppPurchaseController>(context, listen: false)
          .streamSubscription
          .cancel();
    }, onError: (error) {
      log(error.toString());
    });
    super.initState();
  }

  void _proceedToHomeScreen() {
    context.goNamed('Home');
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 140,
          shape: const Border(
            bottom: BorderSide(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
          ),
          automaticallyImplyLeading: false,
          backgroundColor: AppConstants.textGreenColor,
          centerTitle: true,
          title: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // const SizedBox(
              //   height: 20,
              // ),
              Image.asset(
                AppConstants.elJuntoLogo,
                height: 100,
                width: 80,
                filterQuality: FilterQuality.high,
                fit: BoxFit.contain,
              ),
              // SizedBox(height: 8),
              const VersionDisplay(),
              const SizedBox(height: 25),
            ],
          ),
        ),
        body: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                AppConstants.bgImagePath,
              ),
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
            ),
          ),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Column(
                  children: [
                    if (!_isFinalStepDone) ...[
                      const SizedBox(
                        height: 25,
                      ),
                      Text(
                        'This is the final step!',
                        style: lbRegular.copyWith(
                          fontSize: 24,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              recognizer: TapGestureRecognizer()..onTap = () {},
                              text: 'Click ',
                              style: lbRegular.copyWith(fontSize: 18),
                            ),
                            TextSpan(
                              text: 'below',
                              style: lbRegular.copyWith(
                                  color: AppConstants.primaryColor,
                                  // decoration: TextDecoration.underline,
                                  fontSize: 18),
                            ),
                            TextSpan(
                              text: ' to subscribe in your app store',
                              style: lbRegular.copyWith(fontSize: 18),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.left,
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Text(
                        "Your first 30 days are free. \n\nCancel anytime before the trial ends to avoid being charged.",
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Expanded(
                        child: Skeleton.replace(
                          replacement: SubscriptionListUI(
                            subProducts: inAppPurchaseController?.products,
                          ),
                          child: SubscriptionListUI(
                            subProducts: inAppPurchaseController?.products,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                    ] else if (_isFinalStepDone) ...[
                      const SizedBox(
                        height: 25,
                      ),
                      Text(
                        'You’re signed up!',
                        style: lbRegular.copyWith(
                          fontSize: 24,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 25),
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 0.0), // Add the desired left padding
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              'We suggest you:',
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 25),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 5.0),
                          child: ListView(
                            children: [
                              const UnorderedList(
                                [
                                  'Fill in your profile.',
                                  'Add books to your bookcase (books you have finished).',
                                  'Mark books as “Top Shelf” books (your top 20 Bookcase books).',
                                  'Find an existing book club and ask to join.',
                                  'Start your own book club and search for others to invite.  ',
                                ],
                              ),
                              CustomButton(
                                text: 'Procced To Home Screen',
                                onPressed: _proceedToHomeScreen,
                              ),
                              const SizedBox(
                                height: 25,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ]
                  ],
                ),
              ),
              NoConnectionTag(bottomPosition: 70),
            ],
          ),
        ),
      ),
    );
  }
}
