import 'dart:async';

import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/login_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';

import '../../../constants/constants.dart';
import '../../../reusableWidgets/custom_text_widget.dart';
import '../../reusableWidgets/custom_button.dart';

class OTPPage extends StatefulWidget {
  final String email;
  final String token;
  final bool? isForgotPassword;
  const OTPPage({
    super.key,
    required this.email,
    required this.token,
    this.isForgotPassword,
  });
  @override
  State createState() => _OTPPageState();
}

class _OTPPageState extends State<OTPPage> {
  TextEditingController otpController = TextEditingController();

  bool emailValidation = false;
  bool emailInvalid = false;
  String? currentToken;
  String errorMessage = '';
  bool isResendOtp = false;
  bool otpFlag = false;
  bool isLoading = false;
  bool isFieldEnabled = true;

  void forgotPasOtp() {
    String otp = otpController.text;

    isLoading = true;
    setState(() {});
    isFieldEnabled = false;

    Provider.of<LoginController>(context, listen: false)
        .verifyOTP(widget.email, currentToken!, otp, true, context)
        .then((responseMap) {
      if (responseMap["statusCode"] == 200) {
        context.goNamed(
          'set-password',
          extra: {
            'email': widget.email,
            'token': currentToken,
            'otp': otp,
            'isForgotPassword': true,
          },
        );
        isFieldEnabled = true;

        _start = 0;
        otpController.clear();
        isLoading = false;
      } else {
        setState(() {
          isLoading = false;
          otpFlag = true;
          errorMessage = responseMap['error'];
          //errorMessage = Provider.of<LoginController>(context, listen: false).errorMessage;
        });
      }
    });
  }

  void _verifyOtp() {
    String otp = otpController.text;
    isLoading = true;
    isFieldEnabled = false;
    Provider.of<LoginController>(context, listen: false)
        .verifyOTP(widget.email, currentToken!, otp, false, context)
        .then((responseMap) {
      if (responseMap["statusCode"] == 200) {
        context.pushNamed(
          'set-password',
          extra: {
            'email': widget.email,
            'token': currentToken,
            'otp': otp,
            'isForgotPassword': false,
          },
        );
        isFieldEnabled = true;

        _start = 0;
        otpController.clear();
        isLoading = false;
      } else {
        setState(() {
          isFieldEnabled = true;
          otpFlag = true;
          isLoading = false;
          errorMessage = responseMap['error'];
          //errorMessage = Provider.of<LoginController>(context, listen: false).errorMessage;
        });
      }
    });
  }

  final defaultPinTheme = PinTheme(
    width: 56,
    height: 56,
    textStyle: const TextStyle(
        fontSize: 20,
        color: Color.fromRGBO(30, 60, 87, 1),
        fontWeight: FontWeight.w600),
    decoration: BoxDecoration(
      border: Border.all(
        color: AppConstants.primaryColor,
      ),
      borderRadius: BorderRadius.circular(20),
    ),
  );

  int _start = 30;
  Timer? _timer;
  LoginController? loginController;

  @override
  void initState() {
    loginController = Provider.of<LoginController>(context, listen: false);
    super.initState();
    currentToken = widget.token;
    startTimer();
  }

  void startTimer() {
    _start = 600;
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(oneSec, (timer) {
      if (_start == AppConstants.otpExpiryTime) {
        setState(() {
          isResendOtp = true;
          _timer!.cancel();
        });
      } else {
        setState(() {
          isResendOtp = false;
          _start--;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      height: 45,
      width: 48.03,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: Colors.white, // Grey color for unfocused boxes
        border: Border.all(
          color: Colors.black38,
          width: 1,
        ),
      ),
    );

    final focusedPinTheme = PinTheme(
      height: 45,
      width: 48.03,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: Colors.white, // Grey color for unfocused boxes
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1,
        ),
      ),
    );

    final submittedPinTheme = PinTheme(
      height: 45,
      width: 48.03,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        color: Colors.white, // Grey color for unfocused boxes
        border: Border.all(
          color: Colors.black38,
          width: 1.5,
        ),
      ),
    );

    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                toolbarHeight: 140,
                shape: const Border(
                  bottom: BorderSide(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
                automaticallyImplyLeading: false,
                backgroundColor: AppConstants.textGreenColor,
                centerTitle: true,
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      AppConstants.elJuntoLogo,
                      height: 100, // Adjust height as needed
                      width: 80,
                      filterQuality: FilterQuality.high,
                      fit: BoxFit.contain, // Adjust width as needed
                      //fit: BoxFit.contain, // Maintain logo aspect ratio
                    ),
                    // const SizedBox(height: 8),
                    const VersionDisplay(),
                    const SizedBox(
                      height: 25,
                    ),
                  ],
                ),
              ),
              body: ListView(
                children: <Widget>[
                  const SizedBox(
                    height: 25,
                  ),
                  Text(
                    widget.isForgotPassword ?? false
                        ? 'Forgot Password'
                        : 'Create New Account',
                    style: lbRegular.copyWith(
                      fontSize: 24,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 25),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Enter verification code sent to email:',
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),

                  /// OTP BOX

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Pinput(
                      autofocus: true,
                      length: 6,
                      keyboardType: TextInputType.number,
                      showCursor: true,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      controller: otpController,
                      defaultPinTheme: defaultPinTheme,
                      focusedPinTheme: focusedPinTheme,
                      submittedPinTheme: submittedPinTheme,
                      enabled: isFieldEnabled,
                    ),
                  ),
                  const SizedBox(height: 10),
                  // if (_showOTPValidation)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        otpFlag
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Text(
                                    "*Code incorrect",
                                    style: errorMsg,
                                  ),
                                ],
                              )
                            : const SizedBox.shrink(),

                        // const Spacer(),
                        isResendOtp
                            ? NetworkAwareTap(
                                onTap: resendOTP,
                                child: Text(
                                  "Resend code",
                                  style: lbBold.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.primaryColor,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              )
                            : Row(
                                children: [
                                  NetworkAwareTap(
                                    onTap: () => isResendOtp ? resendOTP : null,
                                    child: Text(
                                      "Resend code in ",
                                      style: libreBaskervilleRegular,
                                    ),
                                  ),
                                  Text(
                                    '${(_start ~/ 60).toString().padLeft(2, '0')}:${(_start % 60).toString().padLeft(2, '0')}',
                                    style: lbRegular.copyWith(
                                      fontSize: 14,
                                      color: AppConstants.redColor,
                                    ),
                                  ),
                                  // Text(
                                  //   '- 10:00',
                                  //   style: lbRegular.copyWith(fontSize: 14),
                                  // ),
                                ],
                              ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 25),

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: CustomLoaderButton(
                      // loginText: 'Login',
                      buttonWidth:
                          isLoading ? 45.0 : MediaQuery.of(context).size.width,
                      buttonRadius: 30.0,
                      buttonChild: isLoading
                          ? const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation(Colors.white),
                              strokeWidth: 3.0,
                            )
                          : Text(
                              'Next',
                              style: lbBold.copyWith(
                                fontSize: 18,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                      buttonPressed: widget.isForgotPassword ?? false
                          ? forgotPasOtp
                          : _verifyOtp,
                    ),
                  ),
                  const SizedBox(height: 25),
                  const ContactUs(),
                ],
              ),
            ),
            NoConnectionTag(bottomPosition: 70),
          ],
        ),
      ),
    );
  }

  void resendOTP() {
    String responseMessage = '';
    Provider.of<LoginController>(context, listen: false)
        .verifyEmail(widget.email, context)
        .then((responseMap) {
      if (responseMap["statusCode"] == 200) {
        final newToken = responseMap['data']['token'];
        setState(() {
          currentToken = newToken;
          responseMessage = "A new code has been emailed";
          errorMessage = '';
        });
      } else {
        setState(() {
          responseMessage = responseMap['error'];
          //errorMessage = Provider.of<LoginController>(context, listen: false).errorMessage;
        });
      }

      showDialog(
        barrierColor: Colors.white60,
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            insetPadding: const EdgeInsets.all(25),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Column(
                children: [
                  NetworkAwareTap(
                    onTap: () {
                      context.pop();
                    },
                    child: Container(
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(
                        top: 10,
                      ),
                      child: Image.asset(
                        AppConstants.closePopupImagePath,
                        height: 30,
                        width: 30,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30.0),
                    child: Text(
                      responseMessage,
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          setState(() {
                            startTimer();
                            context.pop();
                          });
                        },
                        child: Container(
                          height: 45,
                          width: 120,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.textGreenColor),
                          child: Center(
                            child: Text(
                              "Ok",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                ],
              )
            ],
          );
        },
      );
    });
  }
}
