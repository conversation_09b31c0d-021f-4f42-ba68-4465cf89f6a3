import 'dart:async';
import 'dart:developer';

import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/login_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/contact_us.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../constants/constants.dart';
import '../../../reusableWidgets/custom_text_widget.dart';
import '../../reusableWidgets/custom_button.dart';
import '../../reusableWidgets/version_display.dart';

class SignUpPage extends StatefulWidget {
  final bool? isForgotPassword;

  const SignUpPage({
    super.key,
    this.isForgotPassword,
  });

  @override
  State createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  TextEditingController otpController = TextEditingController();
  bool _agreeToPolicy = false;
  bool _is18YearsOld = false;
  bool _showPolicyValidation = true;
  bool _showAgeValidation = true;
  bool emailValidation = false;
  bool emailInvalid = false;

  String errorMessage = '';
  bool isLoading = false;
  bool isFieldEnabled = true;
  bool emailAlreadyExist = false;
  bool isServerDown = false;

  LoginController? loginController;

  @override
  void initState() {
    loginController = Provider.of<LoginController>(context, listen: false);
    super.initState();
  }

  void forgotPassword() {
    bool validation = _formKey.currentState!.validate();
    if (_emailController.text.isEmpty && validation) {
      setState(() {
        emailValidation = true;
        emailInvalid = false;
        errorMessage = '*Enter email';
      });
    } else if (!EmailValidator.validate(_emailController.text) && validation) {
      setState(() {
        emailInvalid = true;
        emailValidation = false;
        errorMessage = 'Error';
      });
    } else {
      if (validation && _emailController.text.isNotEmpty) {
        isLoading = true;
        isFieldEnabled = false;
        errorMessage = '';
        setState(() {});
        Provider.of<LoginController>(context, listen: false)
            .forgotPassword(_emailController.text, context)
            .then((responseMap) {
          if (responseMap["statusCode"] == 200) {
            final token = responseMap['data']['token'];
            if (mounted) {
              context.pushNamed(
                'otp',
                extra: {
                  'email': _emailController.text,
                  'token': token,
                  'isForgotPassword': true,
                },
              );
            }
            isFieldEnabled = true;
            _emailController.clear();
            isLoading = false;
            setState(() {});
          } else {
            setState(() {
              // emailAlreadyExist = true;
              isFieldEnabled = true;
              isLoading = false;
              errorMessage = '*Account doesn’t exist';
              log("Email not exists : $errorMessage");
              //errorMessage = Provider.of<LoginController>(context, listen: false).errorMessage;
            });
          }
        });
      }
    }
  }

  void _submitForm() {
    bool validation = _formKey.currentState!.validate();
    setState(() {
      _showPolicyValidation = !_agreeToPolicy;
      _showAgeValidation = !_is18YearsOld;
    });
    if (_emailController.text.isEmpty && validation) {
      setState(() {
        emailValidation = true;
        emailInvalid = false;
        errorMessage = '*Enter email';
      });
    } else if (!EmailValidator.validate(_emailController.text) && validation) {
      setState(() {
        errorMessage = '*Email incorrect';
        emailInvalid = true;
        emailValidation = false;
      });
    } else {
      if (validation &&
          _agreeToPolicy &&
          _is18YearsOld &&
          _emailController.text.isNotEmpty) {
        String emailId = _emailController.text.toLowerCase();
        isLoading = true;
        isFieldEnabled = false;
        errorMessage = '';
        Provider.of<LoginController>(context, listen: false)
            .verifyEmail(emailId, context)
            .then((responseMap) async {
          log("Response : $responseMap");
          await saveLocally();
          if (responseMap["statusCode"] == 200) {
            final token = responseMap['data']['token'];
            if (mounted) {
              context.pushNamed(
                'otp',
                extra: {
                  'email': emailId,
                  'token': token,
                  "isForgotPassword": false,
                },
              );
            }
            isFieldEnabled = true;

            _emailController.clear();
            _agreeToPolicy = false;
            _is18YearsOld = false;
            _showAgeValidation = true;
            _showPolicyValidation = true;
            isLoading = false;
            setState(() {});
          } else if (responseMap["statusCode"] == 400) {
            emailAlreadyExist = true;
            errorMessage = '*This email is already registered';
            isLoading = false;
            isFieldEnabled = true;

            setState(() {});
          } else {
            setState(() {
              errorMessage = '*Something went wrong';
              isServerDown = true;
              isFieldEnabled = true;
              isLoading = false;
              // errorMessage = responseMap['error'];
              //errorMessage = Provider.of<LoginController>(context, listen: false).errorMessage;
            });
          }
        });
      } else {
        emailInvalid = false;
        emailValidation = false;
        errorMessage = '';
      }
    }
  }

  Future<void> saveLocally() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setString('userEmailId', _emailController.text);
  }

  String? appName, appVersion;

  @override
  void dispose() {
    _emailController.dispose();
    emailValidation = false;
    errorMessage = '';
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                toolbarHeight: 140,
                shape: const Border(
                  bottom: BorderSide(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
                automaticallyImplyLeading: false,
                backgroundColor: AppConstants.textGreenColor,
                centerTitle: true,
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      AppConstants.elJuntoLogo,
                      height: 100,
                      width: 80,
                      filterQuality: FilterQuality.high,
                      fit: BoxFit.contain,
                    ),
                    const VersionDisplay(),
                    const SizedBox(
                      height: 25,
                    ),
                  ],
                ),
              ),
              body: ListView(
                shrinkWrap: true,
                physics: const PageScrollPhysics(),
                children: [
                  const SizedBox(
                    height: 25,
                  ),
                  Text(
                    widget.isForgotPassword ?? false
                        ? 'Forgot Password'
                        : 'Create New Account',
                    style: lbRegular.copyWith(
                      fontSize: 24,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  widget.isForgotPassword ?? false
                      ? Text(
                          'Enter email linked with the account to receive a temporary password. ',
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                          textAlign: TextAlign.center,
                        )
                      : const SizedBox.shrink(),
                  const SizedBox(
                    height: 25,
                  ),
                  Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                "Email:",
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 10),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: TextFormField(
                            controller: _emailController,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                            ),
                            keyboardType: TextInputType.emailAddress,
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.all(10),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5.0),
                                borderSide: const BorderSide(
                                    color: AppConstants.primaryColor,
                                    width: 2.5),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                  color: AppConstants.primaryColor,
                                  width: 1.5,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                  color: Colors.black38,
                                  width: 1.5,
                                ),
                              ),
                              filled: true,
                              fillColor: Colors.white.withOpacity(0.8),
                              errorStyle: errorMsg,
                            ),
                            enabled: isFieldEnabled,
                            onChanged: (value) {
                              setState(() {
                                emailValidation = false;
                                emailInvalid = false;
                                emailAlreadyExist = false;
                                errorMessage = '';
                              });
                            },
                          ),
                        ),
                        (emailInvalid ||
                                emailValidation ||
                                emailAlreadyExist ||
                                isServerDown ||
                                errorMessage.isNotEmpty)
                            ? const SizedBox(
                                height: 10,
                              )
                            : const SizedBox.shrink(),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Row(
                            children: [
                              const Spacer(),
                              errorMessage.isNotEmpty
                                  ? Text(
                                      errorMessage,
                                      style: errorMsg,
                                    )
                                  : const SizedBox.shrink(),
                            ],
                          ),
                        ),
                        widget.isForgotPassword ?? false
                            ? const SizedBox.shrink()
                            : const SizedBox(
                                height: 15,
                              ),
                        widget.isForgotPassword ?? false
                            ? const SizedBox.shrink()
                            : Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12.0),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Checkbox(
                                      value: _agreeToPolicy,
                                      activeColor: AppConstants.primaryColor,
                                      // Set the color when checkbox is checked
                                      checkColor: Colors.white,
                                      // Color of the checkmark
                                      onChanged: (bool? value) {
                                        setState(() {
                                          _agreeToPolicy = value ?? false;
                                          _showPolicyValidation =
                                              !_agreeToPolicy;
                                        });
                                      },
                                    ),
                                    Expanded(
                                      child: RichText(
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: 'I agree to El Junto ',
                                              style: lbRegular.copyWith(
                                                height: 2.3,
                                                fontSize: 16,
                                              ),
                                            ),
                                            TextSpan(
                                              recognizer: TapGestureRecognizer()
                                                ..onTap = () async =>
                                                    await launchUrl(AppConstants
                                                        .privacyPolicyUrl),
                                              text: 'Privacy Policy',
                                              style: lbRegular.copyWith(
                                                color: AppConstants.blueColor,
                                                fontSize: 16,
                                              ),
                                            ),
                                            TextSpan(
                                              text: ' and ',
                                              style: lbRegular.copyWith(
                                                fontSize: 16,
                                              ),
                                            ),
                                            TextSpan(
                                              text: ' Terms of Service ',
                                              recognizer: TapGestureRecognizer()
                                                ..onTap = () async =>
                                                    await launchUrl(AppConstants
                                                        .termsAndConditionUrl),
                                              style: lbRegular.copyWith(
                                                color: AppConstants.blueColor,
                                                fontSize: 16,
                                              ),
                                            ),
                                            _showPolicyValidation
                                                ? TextSpan(
                                                    text: '*',
                                                    style: lbBold.copyWith(
                                                      fontSize: 14,
                                                      color:
                                                          AppConstants.redColor,
                                                    ),
                                                  )
                                                : const TextSpan(),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                        widget.isForgotPassword ?? false
                            ? const SizedBox.shrink()
                            : Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12.0),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Checkbox(
                                      value: _is18YearsOld,
                                      activeColor: AppConstants.primaryColor,
                                      checkColor: Colors.white,
                                      onChanged: (bool? value) {
                                        setState(() {
                                          _is18YearsOld = value ?? false;
                                          _showAgeValidation = !_is18YearsOld;
                                        });
                                      },
                                    ),
                                    Flexible(
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'I am 18 or older ',
                                            style: lbRegular.copyWith(
                                              fontSize: 16,
                                              height: 3,
                                            ),
                                          ),
                                          _showAgeValidation
                                              ? Text(
                                                  "*",
                                                  style: lbBold.copyWith(
                                                    fontSize: 14,
                                                    height: 3.4,
                                                    color:
                                                        AppConstants.redColor,
                                                  ),
                                                )
                                              : const SizedBox.shrink(),
                                          // if (_showAgeValidation)
                                          //   Text(
                                          //     'Please confirm you are 18 years or older',
                                          //     style: errorMsg,
                                          //   ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                        const SizedBox(
                          height: 25,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: CustomLoaderButton(
                            // loginText: 'Login',
                            buttonWidth: isLoading
                                ? 45.0
                                : MediaQuery.of(context).size.width,
                            buttonRadius: 30.0,
                            buttonChild: isLoading
                                ? const CircularProgressIndicator(
                                    valueColor:
                                        AlwaysStoppedAnimation(Colors.white),
                                    strokeWidth: 3.0,
                                  )
                                : Text(
                                    widget.isForgotPassword ?? false
                                        ? 'Submit'
                                        : 'Next',
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                            buttonPressed: widget.isForgotPassword ?? false
                                ? forgotPassword
                                : _submitForm,
                          ),
                        ),
                        widget.isForgotPassword ?? false
                            ? NetworkAwareTap(
                                onTap: () {
                                  context.pop();
                                },
                                child: Container(
                                  margin: const EdgeInsets.only(
                                      left: 20, right: 20, top: 25),
                                  height: 45,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(49),
                                    border: Border.all(
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      "Cancel",
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                        const SizedBox(height: 25),
                        ContactUs(),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10),
                ],
              ),
            ),
            NoConnectionTag(bottomPosition: 70),
          ],
        ),
      ),
    );
  }
}
