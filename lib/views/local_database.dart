import 'dart:developer';

import 'package:dash_chat_2/dash_chat_2.dart';
import 'package:eljunto/constants/app_config.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  static const _databaseName = "prysom.db";
  static const _databaseNameDev = "prysom_dev.db";
  static const _databaseVersion = 2;

  static const messageTable = 'messages';
  static const metaTableName = 'prysom_metadata';
  static const userClubTable = 'userClubTable';
  static const seenByTable = 'seenBy';
  static const seenStatus = 'seenStatus';

  static const columnId = '_id';
  static const columnText = 'text';
  static const columnBookClubId = 'club_Id';
  static const columnUserId = 'user_id';
  static const columnUserName = 'user_name';
  static const columnUserProfile = 'userProfile';
  static const columnCreatedAt = 'createdAt';

  static const metacolumnClubId = 'clubId';
  static const metacolumnBookClubName = 'bookClubName';
  static const metaColumnCreatedAt = 'createdAt';

  static const userClubIdColumn = 'userClubId';
  static const userClubNameColumn = 'userClubName';
  static const userlogginIdColumn = 'userLoginId';
  static const userCreatedAtColumn = 'createdAt';

  static const seenByUserIdColumn = 'seenByUserId';
  static const seenByMessageIdColumn = 'seenByMessageId';
  static const isSeenColumn = 'isSeen';

  static const userId = 'userId';
  static const clubId = 'clubId';
  static const isSeen = 'isSeen ';

  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  String? databaseName;
  Future<Database> _initDatabase() async {
    databaseName = AppConfig.shared.flavor == Flavor.dev
        ? _databaseNameDev
        : _databaseName;
    String path = join(await getDatabasesPath(), databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $messageTable (
        $columnId INTEGER PRIMARY KEY AUTOINCREMENT,
        $columnText TEXT NOT NULL,
        $columnBookClubId INTEGER NOT NULL,
        $columnUserId TEXT NOT NULL,
        $columnUserName TEXT NOT NULL,
        $columnUserProfile TEXT NOT NULL,
        $columnCreatedAt TEXT NOT NULL
      )
    ''');

    await db.execute('''
    CREATE TABLE $seenByTable (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      $seenByUserIdColumn TEXT NOT NULL,
      $seenByMessageIdColumn TEXT NOT NULL,
      $isSeenColumn INTEGER NOT NULL,
      FOREIGN KEY ($seenByMessageIdColumn) REFERENCES $messageTable (id) ON DELETE CASCADE
    )
  ''');

    await db.execute('''
    CREATE TABLE $metaTableName (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      $metacolumnClubId INTEGER NOT NULL UNIQUE,
      $metacolumnBookClubName TEXT NOT NULL,
      $metaColumnCreatedAt TEXT NOT NULL
    )
  ''');

    await db.execute('''
    CREATE TABLE $userClubTable (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      $userlogginIdColumn INTEGER NOT NULL,
      $userClubIdColumn INTEGER NOT NULL UNIQUE,
      $userClubNameColumn TEXT NOT NULL
    )
  ''');

    await db.execute('''
    CREATE TABLE $seenStatus (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      $userId TEXT NOT NULL,
      $clubId TEXT NOT NULL,
      $isSeen INTEGER NOT NULL
    )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await db.execute('''
      CREATE TABLE IF NOT EXISTS $seenStatus (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        $userId TEXT NOT NULL,
        $clubId TEXT NOT NULL,
        $isSeen INTEGER NOT NULL
      )
    ''');
    }
  }

  Future<void> close() async {
    final db = await instance.database;
    await db.close();
    _database = null;
  }

  Future<void> clearEntireDatabase() async {
    await close();
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, databaseName);
    await deleteDatabase(path);
    log("Clear local db");
    _database = await _initDatabase();
  }

  Future<void> batchInsertMessages(
      List<ChatMessage> messages, int clubId) async {
    if (messages.isEmpty) return;
    final db = await database;
    await db.transaction((txn) async {
      final batch = txn.batch();
      for (var message in messages) {
        batch.insert(
          messageTable,
          {
            columnText: message.text,
            columnBookClubId: clubId,
            columnUserId: message.user.id,
            columnUserName: message.user.firstName ?? '',
            columnUserProfile: message.user.profileImage ?? '',
            columnCreatedAt: message.createdAt.toIso8601String(),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
      await batch.commit(noResult: true);
    });
  }

  Future<List<ChatMessage>> getMessagesByBookClub(
    String clubId, {
    String orderBy = 'DESC',
    int limit = 20,
    int offset = 0,
  }) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        messageTable,
        where: '$columnBookClubId = ?',
        whereArgs: [clubId],
        orderBy: '$columnCreatedAt $orderBy',
        limit: limit,
        offset: offset,
      );
      return maps.map((map) {
        return ChatMessage(
          text: map[columnText] ?? '',
          user: ChatUser(
            id: map[columnUserId] ?? '0',
            firstName: map[columnUserName],
            profileImage: map[columnUserProfile],
          ),
          createdAt: DateTime.parse(map[columnCreatedAt]),
        );
      }).toList();
    } catch (e) {
      log('Error retrieving messages: $e');
      return [];
    }
  }

  Future<void> insertOrUpdateSeenStatus(
      int userId, String clubId, bool isSeen) async {
    final db = await database;
    try {
      var existing = await db.query(
        seenStatus,
        where: 'userId = ? AND clubId = ?',
        whereArgs: [userId, clubId],
      );
      if (existing.isNotEmpty) {
        int currentSeen = existing.first['isSeen'] as int;
        if ((isSeen ? 1 : 0) != currentSeen) {
          await db.update(
            seenStatus,
            {'isSeen': isSeen ? 1 : 0},
            where: 'userId = ? AND clubId = ?',
            whereArgs: [userId, clubId],
          );
        }
      } else {
        await db.insert(
          seenStatus,
          {
            'userId': userId,
            'clubId': clubId,
            'isSeen': isSeen ? 1 : 0,
          },
        );
      }
    } catch (e) {
      log('Error updating seen status: $e');
    }
  }

  Future<void> ensureDatabaseOpen() async {
    try {
      final db = await database;
      // Simple query to check if database is open
      await db.rawQuery('SELECT 1');
    } catch (e) {
      if (e.toString().contains('database_closed')) {
        // Force reinitialization
        _database = null;
        await database;
      }
    }
  }

  Future<bool> getSeenStatus(int userId, String clubId) async {
    await ensureDatabaseOpen();
    final db = await instance.database;
    final result = await db.query(
      seenStatus,
      columns: ['isSeen'],
      where: 'clubId = ? AND userId = ?',
      whereArgs: [clubId, userId],
      limit: 1,
    );
    if (result.isNotEmpty) {
      int isSeenValue = result.first['isSeen'] as int;
      return isSeenValue == 1;
    }
    return false;
  }

  Future<void> deleteSeenStatus(int userId, String clubId) async {
    final db = await instance.database;
    await db.delete(
      seenStatus,
      where: 'userId = ? AND clubId = ?',
      whereArgs: [userId, clubId],
    );
  }

  Future<void> insertLocallyMessage(ChatMessage message, int clubId) async {
    Database db = await DatabaseHelper.instance.database;
    final existingMessages = await db.query(
      messageTable,
      where: '$columnText = ? AND $columnUserId = ? AND $columnCreatedAt = ?',
      whereArgs: [
        message.text,
        message.user.id,
        message.createdAt.toIso8601String(),
      ],
    );
    if (existingMessages.isEmpty) {
      final messageId = await db.insert(
        messageTable,
        {
          columnText: message.text,
          columnBookClubId: clubId,
          columnUserId: message.user.id,
          columnUserName: message.user.firstName ?? '',
          columnUserProfile: message.user.profileImage ?? '',
          columnCreatedAt: message.createdAt.toIso8601String(),
        },
      );
      final seenByList = message.customProperties?['seenBy'] as List<dynamic>?;
      if (seenByList != null) {
        for (var entry in seenByList) {
          await db.insert(
            seenByTable,
            {
              seenByMessageIdColumn: messageId,
              seenByUserIdColumn: entry['user_id'],
              isSeenColumn: entry['isSeen'] ? 1 : 0,
            },
          );
        }
      }
    } else {
      final messageId = existingMessages.first[columnId];
      final seenByList = message.customProperties?['seenBy'] as List<dynamic>?;
      if (seenByList != null) {
        for (var entry in seenByList) {
          final existingSeenBy = await db.query(
            seenByTable,
            where: '$seenByMessageIdColumn = ? AND $seenByUserIdColumn = ?',
            whereArgs: [messageId, entry['user_id']],
          );
          if (existingSeenBy.isNotEmpty) {
            await db.update(
              seenByTable,
              {
                isSeenColumn: entry['isSeen'] ? 1 : 0,
              },
              where: '$seenByMessageIdColumn = ? AND $seenByUserIdColumn = ?',
              whereArgs: [messageId, entry['user_id']],
            );
          } else {
            await db.insert(
              seenByTable,
              {
                seenByMessageIdColumn: messageId,
                seenByUserIdColumn: entry['user_id'],
                isSeenColumn: entry['isSeen'] ? 1 : 0,
              },
            );
          }
        }
      }
    }
  }

  Future<int> insertMessage(ChatMessage message, int clubId) async {
    Database db = await database;
    final existingMessages = await db.query(
      messageTable,
      where: '$columnText = ? AND $columnUserId = ? AND $columnCreatedAt = ?',
      whereArgs: [
        message.text,
        message.user.id,
        message.createdAt.toIso8601String(),
      ],
    );
    if (existingMessages.isNotEmpty) {
      return 0;
    }
    final messageId = await db.insert(
      messageTable,
      {
        columnText: message.text,
        columnBookClubId: clubId,
        columnUserId: message.user.id,
        columnUserName: message.user.firstName ?? '',
        columnUserProfile: message.user.profileImage ?? '',
        columnCreatedAt: message.createdAt.toIso8601String(),
      },
    );
    final seenByList = message.customProperties?['seenBy'] as List<dynamic>?;
    if (seenByList != null) {
      for (var entry in seenByList) {
        await db.insert(
          seenByTable,
          {
            seenByMessageIdColumn: messageId,
            seenByUserIdColumn: entry['user_id'],
            isSeenColumn: entry['isSeen'] ? 1 : 0,
          },
        );
      }
    }
    return messageId;
  }

  Future<void> insertOrUpdateClubMetadata(
      int clubId, DateTime createdAt, String bookClubName) async {
    final db = await database;
    try {
      final existingMetadata = await db.query(
        metaTableName,
        where: '$metacolumnClubId = ?',
        whereArgs: [clubId],
      );
      if (existingMetadata.isNotEmpty) {
        final existingCreatedAt = DateTime.parse(
            existingMetadata.first[metaColumnCreatedAt] as String);
        final existingBookClubName =
            existingMetadata.first[metacolumnBookClubName] as String;
        if (existingCreatedAt != createdAt ||
            existingBookClubName != bookClubName) {
          await db.update(
            metaTableName,
            {
              metacolumnClubId: clubId,
              metacolumnBookClubName: bookClubName,
              metaColumnCreatedAt: createdAt.toIso8601String(),
            },
            where: '$metacolumnClubId = ?',
            whereArgs: [clubId],
          );
        }
      } else {
        await db.insert(
          metaTableName,
          {
            metacolumnClubId: clubId,
            metacolumnBookClubName: bookClubName,
            metaColumnCreatedAt: createdAt.toIso8601String(),
          },
        );
      }
    } catch (e) {
      log("Error in insertOrUpdateClubMetadata: $e");
    }
  }

  Future<void> insertUserClub(
    String clubId,
    String clubName,
    int userId,
  ) async {
    Database localDB = await database;
    final existingUserClub = await localDB.query(
      userClubTable,
      where: '$userClubIdColumn = ? OR $userClubNameColumn = ?',
      whereArgs: [clubId],
    );
    if (existingUserClub.isNotEmpty) {
      return;
    }
    await localDB.insert(
      userClubTable,
      {
        userClubIdColumn: clubId,
        userClubNameColumn: clubName,
        userlogginIdColumn: userId,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<ChatMessage?> getLatestMessage() async {
    Database db = await database;
    final result = await db.query(
      messageTable,
      orderBy: '$columnCreatedAt DESC',
      limit: 1,
    );
    if (result.isNotEmpty) {
      final row = result.first;
      return ChatMessage(
        text: row[columnText] as String,
        user: ChatUser(
          id: row[columnUserId] as String,
          firstName: row[columnUserName] as String,
        ),
        createdAt: DateTime.parse(row[columnCreatedAt] as String),
      );
    }
    return null;
  }

  Future<Map<String, dynamic>?> getClubMetadata(int clubId) async {
    final db = await database;
    final results = await db.query(
      'prysom_metadata',
      where: 'clubId = ?',
      whereArgs: [clubId],
    );
    if (results.isNotEmpty) {
      return results.first;
    }
    return null;
  }

  Future<List<ChatMessage>> fetchMessagesWithSeen(String bookClubId,
      {String orderBy = 'DESC'}) async {
    Database db = await database;
    final List<Map<String, dynamic>> messages = await db.query(
      messageTable,
      where: '$columnBookClubId = ?',
      whereArgs: [bookClubId],
      orderBy: '$columnCreatedAt $orderBy',
    );
    List<ChatMessage> chatMessages = [];
    for (var message in messages) {
      final seenByRecords = await db.query(
        seenByTable,
        where: '$seenByMessageIdColumn = ?',
        whereArgs: [message[columnId]],
      );
      List<Map<String, dynamic>> seenByList = seenByRecords.map((record) {
        return {
          'user_id': record[seenByUserIdColumn],
          'isSeen': record[isSeenColumn] == 1,
        };
      }).toList();
      chatMessages.add(
        ChatMessage(
          text: message[columnText] as String,
          user: ChatUser(
            id: message[columnUserId] as String,
            firstName: message[columnUserName] as String,
            profileImage: message[columnUserProfile] as String,
          ),
          createdAt: DateTime.parse(message[columnCreatedAt] as String),
          customProperties: {'seenBy': seenByList},
        ),
      );
    }
    return chatMessages;
  }

  Future<List<Map<String, dynamic>>> getUserClubData(int userId) async {
    Database localDB = await database;
    final List<Map<String, dynamic>> userClubList = await localDB.query(
      userClubTable,
    );
    return userClubList;
  }
}
