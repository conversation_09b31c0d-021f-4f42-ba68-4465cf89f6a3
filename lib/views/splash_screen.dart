import 'dart:developer';
import 'dart:io';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/controller/app_version_controller.dart';
import 'package:eljunto/controller/connectivity_controller.dart';
import 'package:eljunto/controller/user_credential_controller.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:eljunto/services/deep_link_service.dart';
import 'package:eljunto/services/notification_service.dart';
import 'package:eljunto/services/setup_locator.dart';
import 'package:eljunto/views/routing.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:provider/provider.dart';

import '../constants/app_config.dart';
import '../controller/subscription_controller.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late AppVersionProvider versionProvider;
  late UserCredentialController userCredentialController;
  late SubscriptionController subscriptionController;
  bool isNavigating = false;
  String? pendingDeepLink;
  final provider = locator<ConnectivityProvider>();

  @override
  void initState() {
    versionProvider = Provider.of<AppVersionProvider>(context, listen: false);
    userCredentialController =
        Provider.of<UserCredentialController>(context, listen: false);

    subscriptionController =
        Provider.of<SubscriptionController>(context, listen: false);

    provider.statusStream.listen((status) {
      log("Connection listener : $status");
      if (mounted) {
        if (status == InternetStatus.connected) {
          NotificationServices().checkLoginStatusAndInit(context);

          initializeApp();
        } else {
          context.go('/full-no-network');
        }
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> initializeApp() async {
    if (Platform.isAndroid) {
      try {
        // For tracking app lifecycle to distinguish background vs fresh start
        final isBackgroundResume = AppRouter.appInitialized;

        // Get pending deep link but don't clear it yet - we need to be careful about when we clear
        String? pendingIntentDeepLink =
            await AppRouter.getPendingRouteWithoutClearing();

        // Store if we received this deep link through a notification click
        bool isFromNotificationClick = false;

        // Check if we have a deep link from notification that just arrived
        if (AppRouter.lastClickedDeepLink != null) {
          pendingIntentDeepLink = AppRouter.lastClickedDeepLink;
          AppRouter.lastClickedDeepLink = null; // Clear this flag
          isFromNotificationClick = true;
        }

        // Perform parallel initialization tasks
        final versionFuture = versionProvider.refreshVersionInfo();
        final userFuture = userCredentialController.setUser();

        // Wait for critical paths
        await Future.wait([versionFuture, userFuture]);

        // Check login status
        final isLoggedIn = await userCredentialController.isUserLoggedIn();
        final userName = userCredentialController.userCredential.userName;
        final userEmailId = userCredentialController.userCredential.userEmailId;
        log("UserName : $userName , UserEmailId : $userEmailId");

        // Check subscription status only if logged in
        bool isSubscriptionActive = false;
        if (isLoggedIn) {
          await subscriptionController.isActiveSubscription();
          isSubscriptionActive = subscriptionController
                      .verifySubscriptionModel?.data?.usubStatus ==
                  'ACTIVE' ||
              subscriptionController
                      .verifySubscriptionModel?.data?.usubStatus ==
                  'CANCELLED';
          debugPrint(
              "Is Active Subscription: ${isSubscriptionActive ? 'Yes' : 'No'}");
        }

        // Process system deep links only if no redirect pending
        Uri? systemDeepLink;
        if (pendingIntentDeepLink == null) {
          systemDeepLink = await DeepLinkService().consumePendingDeepLink();
          if (systemDeepLink != null) {
            pendingIntentDeepLink = _parseRouteFromDeepLink(systemDeepLink);
          }
        }

        // Ensure minimum splash time
        await Future.delayed(const Duration(milliseconds: 1250));

        // Mark as initialized BEFORE navigation
        AppRouter.appInitialized = true;

        if (!mounted) return;

        if (isBackgroundResume && !isFromNotificationClick) {
          // For background resume without notification click, CLEAR any pending links
          // to prevent unintended navigation
          await AppRouter.clearPendingRoute();

          if (isLoggedIn && userName != null) {
            // App is already running, go straight to home (don't process old links)
            if (mounted) {
              context.go('/Home');
              return;
            }
          }
          // Otherwise continue to login flow below
        }

        // Navigate based on conditions
        if (mounted) {
          if (!isLoggedIn) {
            context.go('/login');
          } else if (userName == null) {
            context.go(
              '/set-name-handle',
              extra: {'email': userEmailId},
            );
          } else if (!isSubscriptionActive) {
            // Force subscription screen even if deep link exists
            context.go('/subscription');
          } else if (pendingIntentDeepLink != null) {
            // Process valid deep link and then clear it to prevent reuse
            context.go(pendingIntentDeepLink);
            await AppRouter.clearPendingRoute();
          } else {
            context.go('/Home');
          }
        }
      } catch (e) {
        debugPrint("Error during initialization: $e");
        AppRouter.appInitialized = true; // Mark as initialized even on error
        await Future.delayed(const Duration(milliseconds: 500));

        if (mounted) {
          context.go('/login'); // Safe fallback
        }
      }
    } else {
      /// IOS ONLY
      try {
        // For tracking app lifecycle to distinguish background vs fresh start
        final isBackgroundResume = AppRouter.appInitialized;

        // Get pending deep link but don't clear it yet - we need to be careful about when we clear
        String? pendingIntentDeepLink =
            await AppRouter.getPendingRouteWithoutClearing();

        // Store if we received this deep link through a notification click
        bool isFromNotificationClick = false;

        // Check if we have a deep link from notification that just arrived
        if (AppRouter.lastClickedDeepLink != null) {
          pendingIntentDeepLink = AppRouter.lastClickedDeepLink;
          AppRouter.lastClickedDeepLink = null; // Clear this flag
          isFromNotificationClick = true;
        }

        // Perform parallel initialization tasks
        final versionFuture = versionProvider.refreshVersionInfo();
        final userFuture = userCredentialController.setUser();

        // Wait for critical paths
        await Future.wait([versionFuture, userFuture]);

        // Check login status
        final isLoggedIn = await userCredentialController.isUserLoggedIn();
        final userName = userCredentialController.userCredential.userName;
        final userEmailId = userCredentialController.userCredential.userEmailId;
        log("UserName : $userName , UserEmailId : $userEmailId");

        // Check subscription status only if logged in
        bool isSubscriptionActive = false;
        if (isLoggedIn) {
          await subscriptionController.isActiveSubscription();
          isSubscriptionActive = subscriptionController
                      .verifySubscriptionModel?.data?.usubStatus ==
                  'ACTIVE' ||
              subscriptionController
                      .verifySubscriptionModel?.data?.usubStatus ==
                  'CANCELLED';
          debugPrint(
              "Is Active Subscription: ${isSubscriptionActive ? 'Yes' : 'No'}");
        }

        // Process system deep links only if no redirect pending
        Uri? systemDeepLink;
        if (pendingIntentDeepLink == null) {
          systemDeepLink = await DeepLinkService().consumePendingDeepLink();
          if (systemDeepLink != null) {
            pendingIntentDeepLink = _parseRouteFromDeepLink(systemDeepLink);
          }
        }

        // Ensure minimum splash time
        await Future.delayed(const Duration(milliseconds: 1250));

        // Mark as initialized BEFORE navigation
        AppRouter.appInitialized = true;

        if (!mounted) return;

        if (isBackgroundResume && !isFromNotificationClick) {
          // For background resume without notification click, CLEAR any pending links
          // to prevent unintended navigation
          await AppRouter.clearPendingRoute();

          if (isLoggedIn && userName != null) {
            // App is already running, go straight to home (don't process old links)
            if (mounted) {
              context.go('/Home');
              return;
            }
          }
          // Otherwise continue to login flow below
        }

        // Navigate based on conditions
        if (mounted) {
          if (!isLoggedIn) {
            context.go('/login');
          } else if (userName == null) {
            context.go(
              '/set-name-handle',
              extra: {'email': userEmailId},
            );
          } else if (!isSubscriptionActive) {
            // Force subscription screen even if deep link exists
            context.go('/subscription');
          } else if (pendingIntentDeepLink != null) {
            // Process valid deep link and then clear it to prevent reuse
            context.go(pendingIntentDeepLink);
            await AppRouter.clearPendingRoute();
          } else {
            context.go('/Home');
          }
        }
      } catch (e) {
        debugPrint("Error during initialization: $e");
        AppRouter.appInitialized = true; // Mark as initialized even on error
        await Future.delayed(const Duration(milliseconds: 500));

        if (mounted) {
          context.go('/login'); // Safe fallback
        }
      }
    }
  }

  String _parseRouteFromDeepLink(Uri deepLink) {
    // First check if user is authenticated
    final token = Provider.of<UserCredentialController>(context, listen: false)
        .userCredential
        .jwttoken;

    // If no valid token, store the intended destination but return login route
    if (token == null || token.isEmpty) {
      // Store the deep link for later use after login
      DeepLinkService().storePendingDeepLink(deepLink);
      return '/login';
    }

    String? clubId = deepLink.queryParameters['bookClubId'];
    String? userId = deepLink.queryParameters['userId'];

    // More robust route parsing
    if (deepLink.path.contains('ManageIncomeRequest')) {
      // Extract clubId if needed

      return '/Clubs/user-club-details/clubsScreen4/ManageIncomeRequest?bookClubId=${clubId ?? ''}&userId=${userId ?? ''}';
    } else if (deepLink.path.contains('user-club-details')) {
      return '/Clubs/user-club-details?bookClubId=${clubId ?? ''}&userId=${userId ?? ''}';
    } else if (deepLink.path.contains('club-invitations')) {
      return '/Clubs/club-invitations?userId=${userId ?? ''}';
    } else if (deepLink.path.contains('chat-screen')) {
      return '/chat-screen?userId=${userId ?? ''}&bookClubId=${clubId ?? ''}';
    }

    return deepLink.toString();
  }

  Future<bool> isSignInSuccess() async {
    final isUserLoggedIn =
        await Provider.of<UserCredentialController>(context, listen: false)
            .isUserLoggedIn();
    final name = Provider.of<UserCredentialController>(context, listen: false)
        .userCredential
        .userName;
    log("UserNAme : $name");
    // print("Token : $isUserLoggedIn");

    return isUserLoggedIn;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConfig.shared.flavor == Flavor.dev
          ? Colors.white
          : AppConstants.textGreenColor,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Image.asset(
            'assets/images/eljunto_logo_dark_teal.png',
            height: 100,
            width: 80,
            filterQuality: FilterQuality.high,
            fit: BoxFit.contain,
          ),
          const SizedBox(
            height: 10,
          ),
          const Align(
            alignment: Alignment.center,
            child: VersionDisplay(),
          ),
        ],
      ),
    );
  }
}
