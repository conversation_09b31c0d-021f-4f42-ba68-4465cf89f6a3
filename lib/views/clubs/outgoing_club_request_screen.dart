import 'dart:developer';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/common_helper.dart';
import '../../reusableWidgets/marquee_text.dart';

class OutGoingClubRequest extends StatefulWidget {
  // final int? userId;
  const OutGoingClubRequest({
    super.key,
    // this.userId,
  });

  @override
  State<OutGoingClubRequest> createState() => _OutGoingClubRequestState();
}

class _OutGoingClubRequestState extends State<OutGoingClubRequest> {
  bool isActive = false;
  TextEditingController requestController = TextEditingController();
  int? loggedinUserId;
  @override
  void initState() {
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    multipleFunctionCall();
    super.initState();
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();

    // if (userId != null) {
    //   loggedinUserId = userId;
    // } else {}
  }

  Future multipleFunctionCall() async {
    await _initializeUserId();
    await Future.wait([
      getOutGoingrequest(),
    ]);
  }

  BookClubController? bookClubController;
  List<RequestManage>? outgoingRequestList;
  bool isLoading = false;
  Future<void> getOutGoingrequest() async {
    isLoading = true;
    setState(() {});
    await Provider.of<BookClubController>(context, listen: false)
        .inComingRequest(
            loggedinUserId ?? 0,
            ClubMembershipStatus.pending,
            ClubMembershipStatus.reOpened,
            ClubRequestType.outgoingClubRequestByUserId,
            context)
        .then((value) {
      outgoingRequestList = bookClubController?.incomingOutGoingRequest?.data;
      isLoading = false;
      setState(() {});
    });
    log('outgoingRequestList: ${outgoingRequestList?.length}');
  }

  Future<void> rescindInvitation(int index, bool flag) async {
    String? status;

    if (flag) {
      status = ClubMembershipStatus.pending;
    } else {
      status = ClubMembershipStatus.reScind;
    }
    final payload = RequestManage(
      bookClubId: outgoingRequestList?[index].bookClubId,
      bookClubMemberId: outgoingRequestList?[index].bookClubMemberId,
      userId: outgoingRequestList?[index].userId,
      initiatedBy: outgoingRequestList?[index].initiatedBy,
      status: status,
      requestMessage: requestController.text,
      userType: ClubMemberType.member, // UNCOMMENT
    );

    await Provider.of<BookClubController>(context, listen: false)
        .updateInvitation(payload, context)
        .then((value) {
      setState(() {});
    });
  }

  bool checkStatus(int index) {
    if (outgoingRequestList?[index].status == 'REOPENED') {
      return true;
    } else {
      return false;
    }
  }

  @override
  void dispose() {
    requestController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: "Outgoing Club Requests",
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isLoading,
          child: Column(
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: NetworkAwareTap(
                  onTap: () {
                    context.pushNamed(
                      'new-club-opening',
                      // extra: {
                      //   'buttonName': 'Edit Profile',
                      //   "userModel": userModel,
                      //   'isCompleteProfile': true,
                      // },
                    );
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: AppConstants.textGreenColor,
                      borderRadius: BorderRadius.circular(90),
                    ),
                    child: Center(
                      child: Text(
                        "Find Clubs With Openings",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              outgoingRequestList?.isNotEmpty ?? false
                  ? Expanded(
                      child: Consumer<BookClubController>(
                          builder: (context, bookClubController, child) {
                        return ListView.builder(
                          padding: const EdgeInsets.only(
                            bottom: 25,
                          ),
                          itemCount: outgoingRequestList?.length,
                          itemBuilder: (context, index) {
                            return NetworkAwareTap(
                              onTap: () {
                                checkStatus(index)
                                    ? clubOpeningPopUp(
                                        outgoingRequestList?[index]
                                            .bookClubName,
                                        outgoingRequestList?[index].clubType,
                                        outgoingRequestList?[index]
                                            .requestMessage,
                                        index,
                                      )
                                    : context.pushNamed(
                                        'club-details',
                                        // queryParameters: {
                                        //   'bookClubId': clubList[index].clubId,
                                        // },
                                        extra: {
                                          'bookClubId':
                                              outgoingRequestList?[index]
                                                  .bookClubId,
                                          'bookClubName':
                                              outgoingRequestList?[index]
                                                  .bookClubName,
                                          'impromptuCount':
                                              outgoingRequestList?[index]
                                                  .clubCount,
                                        },
                                      );

                                //  context.pushNamed(
                                //     "user-club-details",
                                //     extra: {
                                //       'bookClubId':
                                //           outgoingRequestList?[index]
                                //               .bookClubId,
                                //       'bookClubName':
                                //           outgoingRequestList?[index]
                                //               .bookClubName,
                                //       'totalVacancies':
                                //           outgoingRequestList?[index]
                                //               .totalVacancies,
                                //       // 'leaderId': bookClubItem.userId,
                                //       // 'charter': pendingInvitations?[index].c,
                                //       // 'bookClubModel': bookClubItem,
                                //     },
                                //   );
                              },
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Skeleton.replace(
                                    replacement: clubCardSkeleton(index, false),
                                    child: clubCardSkeleton(index, true),
                                  ),
                                  checkStatus(index)
                                      ? Positioned(
                                          top: -8,
                                          right: 20,
                                          child: Image.asset(
                                            AppConstants.notificationImagePath,
                                            height: 15,
                                            width: 15,
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                ],
                              ),
                            );
                          },
                        );
                      }),
                    )
                  : Skeleton.replace(
                      replacement: Container(
                        padding: const EdgeInsets.only(left: 20),
                        margin: const EdgeInsets.symmetric(
                          horizontal: 20,
                        ),
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: AppConstants.skeletonBackgroundColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "No Outgoing Requests",
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ),
                      child: const NoDataWidget(
                        message: "No outgoing requests",
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  void cancelRequest(String? clubName, String? clubType, int index) {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        String? clubTypeName;
        if (outgoingRequestList?[index].clubType == "STANDING") {
          clubTypeName = 'Standing Book Club';
        } else {
          clubTypeName = clubType;
        }
        Text(
          'Standing Book Club',
          style: lbItalic.copyWith(
            fontSize: 12,
          ),
        );

        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Text(
                    "Cancel Request:",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30, right: 20),
                  child: Text(
                    'Are you sure you would like to cancel the request to join:',
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    clubName ?? '',
                    style: lbRegular.copyWith(
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    clubTypeName ?? '',
                    style: lbItalic.copyWith(
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          rescindInvitation(index, false).then(
                            (value) => confirmCancelRequest(),
                          );
                          // confirmCancelRequest();
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: Colors.transparent,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Go Back",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void confirmCancelRequest() {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Text(
                    "Cancel Request:",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30, right: 20),
                  child: Text(
                    'Your request has been cancelled.',
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                    getOutGoingrequest();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void clubOpeningPopUp(
      String? clubName, String? clubType, String? requestMessage, int index) {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        requestController.text = requestMessage ?? '';
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Center(
                          child: Text(
                            "Club Opening Available Again!",
                            textAlign: TextAlign.center,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      Center(
                        child: Text(
                          'Resend Request to Join',
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Text(
                        outgoingRequestList?[index].memberReqPrompt ?? '',
                        textAlign: TextAlign.start,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      TextFormField(
                        controller: requestController,
                        initialValue: requestMessage,
                        maxLines: 5,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                        ),
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.all(10),
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NetworkAwareTap(
                            onTap: () {
                              rescindInvitation(index, true).then((value) {
                                getOutGoingrequest();
                                if (context.mounted) {
                                  context.pop();
                                }
                              });
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text(
                                  "Resend",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          NetworkAwareTap(
                            onTap: () {
                              context.pop();
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: Colors.transparent,
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  "Go Back",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          cancelRequest(clubName, clubType, index);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: Colors.transparent,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Cancel Request",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                    ],
                  ),
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Widget clubCardSkeleton(int index, bool borderFlag) {
    return Container(
      margin: const EdgeInsets.only(left: 20, right: 20, bottom: 25),
      padding: const EdgeInsets.all(14),
      height: 127,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: borderFlag
            ? Colors.transparent
            : AppConstants.skeletonBackgroundColor,
        border: borderFlag
            ? Border.all(
                color: checkStatus(index)
                    ? AppConstants.isActiveRequestColor
                    : AppConstants.primaryColor,
                width: 1.5,
              )
            : Border.all(
                color: Colors.transparent,
              ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MarqueeList(
                      children: [
                        Text(
                          outgoingRequestList?[index].bookClubName ?? '',
                          overflow: TextOverflow.ellipsis,
                          style: lbBold.copyWith(
                            fontSize: 18,
                            color: checkStatus(index)
                                ? AppConstants.isActiveRequestColor
                                : AppConstants.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      outgoingRequestList?[index].bookAuthor ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 14,
                        color: checkStatus(index)
                            ? AppConstants.isActiveRequestColor
                            : AppConstants.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                width: 5,
              ),
              ClipRRect(
                borderRadius: BorderRadius.circular(49),
                child: Image.asset(
                  AppConstants.clubOpeningLogoImagePath,
                  height: 50,
                  width: 50,
                  filterQuality: FilterQuality.high,
                  fit: BoxFit.cover,
                  color: checkStatus(index)
                      ? AppConstants.isActiveRequestColor
                      : AppConstants.primaryColor,
                ),
              ),
            ],
          ),
          if (outgoingRequestList?[index].clubType == "STANDING") ...[
            Text(
              'Standing Book Club',
              style: lbItalic.copyWith(
                fontSize: 14,
                color: checkStatus(index)
                    ? AppConstants.isActiveRequestColor
                    : AppConstants.primaryColor,
              ),
            ),
          ] else ...[
            Text(
              outgoingRequestList?[index].clubCount ?? '',
              overflow: TextOverflow.ellipsis,
              style: lbItalic.copyWith(
                color: checkStatus(index)
                    ? AppConstants.isActiveRequestColor
                    : AppConstants.primaryColor,
                fontSize: 14,
              ),
            ),
          ],
          const SizedBox(
            height: 6,
          ),
          NetworkAwareTap(
            onTap: () {
              checkStatus(index)
                  ? const SizedBox.shrink()
                  : cancelRequest(
                      outgoingRequestList?[index].bookClubName,
                      outgoingRequestList?[index].clubCount,
                      index,
                    );
            },
            child: Text(
              'Cancel Request',
              style: lbItalic.copyWith(
                fontSize: 14,
                decoration: TextDecoration.underline,
                decorationColor: checkStatus(index)
                    ? AppConstants.isActiveRequestColor
                    : AppConstants.primaryColor,
                color: checkStatus(index)
                    ? AppConstants.isActiveRequestColor
                    : AppConstants.primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
