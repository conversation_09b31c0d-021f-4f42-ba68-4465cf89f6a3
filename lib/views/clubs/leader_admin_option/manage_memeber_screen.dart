import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/config.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/club_membership_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../constants/constants.dart';
import '../../../constants/text_style.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';

class ManageMemberScreen extends StatefulWidget {
  final String? buttonName;
  const ManageMemberScreen({
    super.key,
    this.buttonName,
  });

  @override
  State<ManageMemberScreen> createState() => _ManageMemberScreenState();
}

class _ManageMemberScreenState extends State<ManageMemberScreen> {
  BookClubModel? bookCaseModel;
  List<ClubMembershipModel>? memberList;
  String imageURL = Config.imageBaseUrl;
  int totalPosition = 0;
  int? loggedinUserId;
  int? bookclubIndex;

  @override
  void initState() {
    super.initState();
    _initializeUserId();
    roleController.text = 'Select any one';
    bookCaseModel =
        Provider.of<BookClubController>(context, listen: false).bookClubModel;
    // bookclubIndex =
    //     Provider.of<BookClubController>(context, listen: false).bookClubIndex;
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();
    getBookClubMembers();
  }

  bool isLoading = false;
  Future<void> getBookClubMembers() async {
    setState(() {
      isLoading = true;
    });
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubMembers(bookCaseModel?.bookClubId ?? 0, context)
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        log(responseMap["data"].toString());
        memberList = (responseMap["data"] as List)
            .map((item) => ClubMembershipModel.fromJson(item))
            .toList();
        totalPosition =
            ((memberList?.length ?? 0) + (memberList?[0].totalVacancies ?? 0));
        log("Total Position : $totalPosition");

        filterNonLeaderMembers();
      } else {}
    });
    setState(() {
      isLoading = false;
    });
  }

  List<ClubMembershipModel>? clubMemberList;
  void filterNonLeaderMembers() {
    if (memberList != null) {
      // Assuming the leader is the first member in the memberList
      clubMemberList = memberList?.sublist(1) // Skip the first member (leader)
          ;
    }
  }

  Future<void> removeMember(int index) async {
    final payload = RequestManage(
      bookClubId: bookCaseModel?.bookClubId,
      bookClubMemberId: memberList?[index].bookClubMemberId,
      userId: memberList?[index].userId,
      initiatedBy: bookCaseModel?.userId,
      status: ClubMembershipStatus.removed,
    );

    await Provider.of<BookClubController>(context, listen: false)
        .updateInvitation(payload, context)
        .then((value) async {
      confirmRemoveBox(index);
      await getBookClubMembers();
    });
  }

  bool isInProgress = false;
  Future<void> addRemoveOpening(bool removeAdd) async {
    isInProgress = true;
    await Provider.of<BookClubController>(context, listen: false)
        .addRemoveOpening(removeAdd, bookCaseModel?.bookClubId ?? 0, context)
        .then((value) async {
      isInProgress = false;
      getBookClubMembers();
      setState(() {});
    });
  }

  Future<void> assignLeader(
      int index, int? selectedUserId, int? selectedMemberId) async {
    final payload = RequestManage(
      bookClubId: bookCaseModel?.bookClubId,
      bookClubMemberId: selectedMemberId,
      userId: selectedUserId,
      userType: ClubMemberType.leader,
      initiatedBy: bookCaseModel?.userId,
      status: ClubMembershipStatus.active,
    );

    await Provider.of<BookClubController>(context, listen: false)
        .updateInvitation(payload, context)
        .then((value) async {
      await getBookClubMembers();
      confirmassignLeaderBox(index);
    });
  }

  TextEditingController roleController = TextEditingController();
  int? bookClubMemberId;

  @override
  void dispose() {
    roleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.buttonName,
            clubName: bookCaseModel?.bookClubName,
            clubNameFlag: true,
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isLoading,
          child: Padding(
            padding: const EdgeInsets.only(left: 20.0, right: 20, top: 25),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "No of openings (${memberList?[0].totalVacancies})",
                      style: lbItalic.copyWith(
                        fontSize: 16,
                      ),
                    ),
                    totalPosition == memberList?[0].totalPositions
                        ? const SizedBox.shrink()
                        : NetworkAwareTap(
                            onTap: () {
                              addRemoveOpening(true);
                            },
                            child: Text(
                              "Add opening",
                              style: lbItalic.copyWith(
                                fontSize: 16,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          )
                  ],
                ),
                Expanded(
                  child: Consumer<BookClubController>(
                      builder: (context, bookClubController, child) {
                    return ListView.builder(
                      scrollDirection: Axis.vertical,
                      padding:
                          const EdgeInsets.only(left: 0, right: 0, bottom: 25),
                      itemCount: totalPosition,
                      itemBuilder: (context, index) {
                        // If index is within the bounds of the member list, show member details
                        if (index < memberList!.length) {
                          final urlImage = imageURL +
                              (memberList?[index].userProfilePicture ?? '');
                          return Skeleton.replace(
                            replacement: Container(
                              margin: const EdgeInsets.only(
                                top: 25,
                              ),
                              height: 84,
                              width: MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: AppConstants.skeletonBackgroundColor,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(14.0),
                                child: Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          memberList?[index].userName ?? '',
                                          style: lbBold.copyWith(
                                            fontSize: 18,
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        NetworkAwareTap(
                                          onTap: () {
                                            final name =
                                                memberList?[index].userName;
                                            if (memberList!.length > 1) {
                                              assignLeaderBox(name, index);
                                            } else {
                                              assignLeaderFunction(name, index);
                                            }
                                          },
                                          child: (index == 0)
                                              ? Text(
                                                  'Assign New Leader',
                                                  style: lbItalic.copyWith(
                                                    fontSize: 12,
                                                    decoration: TextDecoration
                                                        .underline,
                                                    color: AppConstants
                                                        .primaryColor,
                                                  ),
                                                )
                                              : NetworkAwareTap(
                                                  onTap: () {
                                                    final name =
                                                        memberList?[index]
                                                            .userName;
                                                    removeMemberBox(
                                                        name, index);
                                                  },
                                                  child: Text(
                                                    'Remove',
                                                    style: lbItalic.copyWith(
                                                      fontSize: 12,
                                                      decoration: TextDecoration
                                                          .underline,
                                                      color: AppConstants
                                                          .primaryColor,
                                                    ),
                                                  ),
                                                ),
                                        ),
                                      ],
                                    ),
                                    const Spacer(),
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(49),
                                      child: CustomCachedNetworkImage(
                                        imageUrl: urlImage,
                                        width: 45,
                                        height: 45,
                                        errorImage:
                                            AppConstants.profileLogoImagePath,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            child: Container(
                              margin: const EdgeInsets.only(
                                top: 25,
                              ),
                              height: 84,
                              width: MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                  width: 1.5,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(14.0),
                                child: Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          memberList?[index].userName ?? '',
                                          style: lbBold.copyWith(
                                            fontSize: 18,
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        NetworkAwareTap(
                                          onTap: () {
                                            final name =
                                                memberList?[index].userName;
                                            if (memberList!.length > 1) {
                                              assignLeaderBox(name, index);
                                            } else {
                                              assignLeaderFunction(name, index);
                                            }
                                          },
                                          child: (index == 0)
                                              ? Text(
                                                  'Assign New Leader',
                                                  style: lbItalic.copyWith(
                                                    fontSize: 12,
                                                    decoration: TextDecoration
                                                        .underline,
                                                    color: AppConstants
                                                        .primaryColor,
                                                  ),
                                                )
                                              : NetworkAwareTap(
                                                  onTap: () {
                                                    final name =
                                                        memberList?[index]
                                                            .userName;
                                                    removeMemberBox(
                                                        name, index);
                                                  },
                                                  child: Text(
                                                    'Remove',
                                                    style: lbItalic.copyWith(
                                                      fontSize: 12,
                                                      decoration: TextDecoration
                                                          .underline,
                                                      color: AppConstants
                                                          .primaryColor,
                                                    ),
                                                  ),
                                                ),
                                        ),
                                      ],
                                    ),
                                    const Spacer(),
                                    CustomCachedNetworkImage(
                                      imageUrl: urlImage,
                                      width: 45,
                                      height: 45,
                                      errorImage:
                                          AppConstants.profileLogoImagePath,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        }
                        // If index is greater than or equal to the number of members, show "Club Opening"
                        else {
                          return Skeleton.replace(
                            replacement: Container(
                              margin: const EdgeInsets.only(top: 25, bottom: 0),
                              height: 84,
                              width: MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: AppConstants.skeletonBackgroundColor,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(14.0),
                                child: Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Club Opening',
                                          style: lbBold.copyWith(
                                            fontSize: 18,
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        NetworkAwareTap(
                                          onTap: () {
                                            addRemoveOpening(false);
                                          },
                                          child: Text(
                                            'Remove',
                                            style: lbItalic.copyWith(
                                              fontSize: 12,
                                              decoration:
                                                  TextDecoration.underline,
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const Spacer(),
                                    const CustomCachedNetworkImage(
                                      imageUrl: '',
                                      width: 45,
                                      height: 45,
                                      errorImage:
                                          AppConstants.clubOpeningLogoImagePath,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            child: Container(
                              margin: const EdgeInsets.only(top: 25, bottom: 0),
                              height: 84,
                              width: MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                  width: 1.5,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(14.0),
                                child: Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Club Opening',
                                          style: lbBold.copyWith(
                                            fontSize: 18,
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        memberList!.length <= 1 &&
                                                memberList![0]
                                                        .totalVacancies! <=
                                                    1
                                            ? const SizedBox.shrink()
                                            : NetworkAwareTap(
                                                onTap: () {
                                                  isInProgress
                                                      ? null
                                                      : addRemoveOpening(false);
                                                },
                                                child: Text(
                                                  'Remove',
                                                  style: lbItalic.copyWith(
                                                    fontSize: 12,
                                                    decoration: TextDecoration
                                                        .underline,
                                                    color: AppConstants
                                                        .primaryColor,
                                                  ),
                                                ),
                                              ),
                                      ],
                                    ),
                                    const Spacer(),
                                    const CustomCachedNetworkImage(
                                      imageUrl: '',
                                      width: 45,
                                      height: 45,
                                      errorImage:
                                          AppConstants.clubOpeningLogoImagePath,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        }
                      },
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void assignLeaderBox(String? name, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        roleController.clear();
        int? selectedUserId;
        int? selectedMemberId;
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Assign New Club Leader",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: SizedBox(
                    height: 45,
                    width: MediaQuery.of(context).size.width,
                    child: DropdownMenu<ClubMembershipModel>(
                      inputDecorationTheme: const InputDecorationTheme(
                        alignLabelWithHint: true,
                        contentPadding: EdgeInsets.symmetric(
                          // vertical: BorderSide.strokeAlignCenter,
                          horizontal: 10,
                        ),
                        filled: true,
                        fillColor: Color.fromRGBO(255, 245, 214, 1),
                        focusColor: AppConstants.primaryColor,
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(10),
                          ),
                        ),
                      ),
                      menuStyle: const MenuStyle(
                        shape: WidgetStatePropertyAll(
                          RoundedRectangleBorder(
                            side: BorderSide(
                              color: AppConstants.primaryColor,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.all(
                              Radius.circular(10),
                            ),
                          ),
                        ),
                        backgroundColor: WidgetStatePropertyAll(
                          Color.fromRGBO(255, 245, 214, 1),
                        ),
                      ),
                      expandedInsets: EdgeInsets.zero,
                      controller: roleController,
                      requestFocusOnTap: false,
                      textStyle: lbBold.copyWith(
                        fontSize: 12,
                      ),
                      onSelected: (newValue) async {
                        selectedUserId = newValue?.userId;
                        selectedMemberId = newValue?.bookClubMemberId;

                        roleController.text = newValue?.userName ?? '';
                        const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: AppConstants.primaryColor,
                        );
                      },
                      dropdownMenuEntries: clubMemberList?.map((role) {
                            return DropdownMenuEntry(
                              style: const ButtonStyle(
                                // shape: WidgetStatePropertyAll(
                                //   RoundedRectangleBorder(
                                //     borderRadius: BorderRadius.all(
                                //       Radius.circular(10),
                                //     ),
                                //     side: BorderSide(
                                //       color: AppConstants.primaryColor,
                                //     ),
                                //   ),
                                // ),
                                visualDensity: VisualDensity.comfortable,
                                side: WidgetStatePropertyAll(
                                  BorderSide(
                                    width: 0.5,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                                textStyle: WidgetStatePropertyAll(
                                  TextStyle(
                                    fontSize: 12,
                                    fontFamily: "Libre Baskerville Regular",
                                    fontStyle: FontStyle.normal,
                                    fontWeight: FontWeight.w400,
                                    decorationStyle: TextDecorationStyle.wavy,
                                  ),
                                ),
                              ),
                              value: role,
                              label: role.userName ?? '',
                            );
                          }).toList() ??
                          [],
                      menuHeight: 200,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          assignLeader(index, selectedUserId, selectedMemberId);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Save",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          // notAcceptedBox(author, impromptu, index);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            border: Border.all(
                              color: AppConstants.popUpBorderColor,
                            ),
                            color: AppConstants.backgroundColor,
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void confirmassignLeaderBox(int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
                color: AppConstants.popUpBorderColor, width: 1.5),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Assign New Club Leader",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Center(
                    child: Text(
                      "New Club leader has been assigned ",
                      style: lbRegular.copyWith(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Center(
                    child: Text(
                      "You no longer have Leader admin privileges",
                      style: lbRegular.copyWith(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.goNamed(
                      'Clubs',
                    );
                    context.pop();
                    setState(() {});
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void removeMemberBox(String? name, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Remove Member",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Text(
                    "Are you sure you would like to remove:",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  "$name",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () async {
                          context.pop();
                          await removeMember(index);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Remove",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();

                          // notAcceptedBox(author, impromptu, index);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            border: Border.all(
                              color: AppConstants.popUpBorderColor,
                            ),
                            color: AppConstants.backgroundColor,
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void confirmRemoveBox(int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Remove Member",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Center(
                    child: Text(
                      "${memberList?[index].userName ?? ''} has been removed",
                      style: lbRegular.copyWith(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                // const SizedBox(height: 10),
                // SizedBox(
                //   width: MediaQuery.of(context).size.width,
                //   child: Center(
                //     child: Text(
                //       "Member has been removed",
                //       style: lbRegular.copyWith(
                //         fontSize: 12,
                //         color: AppConstants.primaryColor,
                //       ),
                //     ),
                //   ),
                // ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    setState(() {});
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void assignLeaderFunction(String? name, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Assign New Club Leader",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "You have no other club members, you cannot assign a new leader. ",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          // memberList.removeAt(index);
                          // confirmRemoveBox(name);
                          setState(() {});
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Ok",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
