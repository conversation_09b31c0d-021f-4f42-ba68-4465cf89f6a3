import 'dart:developer';

import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import '../../../reusableWidgets/customDialouge_with_message.dart';
import '../../../reusableWidgets/custom_button.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';

class ManageIncomeRequestScreen extends StatefulWidget {
  final int? bookClubId;
  final String? userId;
  const ManageIncomeRequestScreen({
    super.key,
    this.bookClubId,
    this.userId,
  });

  @override
  State<ManageIncomeRequestScreen> createState() =>
      _ManageIncomeRequestScreenState();
}

class _ManageIncomeRequestScreenState extends State<ManageIncomeRequestScreen> {
  BookClubModel? bookCaseModel;
  String image = Config.imageBaseUrl;
  TextEditingController readRequestController = TextEditingController();
  TextEditingController acceptRequestController = TextEditingController();
  List<RequestManage>? incomingRequestList;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool acceptMessageValidation = false;
  bool isButtonLoading = false;

  @override
  void initState() {
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    super.initState();
    bookCaseModel =
        Provider.of<BookClubController>(context, listen: false).bookClubModel;
    getClubDetails();
    getIncomingrequest();
    // getIncomingrequest();
  }

  List<BookClubModel>? standingBookClubList;
  Future<void> getClubDetails() async {
    log("Book Club ID : ${widget.bookClubId}");
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubs(
      '',
      null,
      widget.bookClubId,
      context,
      null,
      null,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        List<BookClubModel> bookClubList = [];
        log("Response Map : ${responseMap["data"]}");
        if (responseMap["data"].isNotEmpty) {
          bookClubList = (responseMap["data"] as List)
              .map((item) => BookClubModel.fromJson(item))
              .toList();
          standingBookClubList = bookClubList;
          if (mounted) {
            Provider.of<BookClubController>(context, listen: false)
                .updateData(standingBookClubList?[0] ?? BookClubModel());
          }
          log("Standing Book Club List : ${standingBookClubList?[0].bookClubId}");
        } else {
          standingBookClubList?.clear();
          standingBookClubList = [];
        }
      } else {}
    });
  }

  BookClubController? bookClubController;
  bool isLoading = false;
  Future<void> getIncomingrequest() async {
    isLoading = true;
    setState(() {});
    await Provider.of<BookClubController>(context, listen: false)
        .inComingRequest(widget.bookClubId ?? 0, ClubMembershipStatus.pending,
            '', ClubRequestType.incomingRequestByClubId, context)
        .then((value) {
      incomingRequestList = bookClubController?.incomingOutGoingRequest?.data;
    });

    if (mounted) {
      await Provider.of<MessageController>(context, listen: false)
          .updateStandingClubRequests(incomingRequestList?.isNotEmpty ?? false);
      await Provider.of<MessageController>(context, listen: false)
          .manageIncomingRequestStatus(
              incomingRequestList?.isNotEmpty ?? false, context);
    }
    isLoading = false;
    setState(() {});
    bookClubController?.incomingRequestFunction(incomingRequestList);
  }

  @override
  void dispose() {
    super.dispose();
    readRequestController.dispose();
    acceptRequestController.dispose();
  }

  Future<void> acceptRequest(int index, bool flag) async {
    String? status;
    if (flag) {
      status = ClubMembershipStatus.active;
    } else {
      status = ClubMembershipStatus.rejected;
    }
    final payload = RequestManage(
      bookClubId: widget.bookClubId ?? 0,
      bookClubMemberId: incomingRequestList?[index].bookClubMemberId,
      userId: incomingRequestList?[index].userId,
      initiatedBy: incomingRequestList?[index].userId,
      status: status,
      responseMessage: acceptRequestController.text,
      userType: ClubMemberType.member,
    );

    await Provider.of<BookClubController>(context, listen: false)
        .updateInvitation(payload, context)
        .then((value) async {
      if (value["statusCode"] == 200) {
        getIncomingrequest();
        if (flag) {
          confirmInvitationBox(index);
        } else {
          confirmDeclineBox(index);
        }
      } else {
        if (mounted) {
          await showDialog(
            barrierColor: Colors.white60,
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return const CustomDialog(
                title: "Accept Invitation:",
                // subTitle: bookClubName,
                message:
                    "Sorry there was a problem with this request. You can search for the user and send them an invite. ",
                subMessage: "Message not sent",
                showDoneImage: false,
                incomingClubFont: true,
              );
            },
          ).then((_) {
            getIncomingrequest();
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: Consumer<BookClubController>(
              builder: (context, bookClubController, child) {
            return PreviousScreenAppBar(
              bookName: 'Manage Incoming Requests',
              clubName: bookClubController.bookClubModel?.bookClubName,
              clubNameFlag: true,
              isSetProfile: true,
            );
          }),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              "assets/images/PaperBackground.png",
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          enabled: isLoading,
          child: Column(
            children: [
              incomingRequestList?.isNotEmpty ?? false
                  ? Expanded(
                      child: Consumer<BookClubController>(
                          builder: (context, bookClubController, child) {
                        return ListView.builder(
                          scrollDirection: Axis.vertical,
                          padding: const EdgeInsets.only(
                            left: 20,
                            right: 20,
                            bottom: 25,
                          ),
                          itemCount: incomingRequestList?.length,
                          itemBuilder: (context, index) {
                            final userImage = image +
                                (incomingRequestList?[index]
                                        .userProfilePicture ??
                                    '');
                            return NetworkAwareTap(
                              onTap: () {
                                context.pushNamed(
                                  'club-member-profile',
                                  extra: {
                                    'userId':
                                        incomingRequestList?[index].userId,
                                    'userName':
                                        incomingRequestList?[index].userName
                                  },
                                );
                              },
                              child: Skeleton.replace(
                                replacement: requestSkeleton(
                                  true,
                                  index,
                                  userImage,
                                ),
                                child: requestSkeleton(
                                  false,
                                  index,
                                  userImage,
                                ),
                              ),
                            );
                          },
                        );
                      }),
                    )
                  : Padding(
                      padding: const EdgeInsets.only(top: 25.0),
                      child: Skeleton.replace(
                        replacement: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          padding: const EdgeInsets.only(left: 15),
                          height: 45,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            color: AppConstants.skeletonBackgroundColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "No pending requests",
                              textAlign: TextAlign.start,
                            ),
                          ),
                        ),
                        child: const NoDataWidget(
                          message: 'No pending requests',
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget nameWidget(int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          incomingRequestList?[index].userName ?? '',
          style: lbBold.copyWith(
            fontSize: 18,
            color: AppConstants.primaryColor,
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        NetworkAwareTap(
          onTap: () {
            final name = incomingRequestList?[index].userName;
            final message = incomingRequestList?[index].requestMessage ?? '';

            readInvitationBox(name, message);
          },
          child: Text(
            'Read Request to Join',
            style: lbItalic.copyWith(
              fontSize: 14,
              decoration: TextDecoration.underline,
              color: AppConstants.primaryColor,
            ),
          ),
        )
      ],
    );
  }

  Widget buttonWidget(int index) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        NetworkAwareTap(
          onTap: () {
            acceptInvitationBox(index);
          },
          child: Skeleton.replace(
            replacement: Container(
              height: 45,
              width: MediaQuery.of(context).size.width / 2.7,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(49),
                color: AppConstants.skeletonforgroundColor,
              ),
              child: Center(
                child: Text(
                  'Accept',
                  style: lbBold.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
            ),
            child: Container(
              height: 45,
              width: MediaQuery.of(context).size.width / 2.7,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(49),
                color: AppConstants.textGreenColor,
              ),
              child: Center(
                child: Text(
                  'Accept',
                  style: lbBold.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ),
        NetworkAwareTap(
          onTap: () {
            final name = incomingRequestList?[index].userName;
            declineRequestBox(name, index);
          },
          child: Skeleton.replace(
            replacement: Container(
              height: 45,
              width: MediaQuery.of(context).size.width / 2.7,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(49),
                color: AppConstants.skeletonforgroundColor,
              ),
              child: Center(
                child: Text(
                  'Decline',
                  style: lbBold.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
            ),
            child: Container(
              height: 45,
              width: MediaQuery.of(context).size.width / 2.7,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(49),
                border: Border.all(
                  color: AppConstants.primaryColor,
                  width: 1,
                ),
                color: Colors.transparent,
              ),
              child: Center(
                child: Text(
                  'Decline',
                  style: lbBold.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget requestSkeleton(bool isBorder, int index, String userImage) {
    return Container(
      margin: const EdgeInsets.only(top: 25),
      height: 157,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: isBorder ? Colors.transparent : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          children: [
            Row(
              children: [
                nameWidget(index),
                const Spacer(),
                ClipRRect(
                  borderRadius: BorderRadius.circular(49),
                  child: CustomCachedNetworkImage(
                    imageUrl: userImage,
                    width: 45,
                    height: 45,
                    errorImage: AppConstants.profileLogoImagePath,
                  ),
                )
              ],
            ),
            const SizedBox(
              height: 25,
            ),
            buttonWidget(index),
          ],
        ),
      ),
    );
  }

  void readInvitationBox(String? name, String? message) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Request:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                SizedBox(
                  // height: 65,
                  width: MediaQuery.of(context).size.width,
                  child: Center(
                    child: Text("$name",
                        style: lbRegular.copyWith(
                          fontSize: 12,
                        )),
                  ),
                ),
                const SizedBox(
                  height: 28,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: TextFormField(
                    maxLines: 5,
                    initialValue: message,
                    style: lbRegular.copyWith(
                      fontSize: 14,
                    ),
                    textAlignVertical: TextAlignVertical.top,
                    textAlign: TextAlign.start,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(10),
                      filled: true,
                      fillColor: AppConstants.backgroundColor,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(
                          color: Color.fromRGBO(37, 57, 67, 1),
                          width: 1.5,
                        ),
                      ),
                      hintText: 'No message',
                      hintStyle: lbRegular.copyWith(
                        fontSize: 12,
                      ),
                    ),
                    readOnly: true,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void acceptInvitationBox(int index) {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        acceptRequestController.clear();

        acceptMessageValidation = false;
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.all(20),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(
                          top: 10,
                        ),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: Text(
                        "Accept Request:",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                    const SizedBox(height: 25),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Align(
                        alignment: Alignment.topLeft,
                        child: Text(
                          "Send a message to welcome the new member",
                          overflow: TextOverflow.ellipsis,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 30.0, right: 20),
                          child: TextFormField(
                            controller: acceptRequestController,
                            style: lbRegular.copyWith(
                              fontSize: 12,
                            ),
                            maxLines: 5,
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.all(10),
                              filled: true,
                              fillColor: const Color.fromRGBO(255, 255, 255, 1),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5),
                                borderSide: const BorderSide(
                                  color: AppConstants.primaryColor,
                                  width: 1.5,
                                ),
                              ),
                            ),
                            onChanged: (value) {
                              setState(
                                () {
                                  acceptMessageValidation = false;
                                },
                              );
                            },
                            readOnly: false,
                          ),
                        ),
                        Positioned(
                          top: 115,
                          left: 30,
                          right: 0,
                          child: acceptMessageValidation
                              ? Text(
                                  "*Enter a message",
                                  style: lbRegular.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ),
                      ],
                    ),
                    acceptMessageValidation
                        ? const SizedBox(
                            height: 15,
                          )
                        : const SizedBox.shrink(),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Row(
                        mainAxisAlignment: isButtonLoading
                            ? MainAxisAlignment.center
                            : MainAxisAlignment.spaceBetween,
                        children: [
                          CustomLoaderButton(
                            buttonWidth: isButtonLoading
                                ? 45.0
                                : MediaQuery.of(context).size.width / 3.2,
                            buttonRadius: 30.0,
                            buttonChild: isButtonLoading
                                ? const CircularProgressIndicator(
                                    valueColor:
                                        AlwaysStoppedAnimation(Colors.white),
                                    strokeWidth: 3.0,
                                  )
                                : Text(
                                    "Accept",
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                            buttonPressed: () async {
                              bool validation =
                                  _formKey.currentState!.validate();
                              if (validation &&
                                  acceptRequestController.text.isNotEmpty) {
                                setState(() {
                                  isButtonLoading = true;
                                });
                                context.pop();
                                await acceptRequest(index, true);
                              } else {
                                setState(() {
                                  acceptMessageValidation = true;
                                });
                              }

                              setState(() {
                                isButtonLoading = false;
                              });
                            },
                          ),
                          !isButtonLoading
                              ? NetworkAwareTap(
                                  onTap: () {
                                    context.pop();
                                    // notAcceptedBox(author, impromptu, index);
                                  },
                                  child: Container(
                                    height: 45,
                                    width:
                                        MediaQuery.of(context).size.width / 3.2,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(49),
                                      border: Border.all(
                                        color: AppConstants.popUpBorderColor,
                                      ),
                                      color: AppConstants.backgroundColor,
                                    ),
                                    child: Center(
                                      child: Text(
                                        "Cancel",
                                        textAlign: TextAlign.center,
                                        style: lbBold.copyWith(
                                          fontSize: 18,
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              )
            ],
          );
        });
      },
    );
  }

  void confirmInvitationBox(int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Accept Request:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Center(
                    child: Text(
                      "Request has been accepted",
                      style: lbRegular.copyWith(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    // setState(() {});
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void declineRequestBox(String? name, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Decline Request:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "Are you sure you would like to decline the request of:",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  "$name",
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          // acceptRequestBox(author, impromptu, index);
                          acceptRequest(index, false);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Decline",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          // notAcceptedBox(author, impromptu, index);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            border: Border.all(
                              color: AppConstants.popUpBorderColor,
                            ),
                            color: AppConstants.backgroundColor,
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void confirmDeclineBox(int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Decline Request",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Center(
                    child: Text(
                      "Request has been declined",
                      style: lbRegular.copyWith(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    // setState(() {});
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
