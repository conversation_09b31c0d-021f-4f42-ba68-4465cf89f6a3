import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/home_model/home_screen2_model/meeting_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../controller/book_club_controller.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';

class ManageMeetingScreen extends StatefulWidget {
  final String? buttonName;

  const ManageMeetingScreen({
    super.key,
    this.buttonName,
  });

  @override
  State<ManageMeetingScreen> createState() => _ManageMeetingScreenState();
}

class _ManageMeetingScreenState extends State<ManageMeetingScreen> {
  BookClubModel? bookClubModel;
  int offSet = 0;
  int upComingMeetingLimit = 10;
  int upComingMeetingCount = 0;
  int previousMeetingLimit = 10;
  int previousMeetingCount = 0;
  bool upComingMeetingLoading = false;
  bool previousMeetingLoading = false;
  final ScrollController _upcomingMeetingScrollController = ScrollController();
  final ScrollController _previousMeetingScrollController = ScrollController();

  @override
  void initState() {
    _upcomingMeetingScrollController.addListener(_upComingOnScroll);
    _previousMeetingScrollController.addListener(_previousMOnScroll);
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      loadData(); // Ensures loadData is triggered after build
    });
    updateClubData();
  }

  // UPCOMING MEETING SCROLL
  void _upComingOnScroll() {
    if (_upcomingMeetingScrollController.position.pixels >=
            _upcomingMeetingScrollController.position.maxScrollExtent &&
        !upComingMeetingLoading &&
        (upcomingMeetings?.length ?? 0) < (upComingMeetingCount)) {
      CommonHelper.networkClose(getUpcomingMeetings(true), context);
      // getUpcomingMeetings(true);
    }
  }

  // PREVIOUS MEETING SCROLL
  void _previousMOnScroll() {
    if (_previousMeetingScrollController.position.pixels >=
            _previousMeetingScrollController.position.maxScrollExtent &&
        !previousMeetingLoading &&
        (previousMeetings?.length ?? 0) < (previousMeetingCount)) {
      CommonHelper.networkClose(getPreviousMeetings(true), context);

      // getPreviousMeetings(true);
    }
  }

  Future updateClubData() async {
    previousMeetings = Provider.of<BookClubController>(context, listen: false)
            .previousMeetingList ??
        [];
    upcomingMeetings = Provider.of<BookClubController>(context, listen: false)
            .upComingMeetingList ??
        [];
    bookClubModel =
        Provider.of<BookClubController>(context, listen: false).bookClubModel;
  }

  Future<void> deleteMeeting(int meetingId) async {
    await Provider.of<BookClubController>(context, listen: false)
        .deleteMeeting(meetingId, context)
        .then((value) async {
      if (value) {
        confirmDeleteBox();
        setState(() {});
      }
    });
  }

  Future loadData() async {
    // await Future.wait([
    await getPreviousMeetings(false);
    await getUpcomingMeetings(false);
    // ]);
  }

  List<MeetingList>? upcomingMeetings;
  Future<void> getUpcomingMeetings(bool isMore) async {
    if ((upcomingMeetings?.length ?? 0) <= upComingMeetingCount || !isMore) {
      upComingMeetingLoading = true;

      if (isMore) {
        upComingMeetingLimit += 10;
      }
    }
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubUpcomingMeetings(bookClubModel?.bookClubId ?? 0,
            upComingMeetingLimit, offSet, context)
        .then((value) {
      upComingMeetingCount =
          Provider.of<BookClubController>(context, listen: false)
                  .upcomingMeetingModel
                  ?.count ??
              0;
      upcomingMeetings = Provider.of<BookClubController>(context, listen: false)
          .upcomingMeetingModel
          ?.data;
      Provider.of<BookClubController>(context, listen: false).notifyListeners();
      print("Meeting Count : $upComingMeetingCount");
      if ((upcomingMeetings?.length ?? 0) >= upComingMeetingCount) {
        upComingMeetingLoading = false;
      }
    }).whenComplete(() {
      upComingMeetingLoading = false;
    });
    print("Upcoming List : $upcomingMeetings");
  }

  List<MeetingList>? previousMeetings;
  Future<void> getPreviousMeetings(bool isMore) async {
    if ((previousMeetings?.length ?? 0) <= previousMeetingCount || !isMore) {
      previousMeetingLoading = true;

      if (isMore) {
        previousMeetingLimit += 10;
      }
    }
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubPreviousMeetings(bookClubModel?.bookClubId ?? 0,
            previousMeetingLimit, offSet, context)
        .then((value) {
      previousMeetingCount =
          Provider.of<BookClubController>(context, listen: false)
                  .previousMeetingModel
                  ?.count ??
              0;
      previousMeetings = Provider.of<BookClubController>(context, listen: false)
          .previousMeetingModel
          ?.data;
      Provider.of<BookClubController>(context, listen: false).notifyListeners();
      if ((previousMeetings?.length ?? 0) >= previousMeetingCount) {
        previousMeetingLoading = false;
      }
      print("Previous Meeting Count : $previousMeetingCount");
      print("Previous List : $previousMeetings");
    }).whenComplete(() {
      previousMeetingLoading = false;
    });
  }

  @override
  void dispose() {
    _previousMeetingScrollController.dispose();
    _upcomingMeetingScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.buttonName,
            clubName: bookClubModel?.bookClubName,
            clubNameFlag: true,
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              "assets/images/PaperBackground.png",
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: FutureBuilder(
            future: loadData(),
            builder: (context, snapShot) {
              return Skeletonizer(
                effect: const SoldColorEffect(
                  color: AppConstants.skeletonforgroundColor,
                  lowerBound: 0.1,
                  upperBound: 0.5,
                ),
                containersColor: AppConstants.skeletonBackgroundColor,
                enabled: snapShot.connectionState == ConnectionState.waiting,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const SizedBox(
                        height: 25,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            NetworkAwareTap(
                              onTap: () {
                                context.pushNamed(
                                  'AddMeeting',
                                  extra: {
                                    'addMeeting': 'Add New Meeting',
                                    'boolean': false,
                                  },
                                );
                              },
                              child: Text(
                                'Add new meeting',
                                style: lbItalic.copyWith(
                                  fontSize: 16,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      upcomingMeetings?.isNotEmpty ?? false
                          ? const SizedBox(
                              height: 25,
                            )
                          : const SizedBox.shrink(),
                      upcomingMeetings?.isNotEmpty ?? false
                          ? Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              child: Text(
                                'Upcoming Meetings',
                                style: lbRegular.copyWith(
                                  fontSize: 20,
                                ),
                              ),
                            )
                          : const SizedBox.shrink(),
                      upcomingMeetings?.isNotEmpty ?? false
                          ? const SizedBox(
                              height: 10,
                            )
                          : const SizedBox.shrink(),
                      upcomingMeetings?.isNotEmpty ?? false
                          ? SizedBox(
                              height: 222,
                              child: Consumer<BookClubController>(
                                builder: (context, bookClubController, child) {
                                  return ListView.builder(
                                    controller:
                                        _upcomingMeetingScrollController,
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 20),
                                    itemCount: upComingMeetingLoading
                                        ? (upcomingMeetings?.length ?? 0) + 1
                                        : upcomingMeetings?.length,
                                    itemBuilder: (context, index) {
                                      if (index == upcomingMeetings?.length &&
                                          upComingMeetingLoading) {
                                        return const Padding(
                                          padding: EdgeInsets.only(left: 10.0),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        );
                                      }
                                      final meetingTime =
                                          CommonHelper.getMeetingScheduleTime(
                                              upcomingMeetings?[index]
                                                      .meetingStartTime ??
                                                  0,
                                              upcomingMeetings?[index]
                                                      .meetingEndTime ??
                                                  0);
                                      DateTime meetingStartTime;
                                      if (upcomingMeetings?[index]
                                              .meetingStartTime !=
                                          null) {
                                        meetingStartTime =
                                            DateTime.fromMillisecondsSinceEpoch(
                                                    upcomingMeetings?[index]
                                                            .meetingStartTime ??
                                                        0)
                                                .toLocal();
                                        // meetingStartTime =
                                        //     DateFormat('h:mm a').format(yyy);
                                      }

                                      final date = CommonHelper
                                          .getDayMonthYearDateFormat(
                                              upcomingMeetings?[index]
                                                  .meetingDate);

                                      return Skeleton.replace(
                                        replacement: upcomingMeetingSkeleton(
                                          false,
                                          index,
                                          meetingTime,
                                          date,
                                          // meetingStartTime,
                                        ),
                                        child: upcomingMeetingSkeleton(
                                          true,
                                          index,
                                          meetingTime,
                                          date,
                                          // meetingStartTime,
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.only(top: 25.0),
                              child: Skeleton.replace(
                                replacement: Container(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  padding: const EdgeInsets.only(left: 15),
                                  height: 45,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                    color: AppConstants.skeletonBackgroundColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      "No upcoming meetings",
                                      textAlign: TextAlign.start,
                                    ),
                                  ),
                                ),
                                child: const NoDataWidget(
                                  message: 'No upcoming meetings',
                                ),
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),
                      const Divider(
                        thickness: 1.5,
                        color: AppConstants.primaryColor,
                      ),
                      previousMeetings?.isNotEmpty ?? false
                          ? const SizedBox(
                              height: 25,
                            )
                          : const SizedBox.shrink(),
                      previousMeetings?.isNotEmpty ?? false
                          ? Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              child: Text(
                                'Previous Meetings',
                                style: lbRegular.copyWith(
                                  fontSize: 20,
                                ),
                              ),
                            )
                          : const SizedBox.shrink(),
                      previousMeetings?.isNotEmpty ?? false
                          ? const SizedBox(
                              height: 10,
                            )
                          : const SizedBox.shrink(),
                      previousMeetings?.isNotEmpty ?? false
                          ? SizedBox(
                              height: 186,
                              child: Consumer<BookClubController>(
                                builder: (context, bookClubController, child) {
                                  return ListView.builder(
                                    controller:
                                        _previousMeetingScrollController,
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 20),
                                    itemCount: previousMeetingLoading
                                        ? (previousMeetings?.length ?? 0) + 1
                                        : previousMeetings?.length,
                                    itemBuilder: (context, index) {
                                      if (index == previousMeetings?.length &&
                                          previousMeetingLoading) {
                                        return const Padding(
                                          padding: EdgeInsets.only(left: 10.0),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        );
                                      }
                                      final date = CommonHelper
                                          .getDayMonthYearDateFormat(
                                              previousMeetings?[index]
                                                  .meetingDate);
                                      final meetingTime =
                                          CommonHelper.getMeetingScheduleTime(
                                              previousMeetings?[index]
                                                      .meetingStartTime ??
                                                  0,
                                              previousMeetings?[index]
                                                      .meetingEndTime ??
                                                  0);
                                      return Skeleton.replace(
                                        replacement: previousMeetingSkeleton(
                                          false,
                                          index,
                                          date,
                                          meetingTime,
                                        ),
                                        child: previousMeetingSkeleton(
                                          true,
                                          index,
                                          date,
                                          meetingTime,
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.only(top: 25.0),
                              child: Skeleton.replace(
                                replacement: Container(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  padding: const EdgeInsets.only(left: 15),
                                  height: 45,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                    color: AppConstants.skeletonBackgroundColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      "No previous meetings",
                                      textAlign: TextAlign.start,
                                    ),
                                  ),
                                ),
                                child: const NoDataWidget(
                                  message: 'No previous meetings',
                                ),
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),
                    ],
                  ),
                ),
              );
            }),
      ),
    );
  }

  void deleteBox(int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Delete Meeting",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "Are you sure you would like to delete this meeting?",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          deleteMeeting(
                              upcomingMeetings?[index].meetingId ?? 0);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Delete",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          // context.goNamed('ClubsScreen3');
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.backgroundColor,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void confirmDeleteBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Delete Meeting",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "The meeting has been deleted",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                    // upcomingMeetings.removeAt(index);
                    // setState(() {});
                    // context.goNamed('ClubsScreen3');
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Widget previousMeetingSkeleton(
      bool flag, int index, String date, String meetingTime) {
    return Container(
      width: 250,
      margin: const EdgeInsets.only(left: 10),
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: flag ? Colors.transparent : AppConstants.skeletonBackgroundColor,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MarqueeList(
            children: [
              Text(
                previousMeetings?[index].bookName ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          MarqueeList(
            children: [
              Text(
                previousMeetings?[index].bookAuthor ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            previousMeetings?[index].partOfBookCovered ?? '',
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(
              fontSize: 14,
            ),
          ),
          const SizedBox(
            height: 18,
          ),
          Text(
            date,
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            meetingTime,
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          (previousMeetings?[index].discussionQuestions?.isNotEmpty ?? false)
              ? GestureDetector(
                  onTap: () {
                    context.pushNamed(
                      'discussionScreen',
                      extra: {
                        'clubName': bookClubModel?.bookClubName ?? '',
                        'discussionQue':
                            previousMeetings?[index].discussionQuestions,
                        // 'userName': userName,
                      },
                    );
                  },
                  child: Text(
                    'Discussion Questions',
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(
                      decoration: TextDecoration.underline,
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                )
              : Text(
                  'TBD',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
        ],
      ),
    );
  }

  Widget upcomingMeetingSkeleton(
    bool flag,
    int index,
    String meetingTime,
    String date,
  ) {
    log("Meeting Date : $date");
    return Container(
      width: 320,
      margin: const EdgeInsets.only(left: 10),
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: flag ? Colors.transparent : AppConstants.skeletonBackgroundColor,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MarqueeList(
            children: [
              Text(
                upcomingMeetings?[index].bookName ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          MarqueeList(
            children: [
              Text(
                upcomingMeetings?[index].bookAuthor ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            upcomingMeetings?[index].partOfBookCovered ?? '',
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(
              fontSize: 14,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            upcomingMeetings?[index].meetingDate != null ? date : 'TBD',
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            meetingTime,
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
            ),
          ),
          const SizedBox(
            height: 15,
          ),
          upcomingMeetings?[index].discussionQuestions?.isNotEmpty ?? false
              ? GestureDetector(
                  onTap: () {
                    context.pushNamed(
                      'discussionScreen',
                      extra: {
                        'clubName': bookClubModel?.bookClubName ?? '',
                        'discussionQue':
                            upcomingMeetings?[index].discussionQuestions,
                      },
                    );
                  },
                  child: Text(
                    'Discussion Questions',
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(
                      decoration: TextDecoration.underline,
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                )
              : Text(
                  'TBD',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
          const SizedBox(
            height: 19,
          ),
          Row(
            children: [
              meetingTime == 'TBD' ||
                      DateTime.now().isBefore(
                        DateTime.fromMillisecondsSinceEpoch(
                          upcomingMeetings?[index].meetingStartTime ?? 0,
                        ).toLocal(),
                      )
                  ? NetworkAwareTap(
                      onTap: () {
                        context.pushNamed(
                          'AddMeeting',
                          extra: {
                            'addMeeting': 'Edit Meeting',
                            'editData': upcomingMeetings?[index],
                            'boolean': true,
                            'editMeetingId': upcomingMeetings?[index].meetingId,
                          },
                        );
                      },
                      child: Text(
                        'Edit',
                        overflow: TextOverflow.ellipsis,
                        style: lbItalic.copyWith(
                          decoration: TextDecoration.underline,
                          fontSize: 14,
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
              const Spacer(),
              NetworkAwareTap(
                onTap: () {
                  deleteBox(index);
                },
                child: Text(
                  'Delete',
                  overflow: TextOverflow.ellipsis,
                  style: lbItalic.copyWith(
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                    decorationColor: AppConstants.redColor,
                    color: AppConstants.redColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
