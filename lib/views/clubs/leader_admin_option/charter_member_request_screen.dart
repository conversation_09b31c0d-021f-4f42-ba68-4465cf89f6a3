import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/leader_admin_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/label_with_icon.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../constants/constants.dart';
import '../../../constants/text_style.dart';
import '../../../reusableWidgets/custom_button.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';

class CharterMemberScreen extends StatefulWidget {
  final String? buttonName;

  const CharterMemberScreen({
    super.key,
    this.buttonName,
  });

  @override
  State<CharterMemberScreen> createState() => _CharterMemberScreenState();
}

class _CharterMemberScreenState extends State<CharterMemberScreen> {
  BookClubModel? bookClubModel;
  int? bookClubIndex;
  bool isLoading = false;
  String? clubType;

  @override
  void initState() {
    super.initState();

    bookClubModel =
        Provider.of<BookClubController>(context, listen: false).bookClubModel;
    // bookClubIndex =
    //     Provider.of<BookClubController>(context, listen: false).bookClubIndex;

    clubCharterController.text = bookClubModel?.clubCharter ?? '';
    memberRequestController.text = bookClubModel?.memberReqPrompt ?? '';
    clubType = bookClubModel?.bookClubType;
    print(clubType);
  }

  Future<void> updateCharterFunction() async {
    isLoading = true;
    setState(() {});
    await Provider.of<LeaderAdminController>(context, listen: false)
        .updateClubCharter(
      context,
      bookClubModel?.bookClubId ?? 0,
      clubCharterController.text,
      memberRequestController.text,
      'ACTIVE',
    )
        .then((value) async {
      isLoading = false;
      setState(() {});
      if (value) {
        await getBookClubsByUserId();
        if (mounted) {
          context.pop();
        }
      }
    });
  }

  int offSet = 0;
  int limit = 10;

  Future<void> getBookClubsByUserId() async {
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubs(
      '',
      null,
      bookClubModel?.bookClubId,
      context,
      offSet,
      limit,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        List<BookClubModel> bookClubList = [];
        bookClubList = (responseMap["data"] as List)
            .map((item) => BookClubModel.fromJson(item))
            .toList();
        if (bookClubList.isNotEmpty) {
          setState(() {
            bookClubModel = bookClubList.first;
          });
          Provider.of<BookClubController>(context, listen: false).updateData(
            bookClubModel!,
          );
        }
      } else {}
    });
  }

  bool clubCharter = false;
  bool memberRequest = false;
  int? loggedinUserId;
  TextEditingController clubCharterController = TextEditingController();
  TextEditingController memberRequestController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.buttonName,
            clubName: bookClubModel?.bookClubName,
            clubNameFlag: true,
            isSetProfile: true,
          ),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: GestureDetector(
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            child: Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    "assets/images/PaperBackground.png",
                  ),
                  filterQuality: FilterQuality.high,
                  fit: BoxFit.fitWidth,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 25,
                  ),
                  /* Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      children: [
                        Text(
                          'Club Charter',
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                        const Spacer(),
                        NetworkAwareTap(
                          onTap: () {
                            setState(() {
                              clubCharter = !clubCharter;
                            });
                          },
                          child: Image.asset(
                            'assets/icons/Edit.png',
                            height: 38,
                            width: 38,
                            filterQuality: FilterQuality.high,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ],
                    ),
                  ), */

                  LabelWithIcon(
                    labelText: 'Club Charter',
                    isOptional: true,
                    onIconTap: showClubCharterInfo,
                  ),
                  const SizedBox(
                    height: 10,
                  ),

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: TextFormField(
                      controller: clubCharterController,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                      maxLines: 4,
                      // enabled: clubCharter,
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: const Color.fromRGBO(255, 255, 255, 1),
                        contentPadding: const EdgeInsets.all(10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                          borderSide: const BorderSide(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  /* Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      children: [
                        Text(
                          'Member Request Prompt',
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                        const Spacer(),
                        NetworkAwareTap(
                          onTap: () {
                            setState(() {
                              memberRequest = !memberRequest;
                            });
                          },
                          child: Image.asset(
                            'assets/icons/Edit.png',
                            height: 38,
                            width: 38,
                            filterQuality: FilterQuality.high,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ],
                    ),
                  ), */
                  LabelWithIcon(
                    labelText: 'Member Request Prompt',
                    isOptional: true,
                    onIconTap: showMemberRequestPromtInfo,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: TextFormField(
                      controller: memberRequestController,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                      maxLines: 2,
                      // enabled: memberRequest,
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: const Color.fromRGBO(255, 255, 255, 1),
                        contentPadding: const EdgeInsets.all(10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                          borderSide: const BorderSide(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: CustomLoaderButton(
                      // loginText: 'Login',
                      buttonWidth:
                          isLoading ? 45.0 : MediaQuery.of(context).size.width,
                      buttonRadius: 30.0,
                      buttonChild: isLoading
                          ? const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation(Colors.white),
                              strokeWidth: 3.0,
                            )
                          : Text(
                              'Update',
                              style: lbBold.copyWith(
                                fontSize: 18,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                      buttonPressed: () {
                        /*  bool validation = _formKey.currentState!.validate();
                        if (validation &&
                            clubCharterController.text.isNotEmpty &&
                            memberRequestController.text.isNotEmpty) {
                          updateCharterFunction();
                        } else {
                          log('charter and member prompt empty');
                        } */
                        updateCharterFunction();
                      },
                    ),
                  ),
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  //   child: NetworkAwareTap(
                  //     onTap: () {
                  //       bool validation = _formKey.currentState!.validate();
                  //       if (validation &&
                  //           clubCharterController.text.isNotEmpty &&
                  //           memberRequestController.text.isNotEmpty) {
                  //         updateCharterFunction();
                  //       } else {
                  //         log('charter and member prompt empty');
                  //       }
                  //     },
                  //     child: Container(
                  //       height: 45,
                  //       width: MediaQuery.of(context).size.width,
                  //       decoration: BoxDecoration(
                  //         borderRadius: BorderRadius.circular(49),
                  //         color: AppConstants.textGreenColor,
                  //       ),
                  //       child: Center(
                  //         child: Text(
                  //           "Update",
                  //           textAlign: TextAlign.center,
                  //           style: lbBold.copyWith(fontSize: 18),
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildLabelWithIcon({
    required String labelText,
    required VoidCallback onIconTap,
    bool isOptional = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        children: [
          Text(
            labelText,
            style: lbRegular.copyWith(fontSize: 18),
          ),
          const SizedBox(width: 10),
          NetworkAwareTap(
            onTap: onIconTap,
            child: Image.asset(
              'assets/icons/information.png',
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
              height: 22,
              width: 22,
            ),
          ),
          if (isOptional) ...[
            const Spacer(),
            Text(
              '(Optional)',
              style: lbItalic.copyWith(fontSize: 14),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> showClubCharterInfo() async {
    CommonHelper.showClubCharterInfo(
      context: context,
      clubType: clubType,
    );
  }

  Future<void> showMemberRequestPromtInfo() async {
    CommonHelper.showMemberRequestPromptInfo(
      context: context,
      clubType: clubType,
    );
  }
}
