import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/profile_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/club_charter_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/manage__meeting_model.dart';
import 'package:eljunto/models/profile_model/edit_bookcase/listof_book_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/customDialouge_with_message.dart';
import 'package:eljunto/reusableWidgets/date_picker_util.dart';
import 'package:eljunto/reusableWidgets/label_with_icon.dart';
import 'package:eljunto/reusableWidgets/paginated_book_typeahead.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:eljunto/reusable_api_function/books_api_functions.dart';
import 'package:eljunto/views/clubs/clubs_home/services/clubs_sync_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../constants/constants.dart';
import '../../../reusableWidgets/custom_button.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';

class NewBookClubScreen extends StatefulWidget {
  final String? clubType;

  const NewBookClubScreen({
    super.key,
    this.clubType,
  });

  @override
  State<NewBookClubScreen> createState() => _NewBookClubScreenState();
}

class _NewBookClubScreenState extends State<NewBookClubScreen> {
  List<ClubCharterModel> clubCharterInfo = [];
  List<ClubCharterModel> memberRquestPromptInfo = [];

  int? loggedinUserId;
  int? bookId = 0;
  int? existingClubCount = 0;
  List<Books>? bookList = [];
  bool isNameAvailable = false;
  bool clubNameValidation = false;
  bool clubValidationFlag = false;
  bool impromptuValidationFlag = false;
  bool isLoading = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController clubNameController = TextEditingController();
  TextEditingController clubCharterController = TextEditingController();
  TextEditingController memberReqPromptController = TextEditingController();
  TextEditingController startTimeController = TextEditingController();
  TextEditingController durationController = TextEditingController();
  TextEditingController meetingDateController = TextEditingController();
  SuggestionsController<Books> suggestionsController = SuggestionsController();

  bool isBookIdNotEmpty = false;
  BookCaseController? bookCaseController;

  String clubNameLabel = '';
  String charterLabel = '';
  String appBarTitle = '';
  List<ClubCharterModel> clubCreateList = [
    ClubCharterModel(
      rules: 'Searching for people to invite to fill your 9 club openings. ',
    ),
    ClubCharterModel(
      rules: 'Changing the number of club openings.',
    ),
  ];
  int limit = 10;
  int offSet = 0;

  List<String> alertOptions = [
    '15 Min',
    '1 Day',
    '1 Week',
  ];
  List<String> selectedAlterOptions = [];

  @override
  void initState() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    profileController = Provider.of(context, listen: false);
    _initializeUserId();
    checkClubType();
    bookCaseController?.updateTypeAheadFlag(false);
    super.initState();
  }

  void checkClubType() {
    if (widget.clubType == ClubType.standing) {
      appBarTitle = 'New Standing Club';
    } else {
      appBarTitle = 'New Impromptu Club';
      startTimeController.text = "TBD";
      durationController.text = "TBD";
      meetingDateController.text = "TBD";
      selectedAlterOptions.addAll(alertOptions);
    }
  }

  @override
  void dispose() {
    suggestionsController.dispose();
    clubNameController.dispose();
    clubCharterController.dispose();
    memberReqPromptController.dispose();
    startTimeController.dispose();
    durationController.dispose();
    meetingDateController.dispose();
    super.dispose();
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();
  }

  /// Notify other screens about the new club creation
  void _notifyClubCreated() {
    final clubData = {
      'clubName': clubNameController.text.trim(),
      'bookId': bookId,
      'userId': loggedinUserId,
      'clubCharter': clubCharterController.text.trim(),
      'memberReqPrompt': memberReqPromptController.text.trim(),
      'createdAt': DateTime.now().millisecondsSinceEpoch,
    };

    log('ClubCreation: About to notify other screens about new ${widget.clubType} club creation');

    ClubsSyncService().onClubCreated(
      widget.clubType ?? ClubType.standing,
      clubData,
    );

    log('ClubCreation: Successfully notified other screens about new ${widget.clubType} club creation');
  }

  ProfileController? profileController;
  int bookListCount = 0;
  int bookListLimit = 30;
  bool isBookLoading = false;
  String searchValue = '';

  Future<void> checkClubName() async {
    String clubName = clubNameController.text.trim();
    if (widget.clubType == ClubType.standing) {
      bookId = null;
    } else {
      clubName = '';
    }
    await Provider.of<BookClubController>(context, listen: false)
        .checkAavailabilityOfClubName(clubName, bookId, context)
        .then((responseMap) {
      if (responseMap["statusCode"] == 200) {
        setState(() {
          clubValidationFlag = false;
          isNameAvailable = true;
          existingClubCount = responseMap["data"]["bookClubCount"];
        });
      } else if (responseMap["statusCode"] == 400) {
        setState(() {
          isNameAvailable = false;
        });
      } else {
        setState(() {
          /* errorMessag = responseMap['error'];
          handlerFlag = false; */
          isNameAvailable = false;
        });
      }
    });
  }

  Widget buildClubNameValidationMessage() {
    if (widget.clubType == ClubType.standing) {
      return Column(
        children: isNameAvailable
            ? [
                buildValidationMessage('Name available', isNameAvailable,
                    clubNameController.text.trim().isNotEmpty)
              ]
            : [
                buildValidationMessage('Name not available', isNameAvailable,
                    clubNameController.text.trim().isNotEmpty)
              ],
      );
    } else {
      return Column(children: [
        buildImpromptuMessage(
          'There is/are $existingClubCount impromptu clubs for this book.\nFinal impromptu club # will be assigned on creation.',
        )
      ]
          // :
          // [
          //     buildValidationMessage('Name not available', isNameAvailable,
          //         clubNameController.text.isNotEmpty)
          //   ],
          );
    }
  }

  Widget buildValidationMessage(String message, bool isValid, bool shouldShow) {
    if (!shouldShow) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            isValid ? Icons.check_circle : Icons.error,
            color: isValid ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8.0),
          Expanded(
            child: Text(message, style: lbItalic),
          ),
        ],
      ),
    );
  }

  Widget buildImpromptuMessage(String message) {
    // if (!shouldShow) {
    //   return const SizedBox.shrink();
    // }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon(
          //   isValid ? Icons.check_circle : Icons.error,
          //   color: isValid ? Colors.green : Colors.red,
          // ),
          const SizedBox(width: 8.0),
          Expanded(
            child: Text(clubNameController.text.trim().isEmpty ? '' : message,
                style: lbItalic),
          ),
        ],
      ),
    );
  }

  Future<void> showClubCharterInfo() async {
    CommonHelper.showClubCharterInfo(
      context: context,
      clubType: widget.clubType,
    );
  }

  Future<void> showMemberRequestPromtInfo() async {
    CommonHelper.showMemberRequestPromptInfo(
      context: context,
      clubType: widget.clubType,
    );
  }

  Future<void> showTentativeDateInfo() async {
    String subTitle =
        'Setting up a tentative first meeting will help other users who have this book in their currently reading or their to-be-read section match with your club.';
    clubCharterInfo = [];

    CommonHelper.showInfoDialog(
      context: context,
      title: "Tentative First Meeting Details",
      subTitle: subTitle,
      infoList: clubCharterInfo,
      isHide: true,
      canEditText: "(Can edit later to suit everyone’s schedule)",
    );
  }

  void durationValidateFunction() {
    CommonHelper.showDurationValidationDialog(context);
  }

  bool isCreateClub = false;

  Future<bool> addNewClub() async {
    BookClubModel newClub = BookClubModel(
      bookClubType: widget.clubType,
      bookClubName: clubNameController.text.trim(),
      bookId: bookId,
      userId: loggedinUserId,
      clubCharter: clubCharterController.text.trim(),
      memberReqPrompt: memberReqPromptController.text.trim(),
    );
    final meetingPayload = createMeetingPayload();
    setState(() {
      isLoading = true;
    });
    await Provider.of<BookClubController>(context, listen: false)
        .addBookClub(newClub, meetingPayload, context)
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        isCreateClub = true;
      } else {
        isCreateClub = false;
        setState(() {
          clubValidationFlag = true;
          impromptuValidationFlag = true;
          clubNameValidation = true;
        });
      }
    });
    setState(() {
      isLoading = false;
    });
    return isCreateClub;
  }

  Future<void> handleCreateClub(bool checkBookId) async {
    if (clubNameController.text.trim().isEmpty) {
      bookCaseController?.updateTypeAheadFlag(true);
      setState(() {
        clubNameValidation = true;
        isBookIdNotEmpty = false;
      });
      return;
    }
    if (checkBookId && bookId == 0) {
      setState(() {
        isBookIdNotEmpty = true;
      });
      return;
    }
    if (widget.clubType == ClubType.impromptu) {
      if (CommonHelper.conditionCheck(pickDate, pickedStartTime, durationKey)) {
        durationValidateFunction();
        return;
      }
    }

    if (clubNameController.text.trim().isNotEmpty) {
      if (isNameAvailable) {
        await addNewClub().then((value) {
          if (value) {
            // Notify other screens about the new club creation
            _notifyClubCreated();

            if (mounted) {
              context.pop();
              CommonHelper.showInfoDialog(
                context: context,
                title: "${clubNameController.text.trim()} Created",
                subTitle: "As Admin Leader consider:",
                infoList: clubCreateList,
                isHide: false,
              );
            }
          } else {
            if (mounted) {
              showDialog(
                barrierColor: Colors.white60,
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return const CustomDialog(
                      title: 'Add New Club',
                      message: 'Something went wrong, club not created');
                },
              );
            }
          }
        });
      } else {}
    } else {
      setState(() {
        clubNameValidation = true;
      });
    }
  }

  Widget _buildStandingForm() {
    return Form(
      key: _formKey,
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Text(
                  'Name of the club',
                  style: lbRegular.copyWith(
                    fontSize: 18,
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    TextFormField(
                      inputFormatters: [
                        clubNameController.text.isEmpty
                            ? FilteringTextInputFormatter.deny(RegExp(r'\s'))
                            : FilteringTextInputFormatter.singleLineFormatter,
                      ],
                      controller: clubNameController,
                      textCapitalization: TextCapitalization.sentences,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                      onChanged: (value) {
                        clubNameValidation = false;
                        checkClubName();
                      },
                      maxLines: 1,
                      maxLength: 50,
                      decoration: InputDecoration(
                        counterStyle: lbRegular.copyWith(
                          fontSize: 14,
                          color: AppConstants.primaryColor,
                        ),
                        filled: true,
                        fillColor: const Color.fromRGBO(255, 255, 255, 1),
                        contentPadding: const EdgeInsets.all(10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5),
                          borderSide: const BorderSide(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                        ),
                      ),
                    ),
                    positioned(),
                    clubNameController.text.trim().isNotEmpty
                        ? const SizedBox(
                            height: 10,
                          )
                        : const SizedBox.shrink(),
                    Positioned(
                      top: 55,
                      left: 0,
                      right: 0,
                      child: clubNameController.text.trim().isNotEmpty
                          ? buildClubNameValidationMessage()
                          : const SizedBox(),
                    ),
                  ],
                ),
              ),
              // clubValidationFlag
              //     ? const SizedBox(
              //         height: 25,
              //       )
              //     : const SizedBox.shrink(),

              const SizedBox(
                height: 25,
              ),
              LabelWithIcon(
                labelText: 'Standing club charter',
                onIconTap: showClubCharterInfo,
                isOptional: true,
              ),
              const SizedBox(
                height: 10,
              ),
              buildTextField(
                controller: clubCharterController,
                maxLines: 4,
              ),
              const SizedBox(
                height: 25,
              ),
              LabelWithIcon(
                labelText: 'Member request prompt',
                isOptional: true,
                onIconTap: showMemberRequestPromtInfo,
              ),
              const SizedBox(
                height: 10,
              ),
              buildTextField(
                controller: memberReqPromptController,
                maxLines: 2,
              ),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: !isLoading
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          buildButton(
                            buttonText: "Create club",
                            backgroundColor: AppConstants.textGreenColor,
                            onTap: () {
                              handleCreateClub(false);
                              //context.pop();
                            },
                          ),
                          buildButton(
                            buttonText: "Cancel",
                            hasBorder: true,
                            borderColor: AppConstants.primaryColor,
                            backgroundColor: Colors.transparent,
                            onTap: () {
                              context.pop();
                            },
                          ),
                        ],
                      )
                    : CustomLoaderButton(
                        // loginText: 'Login',
                        buttonWidth: isLoading
                            ? 45.0
                            : MediaQuery.of(context).size.width,
                        buttonRadius: 30.0,
                        buttonChild: isLoading
                            ? const CircularProgressIndicator(
                                valueColor:
                                    AlwaysStoppedAnimation(Colors.white),
                                strokeWidth: 3.0,
                              )
                            : Text(
                                'Create club',
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                        buttonPressed: () {
                          // bool validation = _formKey.currentState!.validate();
                        },
                      ),
              ),
              const SizedBox(
                height: 25,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String searchBook = '';
  List<Books>? autoBookList = [];

  Widget _buildImpromptuForm() {
    _generateTimeOptions();
    durationOptions = CommonHelper.generateTimeIntervalsMap();
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(
            AppConstants.bgImagePath,
          ),
          filterQuality: FilterQuality.high,
          fit: BoxFit.fitWidth,
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 25,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Text(
                'Book',
                style: lbRegular.copyWith(
                  fontSize: 18,
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  typeAheadField(),
                  impromptuClubError(),
                  // positioned(),
                  booknotSelect(),
                ],
              ),
            ),
            Consumer<BookCaseController>(
                builder: (context, bookCaseController, child) {
              return bookCaseController.isTypeAheadEmpty
                  ? const SizedBox(
                      height: 15,
                    )
                  : const SizedBox.shrink();
            }),
            isBookIdNotEmpty && bookId == 0
                ? const SizedBox(
                    height: 15,
                  )
                : const SizedBox.shrink(),
            impromptuValidationFlag
                ? const SizedBox(
                    height: 25,
                  )
                : const SizedBox.shrink(),
            clubNameController.text.trim().isNotEmpty
                ? const SizedBox(
                    height: 10,
                  )
                : const SizedBox.shrink(),
            clubNameController.text.trim().isNotEmpty
                ? buildClubNameValidationMessage()
                : const SizedBox(),
            const SizedBox(
              height: 25,
            ),
            LabelWithIcon(
              labelText: 'Tentative first meeting date',
              isOptional: false,
              onIconTap: showTentativeDateInfo,
            ),
            const SizedBox(
              height: 10,
            ),
            meetingStartDateWidget(),
            const SizedBox(
              height: 25,
            ),
            meetingStartTime(),
            const SizedBox(
              height: 25,
            ),
            LabelWithIcon(
              labelText: 'Impromptu club charter',
              isOptional: true,
              onIconTap: showClubCharterInfo,
            ),
            const SizedBox(
              height: 10,
            ),
            buildTextField(
              controller: clubCharterController,
              maxLines: 4,
            ),
            const SizedBox(
              height: 25,
            ),
            LabelWithIcon(
              labelText: 'Member request prompt',
              isOptional: true,
              onIconTap: showMemberRequestPromtInfo,
            ),
            const SizedBox(
              height: 10,
            ),
            buildTextField(
              controller: memberReqPromptController,
              maxLines: 2,
            ),
            const SizedBox(
              height: 25,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: !isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        buildButton(
                          buttonText: "Create club",
                          backgroundColor: AppConstants.textGreenColor,
                          onTap: () {
                            handleCreateClub(true);
                            //context.pop();
                          },
                        ),
                        buildButton(
                          buttonText: "Cancel",
                          hasBorder: true,
                          borderColor: AppConstants.primaryColor,
                          backgroundColor: Colors.transparent,
                          onTap: () {
                            context.pop();
                          },
                        ),
                      ],
                    )
                  : CustomLoaderButton(
                      buttonWidth:
                          isLoading ? 45.0 : MediaQuery.of(context).size.width,
                      buttonRadius: 30.0,
                      buttonChild: isLoading
                          ? const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation(Colors.white),
                              strokeWidth: 3.0,
                            )
                          : Text(
                              'Create club',
                              style: lbBold.copyWith(
                                fontSize: 18,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                      buttonPressed: () {},
                    ),
            ),
            const SizedBox(
              height: 25,
            ),
          ],
        ),
      ),
    );
  }

  Widget typeAheadField() {
    return PaginatedBookTypeahead(
      suggestionsController: suggestionsController,
      controller: clubNameController,
      fetchBooksCallback: (query, offset, limit) =>
          BooksApiFunctions.fetchBooks(
        query,
        offset,
        limit,
        context,
        loggedinUserId,
      ),
      isAddNewMeeting: true,
      onSelected: (book) {
        if (book.bookName == "Can't Find Your Book? Click Here") {
          questionFeedBox();
        } else {
          _handleBookSelection(book);
        }
      },
    );
  }

  void _handleBookSelection(Books book) {
    setState(() {
      clubNameController.text = book.bookName.toString();
      bookId = book.bookId;
      isBookIdNotEmpty = true;
      setState(() {});
      checkClubName();
      if (isBookIdNotEmpty && bookId != 0) {
        buildClubNameValidationMessage();
      }
    });
  }

  void questionFeedBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const QuestionFeedbackDialog(isBookQuery: true);
      },
    );
  }

  Widget positioned() {
    return Positioned(
      top: 55,
      left: 0,
      right: 0,
      child: clubNameValidation
          ? Text(
              widget.clubType == ClubType.impromptu
                  ? '*Select a book'
                  : '*Enter club name',
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            )
          : const SizedBox.shrink(),
    );
  }

  Widget impromptuClubError() {
    return Consumer<BookCaseController>(
        builder: (context, bookCaseController, child) {
      return Positioned(
        top: 55,
        left: 0,
        right: 0,
        child: bookCaseController.isTypeAheadEmpty
            ? Text(
                '*Select a book',
                style: lbRegular.copyWith(
                  fontSize: 14,
                  color: AppConstants.redColor,
                ),
              )
            : const SizedBox.shrink(),
      );
    });
  }

  Widget booknotSelect() {
    return Positioned(
      top: 55,
      left: 0,
      right: 0,
      child: isBookIdNotEmpty && bookId == 0
          ? Text(
              '*Invalid book',
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            )
          : const SizedBox.shrink(),
    );
  }

  Widget buildButton({
    required String buttonText,
    required VoidCallback onTap,
    Color backgroundColor = AppConstants.textGreenColor, // default color
    Color borderColor = AppConstants.primaryColor, // add border color parameter
    bool hasBorder =
        false, // add a flag to switch between solid color or border
  }) {
    return NetworkAwareTap(
      onTap: onTap,
      child: Container(
        height: 45,
        width: MediaQuery.of(context).size.width / 2.5,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(49),
          color: hasBorder
              ? Colors.transparent
              : backgroundColor, // Use transparent if hasBorder is true
          border: hasBorder
              ? Border.all(
                  color: borderColor, // Use border color if hasBorder is true
                )
              : null, // No border if hasBorder is false
        ),
        child: Center(
          child: Text(
            buttonText,
            textAlign: TextAlign.center,
            style: lbBold.copyWith(
              fontSize: 18,
            ),
          ),
        ),
      ),
    );
  }

  Widget buildTextField({
    required TextEditingController controller,
    int? maxLines,
    int? maxLength,
    VoidCallback? onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: TextFormField(
        controller: controller,
        textCapitalization: TextCapitalization.sentences,
        style: lbRegular.copyWith(
          fontSize: 18,
        ),
        maxLines: maxLines,
        maxLength: maxLength,
        onChanged: (value) => onChanged?.call(),
        decoration: InputDecoration(
          filled: true,
          fillColor: const Color.fromRGBO(255, 255, 255, 1),
          contentPadding: const EdgeInsets.all(10),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: const BorderSide(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
          ),
          counterStyle: lbRegular.copyWith(
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  TimeOfDay? pickedStartTime;
  DateTime? formatedTime;
  DateTime? pickDate;
  List<String> startTimeOptions = [];
  String? durationKey;
  List<Map<String, String>> durationOptions = [];

  Widget meetingStartTime() {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Start time',
                  style: lbRegular.copyWith(fontSize: 18),
                ),
                const SizedBox(height: 10),
                SizedBox(
                  height: 50,
                  width: MediaQuery.of(context).size.width / 2.5,
                  child: DropdownMenu(
                    inputDecorationTheme: const InputDecorationTheme(
                      alignLabelWithHint: true,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 10,
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      focusColor: AppConstants.primaryColor,
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                        borderRadius: BorderRadius.all(
                          Radius.circular(10),
                        ),
                      ),
                    ),
                    menuStyle: const MenuStyle(
                      surfaceTintColor:
                          WidgetStatePropertyAll(Colors.transparent),
                      shape: WidgetStatePropertyAll(
                        RoundedRectangleBorder(
                          side: BorderSide(
                            color: AppConstants.primaryColor,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(10),
                          ),
                        ),
                      ),
                      padding: WidgetStatePropertyAll(
                        EdgeInsets.zero,
                      ),
                      backgroundColor: WidgetStatePropertyAll(
                        Colors.white,
                      ),
                    ),
                    expandedInsets: EdgeInsets.zero,
                    controller: startTimeController,
                    requestFocusOnTap: false,
                    textStyle: lbRegular.copyWith(
                      fontSize: 18,
                    ),
                    onSelected: (newValue) async {
                      startTimeController.text = newValue.toString();
                      if (startTimeController.text == "TBD") {
                        pickedStartTime = null;
                      }
                      if (newValue != "TBD") {
                        // Parse the selected time to TimeOfDay
                        final selectedTimeOfDay =
                            CommonHelper.parseTimeOfDay_1(newValue.toString());

                        // Do something with the TimeOfDay object, e.g., store it or pass it to another function
                        pickedStartTime = selectedTimeOfDay;
                        // getAlertOptions(formatedTime ?? DateTime.now());
                        formatedTime = DateTime(
                          pickDate?.year ?? 0,
                          pickDate?.month ?? 0,
                          pickDate?.day ?? 0,
                          pickedStartTime?.hour ?? 0,
                          pickedStartTime?.minute ?? 0,
                        );
                        selectedAlterOptions =
                            CommonHelper.getMeetingAlertOptions(
                          formatedTime ?? DateTime.now(),
                        );
                        log("Alert Option : $selectedAlterOptions");
                      }
                      setState(() {});
                    },
                    dropdownMenuEntries: _timeOptions.map((role) {
                      return DropdownMenuEntry(
                        style: TextButton.styleFrom(
                          surfaceTintColor: Colors.white,
                          visualDensity: VisualDensity.comfortable,
                          side: const BorderSide(
                            width: 0.5,
                            color: AppConstants.primaryColor,
                          ),
                          textStyle: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                        value: role,
                        label: role,
                      );
                    }).toList(),
                    menuHeight: 200,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Duration',
                  style: lbRegular.copyWith(fontSize: 18),
                ),
                const SizedBox(height: 10),
                SizedBox(
                  height: 50,
                  width: MediaQuery.of(context).size.width / 2.5,
                  child: DropdownMenu(
                    // initialSelection: 'Title A-Z',
                    inputDecorationTheme: const InputDecorationTheme(
                      alignLabelWithHint: true,
                      contentPadding: EdgeInsets.symmetric(
                        // vertical: BorderSide.strokeAlignCenter,
                        horizontal: 10,
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      focusColor: AppConstants.primaryColor,
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                        borderRadius: BorderRadius.all(
                          Radius.circular(10),
                        ),
                      ),
                    ),

                    menuStyle: const MenuStyle(
                      surfaceTintColor:
                          WidgetStatePropertyAll(Colors.transparent),
                      shape: WidgetStatePropertyAll(
                        RoundedRectangleBorder(
                          side: BorderSide(
                            color: AppConstants.primaryColor,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(10),
                          ),
                        ),
                      ),
                      padding: WidgetStatePropertyAll(
                        EdgeInsets.zero,
                      ),
                      backgroundColor: WidgetStatePropertyAll(
                        Colors.white,
                      ),
                    ),

                    expandedInsets: EdgeInsets.zero,
                    controller: durationController,
                    requestFocusOnTap: false,
                    textStyle: lbRegular.copyWith(
                      fontSize: 18,
                    ),
                    onSelected: (newValue) async {
                      durationController.text = newValue?.values.first ?? '';
                      durationKey = newValue?.keys.first;
                      if (durationController.text == "TBD") {
                        durationKey = null;
                      }
                      // await sortList(startTimeController.text);
                      setState(() {});
                    },
                    dropdownMenuEntries: durationOptions.map((role) {
                      return DropdownMenuEntry(
                        style: TextButton.styleFrom(
                          visualDensity: VisualDensity.comfortable,
                          side: const BorderSide(
                            width: 0.5,
                            color: AppConstants.primaryColor,
                          ),
                          surfaceTintColor: Colors.white,
                          textStyle: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                        value: role,
                        label: role.values.first,
                      );
                    }).toList(),
                    menuHeight: 200,
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  width: 1.5,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
            child: PreviousScreenAppBar(
              bookName: appBarTitle,
              isSetProfile: true,
            ),
          ),
        ),
        body: widget.clubType == ClubType.standing
            ? _buildStandingForm()
            : _buildImpromptuForm(),
      ),
    );
  }

  // Check if the selected date is today
  bool _isToday() {
    final today = DateTime.now();
    return pickDate?.year == today.year &&
        pickDate?.month == today.month &&
        pickDate?.day == today.day;
  }

  // Check if the loop time is a future time compared to the current time
  bool _isFutureTime(TimeOfDay loopTime, TimeOfDay currentTime) {
    return (loopTime.hour > currentTime.hour) ||
        (loopTime.hour == currentTime.hour &&
            loopTime.minute > currentTime.minute);
  }

  // Format TimeOfDay to a readable string
  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  // Increment TimeOfDay by a given number of minutes
  TimeOfDay _incrementTimeByMinutes(TimeOfDay time, int minutes) {
    final totalMinutes = time.hour * 60 + time.minute + minutes;
    return TimeOfDay(hour: totalMinutes ~/ 60, minute: totalMinutes % 60);
  }

  List<String> _timeOptions = [];

  void _generateTimeOptions() {
    _timeOptions.clear();
    _timeOptions = ['TBD'];
    const int interval = 15;
    const startTime = TimeOfDay(hour: 0, minute: 0);
    const endTime = TimeOfDay(hour: 23, minute: 45);
    final currentTime = pickedStartTime ?? TimeOfDay.now();

    var loopTime = startTime;
    while (loopTime.hour < endTime.hour ||
        (loopTime.hour == endTime.hour && loopTime.minute <= endTime.minute)) {
      if (!_isToday() || _isFutureTime(loopTime, currentTime)) {
        _timeOptions.add(_formatTimeOfDay(loopTime));
      }
      loopTime = _incrementTimeByMinutes(loopTime, interval);
    }
    setState(() {}); // Update the dropdown options after generating times
  }

  Widget meetingStartDateWidget() {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: TextFormField(
          style: lbRegular.copyWith(
            fontSize: 18,
          ),
          controller: meetingDateController,
          decoration: InputDecoration(
            suffixIcon: const Icon(
              Icons.calendar_month_outlined,
              size: 25,
              color: AppConstants.primaryColor,
            ),
            filled: true,
            fillColor: const Color.fromRGBO(255, 255, 255, 1),
            contentPadding: const EdgeInsets.all(10),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: const BorderSide(
                color: AppConstants.primaryColor,
                width: 1.5,
              ),
            ),
          ),
          readOnly: true,
          onTap: () async {
            pickDate = await selectDate(
              context: context,
              meetingDateController: meetingDateController,
              isAndroid: Theme.of(context).platform == TargetPlatform.android,
              generateTimeOptions: _generateTimeOptions,
            );
            formatedTime = DateTime(
              pickDate?.year ?? 0,
              pickDate?.month ?? 0,
              pickDate?.day ?? 0,
              pickedStartTime?.hour ?? 0,
              pickedStartTime?.minute ?? 0,
            );

            if (pickDate == null) {
              meetingDateController.text = 'TBD';
            }
            setState(() {});
          },
        ));
  }

  ManageMeetingModel createMeetingPayload() {
    DateTime? startTime;
    if (pickDate != null && pickedStartTime != null) {
      startTime = DateTime(
        pickDate?.year ?? 0,
        pickDate?.month ?? 0,
        pickDate?.day ?? 0,
        pickedStartTime?.hour ?? 0,
        pickedStartTime?.minute ?? 0,
      );
    } else {
      startTime = null;
    }
    return ManageMeetingModel(
      bookId: bookId,
      meetingDate: startTime?.millisecondsSinceEpoch,
      meetingStartTime: startTime?.millisecondsSinceEpoch,
      meetingDuration: durationKey,
      meetingStatus: 'SCHEDULED',
      userId: loggedinUserId,
      discussionQuestions: "TBD",
      meetingAlerts: selectedAlterOptions,
      partOfBookCovered: "TBD",
    );
  }
}
