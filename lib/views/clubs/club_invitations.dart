import 'dart:developer';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import '../../reusableWidgets/customDialouge_with_message.dart';
import '../../reusableWidgets/custom_button.dart';

class ClubInvitations extends StatefulWidget {
  final String? invitation;
  final int? userId;

  const ClubInvitations({
    super.key,
    this.invitation,
    this.userId,
  });

  @override
  State<ClubInvitations> createState() => _ClubInvitationsState();
}

class _ClubInvitationsState extends State<ClubInvitations> {
  RequestManage? selectedInvitaton;
  TextEditingController readInvitationController = TextEditingController();

  // int? loggedinUserId;
  Future<void>? loadAPI;
  String responseMessage = '';
  String subMessage = '';
  bool responseMsgValidation = false;

  @override
  void initState() {
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    multipleFunctionCall();
    super.initState();
  }

  Future<void> _initializeUserId() async {
    // loggedinUserId = await CommonHelper.getLoggedInUserId();

    // if (userId != null) {
    //   loggedinUserId = userId;
    // } else {}
  }

  Future multipleFunctionCall() async {
    await _initializeUserId();
    await Future.wait([
      getPendingInvitations(),
    ]);
  }

  BookClubController? bookClubController;
  List<RequestManage>? bookClubInvitation;
  bool isLoading = false;
  bool isButtonLoading = false;

  Future<void> getPendingInvitations() async {
    isLoading = true;
    setState(() {});
    await Provider.of<BookClubController>(context, listen: false)
        .inComingRequest(
            widget.userId ?? 0,
            ClubMembershipStatus.pending,
            ClubMembershipStatus.reOpened,
            ClubRequestType.incomingClubRequestByUserId,
            context)
        .then((value) {
      bookClubInvitation = bookClubController?.incomingOutGoingRequest?.data;
    });
    if (mounted) {
      await Provider.of<MessageController>(context, listen: false)
          .incomingClubInvitationStatus(
              bookClubInvitation?.isNotEmpty ?? false);
    }
    isLoading = false;
    setState(() {});
  }

  @override
  void dispose() {
    readInvitationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: "Incoming Club Invitations",
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isLoading,
          child: Column(
            children: [
              bookClubInvitation?.isNotEmpty ?? false
                  ? Expanded(
                      child: Consumer<BookClubController>(
                          builder: (context, bookClubController, child) {
                        return ListView.builder(
                          padding: const EdgeInsets.only(
                            bottom: 25,
                          ),
                          scrollDirection: Axis.vertical,
                          itemCount: bookClubInvitation?.length ?? 0,
                          itemBuilder: (context, index) {
                            String author = '';
                            String clubCount = '';
                            if (bookClubInvitation?[index].clubType ==
                                ClubType.impromptu) {
                              author =
                                  bookClubInvitation?[index].bookAuthor ?? '';
                              clubCount =
                                  bookClubInvitation?[index].clubCount ?? '';
                            }
                            return NetworkAwareTap(
                              onTap: () {
                                context.pushNamed(
                                  "club-details",
                                  extra: {
                                    'bookClubId':
                                        bookClubInvitation?[index].bookClubId,
                                    'bookClubName':
                                        bookClubInvitation?[index].bookClubName,
                                    'impromptuCount': clubCount,
                                  },
                                );
                              },
                              child: Skeleton.replace(
                                replacement: skeletonWidget(true, index, author,
                                    clubCount, bookClubInvitation ?? []),
                                child: skeletonWidget(false, index, author,
                                    clubCount, bookClubInvitation ?? []),
                              ),
                            );
                          },
                        );
                      }),
                    )
                  : Skeleton.replace(
                      replacement: Container(
                        padding: const EdgeInsets.only(left: 20),
                        margin:
                            const EdgeInsets.only(left: 20, right: 20, top: 25),
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: AppConstants.skeletonBackgroundColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "No pending invitations",
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ),
                      child: const Padding(
                        padding: EdgeInsets.only(top: 25.0),
                        child: NoDataWidget(
                          message: "No pending invitations",
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget skeletonWidget(bool isBorder, int index, String author,
      String clubCount, List<RequestManage> bookClubItem) {
    return Container(
      margin: const EdgeInsets.only(left: 20, right: 20, top: 25),
      padding: const EdgeInsets.all(14),
      height: 179,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          nameLogoWidget(index, author, bookClubItem),
          const SizedBox(
            height: 8,
          ),
          Row(
            children: [
              Text(
                clubCount,
                overflow: TextOverflow.ellipsis,
                style: lbItalic.copyWith(
                  fontSize: 14,
                  // decoration:
                  //     TextDecoration.underline,
                ),
              ),
              (bookClubItem[index].clubType != null &&
                      bookClubItem[index].clubType == ClubType.impromptu)
                  ? const Spacer()
                  : const SizedBox.shrink(),
              NetworkAwareTap(
                onTap: () {
                  selectedInvitaton = bookClubItem[index];
                  readInvitationMessage();
                },
                child: Text(
                  "Read Invitation",
                  overflow: TextOverflow.ellipsis,
                  style: lbItalic.copyWith(
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 25,
          ),
          acceptDeclineButtonWidget(index, bookClubItem),
        ],
      ),
    );
  }

  void readInvitationMessage() {
    String bookClubName = selectedInvitaton?.bookClubName ?? '';
    String clubCount = '';
    if (selectedInvitaton?.clubType == ClubType.impromptu) {
      bookClubName = '$bookClubName-${selectedInvitaton?.bookAuthor}';
      clubCount = selectedInvitaton?.clubCount ?? '';
    }

    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Text(
                    "Invitation:",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30, right: 20),
                  child: Text(
                    bookClubName,
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                Text(
                  clubCount,
                  style: lbItalic.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: TextFormField(
                    maxLines: 6,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                    initialValue: selectedInvitaton?.invitationMessage ?? '',
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(10),
                      filled: true,
                      fillColor: AppConstants.backgroundColor,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                    ),
                    readOnly: true,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                    // confirmBox();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Future<bool> confirmInvitationResponse() async {
    log("Selected Invitation here : ${selectedInvitaton?.status}");

    await Provider.of<BookClubController>(context, listen: false)
        .updateInvitation(selectedInvitaton ?? RequestManage(), context)
        .then(
      (responseMap) {
        log("Response Map : $responseMap");
        if (responseMap["statusCode"] == 200) {
          if (selectedInvitaton?.status == ClubMembershipStatus.active) {
            responseMessage = 'Message sent! You are now in the club';
          } else if (selectedInvitaton?.status ==
              ClubMembershipStatus.rejected) {
            responseMessage = 'Declined!';
          } else {
            responseMessage =
                'Sorry there was a problem with this invitation. Search for the club page and request to fill any remaining club openings.';
            subMessage = 'Message not sent';
          }
        }
      },
    );

    return true;
  }

  void handleInvitationResponse(String response, int index) {
    String title = "Accept Invitation:";
    String btnLabel = "Accept";
    String bookClubName = selectedInvitaton?.bookClubName ?? '';
    String clubCount = '';
    // bool responseMsgValidation = false;
    TextEditingController responseController = TextEditingController();
    if (response == ClubMembershipStatus.rejected) {
      btnLabel = "Decline";
      title = "Decline Invitation:";
    }

    if (selectedInvitaton?.clubType == ClubType.impromptu) {
      bookClubName = '$bookClubName-${selectedInvitaton?.bookAuthor}';
      clubCount = selectedInvitaton?.clubCount ?? '';
    }
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        isButtonLoading = false;
        responseMsgValidation = false;
        return StatefulBuilder(builder: (context, setState) {
          return Center(
            child: SingleChildScrollView(
              child: GestureDetector(
                onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
                child: AlertDialog(
                  actionsPadding: const EdgeInsets.only(right: 10),
                  insetPadding: const EdgeInsets.all(25),
                  contentPadding: EdgeInsets.zero,
                  backgroundColor: AppConstants.backgroundColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                    side: const BorderSide(
                      color: AppConstants.popUpBorderColor,
                      width: 1.5,
                    ),
                  ),
                  surfaceTintColor: Colors.white,
                  actions: [
                    Column(
                      children: [
                        NetworkAwareTap(
                          onTap: () {
                            context.pop();
                          },
                          child: Container(
                            alignment: Alignment.centerRight,
                            padding: const EdgeInsets.only(
                              top: 10,
                            ),
                            child: Image.asset(
                              AppConstants.closePopupImagePath,
                              height: 30,
                              width: 30,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Text(
                            title,
                            textAlign: TextAlign.center,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                        const SizedBox(height: 25),
                        Padding(
                          padding: const EdgeInsets.only(left: 30, right: 20),
                          child: Text(
                            bookClubName,
                            textAlign: TextAlign.center,
                            style: lbRegular.copyWith(
                              fontSize: 12,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                        if (response == ClubMembershipStatus.active) ...[
                          const SizedBox(
                            height: 3,
                          ),
                          Text(
                            clubCount,
                            style: lbItalic.copyWith(
                              fontSize: 12,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          clubCount.isNotEmpty
                              ? const SizedBox(
                                  height: 15,
                                )
                              : const SizedBox.shrink(),
                        ],
                        const SizedBox(
                          height: 10,
                        ),
                        if (response == ClubMembershipStatus.active) ...[
                          Padding(
                            padding:
                                const EdgeInsets.only(left: 30.0, right: 20),
                            child: Align(
                              alignment: Alignment.topLeft,
                              child: Text(
                                "Send an introductory message to the club",
                                style: lbRegular.copyWith(
                                  fontSize: 12,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Stack(
                            clipBehavior: Clip.none,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 30.0, right: 20),
                                child: TextFormField(
                                  controller: responseController,
                                  maxLines: 6,
                                  maxLength: 500,
                                  style: lbRegular.copyWith(
                                    fontSize: 12,
                                  ),
                                  decoration: InputDecoration(
                                    counterStyle: lbRegular.copyWith(
                                      fontSize: 12,
                                    ),
                                    contentPadding: const EdgeInsets.all(10),
                                    filled: true,
                                    fillColor:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(5),
                                      borderSide: const BorderSide(
                                        color: AppConstants.primaryColor,
                                        width: 1.5,
                                      ),
                                    ),
                                  ),
                                  onChanged: (value) {
                                    setState(() {
                                      responseMsgValidation = false;
                                    });
                                  },
                                ),
                              ),
                              Positioned(
                                top: 130,
                                left: 30,
                                right: 0,
                                child: responseMsgValidation
                                    ? Text(
                                        "*Enter a message",
                                        style: lbRegular.copyWith(
                                          fontSize: 14,
                                          color: AppConstants.redColor,
                                        ),
                                      )
                                    : const SizedBox.shrink(),
                              ),
                            ],
                          ),
                        ],
                        const SizedBox(
                          height: 25,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 30.0, right: 20),
                          child: Row(
                            mainAxisAlignment: isButtonLoading
                                ? MainAxisAlignment.center
                                : MainAxisAlignment.spaceBetween,
                            children: [
                              CustomLoaderButton(
                                // loginText: 'Login',
                                buttonWidth: isButtonLoading
                                    ? 45.0
                                    : MediaQuery.of(context).size.width / 3.2,
                                buttonRadius: 30.0,
                                buttonChild: isButtonLoading
                                    ? const CircularProgressIndicator(
                                        valueColor: AlwaysStoppedAnimation(
                                            Colors.white),
                                        strokeWidth: 3.0,
                                      )
                                    : Text(
                                        btnLabel,
                                        style: lbBold.copyWith(
                                          fontSize: 18,
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                buttonPressed: () async {
                                  if (responseController.text.isEmpty &&
                                      response == ClubMembershipStatus.active) {
                                    setState(() {
                                      responseMsgValidation = true;
                                    });
                                  } else {
                                    setState(() {
                                      isButtonLoading = true;
                                    });
                                    selectedInvitaton?.status = response;
                                    selectedInvitaton?.responseMessage =
                                        responseController.text;

                                    await confirmInvitationResponse().then(
                                      (value) async {
                                        if (context.mounted) {
                                          context.pop();

                                          await showDialog(
                                            barrierColor: Colors.white60,
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (BuildContext context) {
                                              return CustomDialog(
                                                title: title,
                                                subTitle: bookClubName,
                                                message: responseMessage,
                                                subMessage: subMessage,
                                                showDoneImage: false,
                                                incomingClubFont: true,
                                              );
                                            },
                                          ).then((value) {
                                            getPendingInvitations();
                                          });
                                        }
                                      },
                                    );
                                  }
                                  setState(
                                    () {
                                      isButtonLoading = false;
                                    },
                                  );
                                },
                              ),
                              !isButtonLoading
                                  ? NetworkAwareTap(
                                      onTap: () {
                                        context.pop();
                                      },
                                      child: Container(
                                        height: 45,
                                        width:
                                            MediaQuery.of(context).size.width /
                                                3.2,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(49),
                                          border: Border.all(
                                            color:
                                                AppConstants.popUpBorderColor,
                                          ),
                                          color: AppConstants.backgroundColor,
                                        ),
                                        child: Center(
                                          child: Text(
                                            "Cancel",
                                            textAlign: TextAlign.center,
                                            style: lbRegular.copyWith(
                                              fontSize: 18,
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                  : const SizedBox.shrink(),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 30,
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  void acceptInvitationBox(String? author, String? impromptu, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Accept Invitation:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 32, right: 20),
                  child: SizedBox(
                    // height: 65,
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "The Ranger Objective - $author",
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                Text(
                  "$impromptu",
                  style: lbItalic.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                Text(
                  "Send an introductory message to the club",
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: TextFormField(
                    maxLines: 4,
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: const Color.fromRGBO(255, 255, 255, 1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                    ),
                    readOnly: false,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          acceptRequestBox(author, impromptu, index);
                          setState(() {});
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Submit",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          notAcceptedBox(author, impromptu, index);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            border: Border.all(
                              color: AppConstants.popUpBorderColor,
                            ),
                            color: AppConstants.backgroundColor,
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void notAcceptedBox(String? author, String? impromptu, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Accept Invitation:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 32, right: 20),
                  child: SizedBox(
                    // height: 65,
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "The Ranger Objective - $author",
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                Text(
                  "$impromptu",
                  style: lbItalic.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    "Sorry there was a problem with this invitation. Search for the club page and request to fill any remaining club openings",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                Text(
                  "Message not sent.",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                    setState(() {});
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void acceptRequestBox(String? author, String? impromptu, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Accept Invitation:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 32, right: 20),
                  child: SizedBox(
                    // height: 65,
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        "The Ranger Objective - $author",
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 3,
                ),
                Text(
                  "$impromptu",
                  style: lbItalic.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                Text(
                  "Message sent! You are now in the club",
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                    // pendingInvitations.removeAt(index);
                    setState(() {});
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void declineRequestBox(String? bookName, int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Decline Invitation:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Center(
                    child: Text(
                      "$bookName",
                      style: lbRegular.copyWith(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          // pendingInvitations.removeAt(index);
                          declinedConfirmBox(index);
                          setState(() {});
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Decline",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          setState(() {});
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.backgroundColor,
                              border: Border.all(
                                color: const Color.fromRGBO(45, 45, 45, 1),
                              )),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void declinedConfirmBox(int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(
                      top: 10,
                    ),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Decline Invitation:",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 25),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Center(
                    child: Text(
                      "Declined!",
                      style: lbRegular.copyWith(
                        fontSize: 12,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Widget nameLogoWidget(
      int index, String author, List<RequestManage> bookClubItem) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MarqueeList(
                children: [
                  Text(
                    bookClubItem[index].bookClubName ?? '',
                    overflow: TextOverflow.ellipsis,
                    style: lbBold.copyWith(
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
              Text(
                author,
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          width: 5,
        ),
        ClipRRect(
          borderRadius: BorderRadius.circular(50),
          child: Image.asset(
            AppConstants.clubOpeningLogoImagePath,
            height: 50,
            width: 50,
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }

  Widget acceptDeclineButtonWidget(
      int index, List<RequestManage> bookClubItem) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        NetworkAwareTap(
          onTap: () {
            selectedInvitaton = bookClubItem[index];
            handleInvitationResponse(ClubMembershipStatus.active, index);
          },
          child: Container(
            height: 45,
            width: MediaQuery.of(context).size.width / 2.7,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(49),
              color: AppConstants.textGreenColor,
            ),
            child: Center(
              child: Text(
                "Accept",
                textAlign: TextAlign.center,
                style: lbBold.copyWith(
                  fontSize: 18,
                ),
              ),
            ),
          ),
        ),
        NetworkAwareTap(
          onTap: () {
            selectedInvitaton = bookClubItem[index];
            handleInvitationResponse(ClubMembershipStatus.rejected, index);
          },
          child: Container(
            height: 45,
            width: MediaQuery.of(context).size.width / 2.7,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(49),
              border: Border.all(
                color: AppConstants.popUpBorderColor,
              ),
              color: Colors.transparent,
            ),
            child: Center(
              child: Text(
                "Decline",
                textAlign: TextAlign.center,
                style: lbBold.copyWith(
                  fontSize: 18,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
