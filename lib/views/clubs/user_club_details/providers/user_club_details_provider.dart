import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/club_membership_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/models/home_model/home_screen2_model/meeting_model.dart';
import 'package:eljunto/models/meeting_join_model.dart';
import 'package:eljunto/views/clubs/clubs_home/services/clubs_sync_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// Dedicated provider for User Club Details screen
/// Implements targeted refresh methods and event-based updates
class UserClubDetailsProvider with ChangeNotifier {
  // Dependencies
  BookClubController? _bookClubController;
  MessageController? _messageController;

  // State variables
  BookClubModel? _bookClubModel;
  List<ClubMembershipModel>? _memberList;
  List<RequestManage>? _incomingRequestList;
  List<MeetingList>? _upcomingMeetings;
  List<MeetingList>? _previousMeetings;
  MeetingJoinModel? _meetingJoinModel;

  // User data
  int? _loggedInUserId;
  String? _userName;
  String? _userHandle;

  // Loading states
  bool _isInitialLoading = false;
  bool _isMembersLoading = false;
  bool _isUpcomingMeetingsLoading = false;
  bool _isPreviousMeetingsLoading = false;
  bool _isJoinMeetingLoading = false;

  // Pagination
  int _upcomingMeetingLimit = 10;
  int _previousMeetingLimit = 10;
  int _upcomingMeetingCount = 0;
  int _previousMeetingCount = 0;
  final int _offset = 0;

  // Member data
  List<int>? _memberIdsList;
  List<Map<int?, String?>> _userHandles = [];
  List<Map<int?, String?>> _userProfilePicture = [];

  // Getters
  BookClubModel? get bookClubModel => _bookClubModel;
  List<ClubMembershipModel>? get memberList => _memberList;
  List<RequestManage>? get incomingRequestList => _incomingRequestList;
  List<MeetingList>? get upcomingMeetings => _upcomingMeetings;
  List<MeetingList>? get previousMeetings => _previousMeetings;
  MeetingJoinModel? get meetingJoinModel => _meetingJoinModel;

  int? get loggedInUserId => _loggedInUserId;
  String? get userName => _userName;
  String? get userHandle => _userHandle;

  bool get isInitialLoading => _isInitialLoading;
  bool get isMembersLoading => _isMembersLoading;
  bool get isUpcomingMeetingsLoading => _isUpcomingMeetingsLoading;
  bool get isPreviousMeetingsLoading => _isPreviousMeetingsLoading;
  bool get isJoinMeetingLoading => _isJoinMeetingLoading;

  int get upcomingMeetingCount => _upcomingMeetingCount;
  int get previousMeetingCount => _previousMeetingCount;

  List<int>? get memberIdsList => _memberIdsList;
  List<Map<int?, String?>> get userHandles => _userHandles;
  List<Map<int?, String?>> get userProfilePicture => _userProfilePicture;

  bool get hasNewStandingClubRequest =>
      _messageController?.hasNewStandingClubRequest ?? false;

  BookClubController? get bookClubController => _bookClubController;

  /// Initialize the provider with required dependencies
  void initialize(BuildContext context) {
    _bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    _messageController = Provider.of<MessageController>(context, listen: false);
  }

  /// Initialize user data from local storage
  Future<void> initializeUserData() async {
    _userName = await CommonHelper.getLoggedinUserName();
    _userHandle = await CommonHelper.getLoggedinUserHandler();
    _loggedInUserId = await CommonHelper.getLoggedInUserId();
    notifyListeners();
  }

  /// Load all initial data for the club details screen
  Future<void> loadInitialData(int bookClubId, BuildContext context) async {
    _isInitialLoading = true;
    notifyListeners();

    try {
      await initializeUserData();

      // Load all data concurrently for better performance
      await Future.wait([
        getClubDetails(bookClubId, context),
        getUpcomingMeetings(bookClubId, false, context),
        getPreviousMeetings(bookClubId, false, context),
        getBookClubMembers(bookClubId, context),
        getIncomingRequest(bookClubId, context),
      ]);
    } catch (e) {
      log('Error loading initial data: $e');
    } finally {
      _isInitialLoading = false;
      notifyListeners();
    }
  }

  /// Get club details - targeted refresh method
  Future<void> getClubDetails(int bookClubId, BuildContext context) async {
    try {
      final responseMap = await _bookClubController?.getBookClubs(
        '',
        null,
        bookClubId,
        context,
        null,
        null,
      );

      if (responseMap?["statusCode"] == 200) {
        if (responseMap?["data"].isNotEmpty) {
          final bookClubList = (responseMap?["data"] as List)
              .map((item) => BookClubModel.fromJson(item))
              .toList();

          if (bookClubList.isNotEmpty) {
            _bookClubModel = bookClubList[0];
            _bookClubController?.updateData(_bookClubModel!);
            notifyListeners();
          }
        }
      }
    } catch (e) {
      log('Error getting club details: $e');
    }
  }

  /// Get book club members - targeted refresh method
  Future<void> getBookClubMembers(int bookClubId, BuildContext context) async {
    _isMembersLoading = true;
    notifyListeners();

    try {
      final responseMap = await _bookClubController?.getBookClubMembers(
        bookClubId,
        context,
      );

      if (responseMap?["statusCode"] == 200) {
        _memberList = (responseMap?["data"] as List)
            .map((item) => ClubMembershipModel.fromJson(item))
            .toList();

        _memberIdsList = _memberList?.map((e) => e.userId ?? 0).toList() ?? [];
        _userHandles =
            _memberList?.map((e) => {e.userId: e.userHandle}).toList() ?? [];
        _userProfilePicture = _memberList
                ?.map((e) => {e.userId: e.userProfilePicture})
                .toList() ??
            [];

        // Update book club controller with member data
        _bookClubController?.updateUserHandles(_userHandles);
        _bookClubController?.initializeIds(_memberIdsList ?? []);
        _bookClubController?.updateUserProfilePicture(_userProfilePicture);
      }
    } catch (e) {
      log('Error getting book club members: $e');
    } finally {
      _isMembersLoading = false;
      notifyListeners();
    }
  }

  /// Get upcoming meetings with pagination support
  Future<void> getUpcomingMeetings(
      int bookClubId, bool isLoadMore, BuildContext context) async {
    if (isLoadMore &&
        (_upcomingMeetings?.length ?? 0) >= _upcomingMeetingCount) {
      return; // No more data to load
    }

    _isUpcomingMeetingsLoading = true;
    if (isLoadMore) {
      _upcomingMeetingLimit += 10;
    }
    notifyListeners();

    try {
      await _bookClubController?.getBookClubUpcomingMeetings(
        bookClubId,
        _upcomingMeetingLimit,
        _offset,
        context,
      );

      _upcomingMeetingCount =
          _bookClubController?.upcomingMeetingModel?.count ?? 0;
      _upcomingMeetings = _bookClubController?.upcomingMeetingModel?.data;
    } catch (e) {
      log('Error getting upcoming meetings: $e');
    } finally {
      _isUpcomingMeetingsLoading = false;
      notifyListeners();
    }
  }

  /// Get previous meetings with pagination support
  Future<void> getPreviousMeetings(
      int bookClubId, bool isLoadMore, BuildContext context) async {
    if (isLoadMore &&
        (_previousMeetings?.length ?? 0) >= _previousMeetingCount) {
      return; // No more data to load
    }

    _isPreviousMeetingsLoading = true;
    if (isLoadMore) {
      _previousMeetingLimit += 10;
    }
    notifyListeners();

    try {
      await _bookClubController?.getBookClubPreviousMeetings(
        bookClubId,
        _previousMeetingLimit,
        _offset,
        context,
      );

      _previousMeetingCount =
          _bookClubController?.previousMeetingModel?.count ?? 0;
      _previousMeetings = _bookClubController?.previousMeetingModel?.data;
    } catch (e) {
      log('Error getting previous meetings: $e');
    } finally {
      _isPreviousMeetingsLoading = false;
      notifyListeners();
    }
  }

  /// Get incoming requests - targeted refresh method
  Future<void> getIncomingRequest(int bookClubId, BuildContext context) async {
    try {
      await _bookClubController?.inComingRequest(
        bookClubId,
        ClubMembershipStatus.pending,
        '',
        ClubRequestType.incomingRequestByClubId,
        context,
      );

      _incomingRequestList = _bookClubController?.incomingOutGoingRequest?.data;

      // Update message controller with request status
      await _messageController?.updateStandingClubRequests(
        _incomingRequestList?.isNotEmpty ?? false,
      );
      await _messageController?.manageIncomingRequestStatus(
        _incomingRequestList?.isNotEmpty ?? false,
        context,
      );

      _bookClubController?.incomingRequestFunction(_incomingRequestList);
      notifyListeners();
    } catch (e) {
      log('Error getting incoming requests: $e');
    }
  }

  /// Join meeting functionality
  Future<bool> joinMeeting(int meetingId, String channelName) async {
    _isJoinMeetingLoading = true;
    notifyListeners();

    try {
      final result = await _bookClubController?.joinMeeting(
        meetingId,
        _loggedInUserId ?? 0,
        channelName,
      );

      if (result == true) {
        _meetingJoinModel = _bookClubController?.meetingJoinModel;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      log('Error joining meeting: $e');
      return false;
    } finally {
      _isJoinMeetingLoading = false;
      notifyListeners();
    }
  }

  /// Remove member from club
  Future<bool> removeMember(
      int bookClubId, int memberIndex, BuildContext context) async {
    try {
      final member = _memberList?[memberIndex];
      if (member == null) return false;

      final payload = RequestManage(
        bookClubId: bookClubId,
        bookClubMemberId: member.bookClubMemberId,
        userId: member.userId,
        initiatedBy: _loggedInUserId,
        status: ClubMembershipStatus.left,
      );

      final result =
          await _bookClubController?.updateInvitation(payload, context);

      if (result?["statusCode"] == 200) {
        // Remove member from local list
        _memberList?.removeAt(memberIndex);

        // Notify other screens about the club membership change
        final clubType = _bookClubModel?.bookClubType ?? '';
        ClubsSyncService().onUserLeftClub(bookClubId, clubType);

        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      log('Error removing member: $e');
      return false;
    }
  }

  /// Check if current user is club leader
  bool isCurrentUserLeader() {
    return _bookClubModel?.userId == _loggedInUserId;
  }

  /// Get member profile picture URL
  String getMemberProfilePictureUrl(int index) {
    final member = _memberList?[index];
    if (member?.userProfilePicture != null) {
      return '${Config.imageBaseUrl}${member!.userProfilePicture!}';
    }
    return AppConstants.profileLogoImagePath;
  }

  /// Refresh specific section
  Future<void> refreshSection(
      String section, int bookClubId, BuildContext context) async {
    switch (section) {
      case 'members':
        await getBookClubMembers(bookClubId, context);
        break;
      case 'upcoming_meetings':
        _upcomingMeetingLimit = 10; // Reset pagination
        await getUpcomingMeetings(bookClubId, false, context);
        break;
      case 'previous_meetings':
        _previousMeetingLimit = 10; // Reset pagination
        await getPreviousMeetings(bookClubId, false, context);
        break;
      case 'incoming_requests':
        await getIncomingRequest(bookClubId, context);
        break;
      case 'club_details':
        await getClubDetails(bookClubId, context);
        break;
    }
  }
}
