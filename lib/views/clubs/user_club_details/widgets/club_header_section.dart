import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/views/clubs/user_club_details/providers/user_club_details_provider.dart';

/// Widget for displaying club header with charter and admin buttons
class ClubHeaderSection extends StatelessWidget {
  final int bookClubId;

  const ClubHeaderSection({
    super.key,
    required this.bookClubId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<UserClubDetailsProvider>(
      builder: (context, provider, child) {
        final isLeader = provider.isCurrentUserLeader();
        final bookClubName = provider.bookClubModel?.bookClubName ?? '';
        final clubCharter = provider.bookClubModel?.clubCharter ?? '';

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: isLeader ? _buildLeaderHeader(
            context,
            bookClubName,
            clubCharter,
            provider,
          ) : _buildMemberHeader(
            context,
            bookClubName,
            clubCharter,
          ),
        );
      },
    );
  }

  /// Build header for club leaders with admin options
  Widget _buildLeaderHeader(
    BuildContext context,
    String bookClubName,
    String clubCharter,
    UserClubDetailsProvider provider,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildCharterButton(context, bookClubName, clubCharter),
        _buildLeaderAdminButton(context, provider),
      ],
    );
  }

  /// Build header for regular members
  Widget _buildMemberHeader(
    BuildContext context,
    String bookClubName,
    String clubCharter,
  ) {
    return _buildCharterButton(context, bookClubName, clubCharter, isFullWidth: true);
  }

  /// Build club charter button
  Widget _buildCharterButton(
    BuildContext context,
    String bookClubName,
    String clubCharter, {
    bool isFullWidth = false,
  }) {
    return NetworkAwareTap(
      onTap: () {
        context.pushNamed('club-charter', extra: {
          'bookClubName': bookClubName,
          'clubCharter': clubCharter,
        });
      },
      child: Container(
        height: 45,
        width: isFullWidth 
            ? MediaQuery.of(context).size.width
            : MediaQuery.of(context).size.width / 2.4,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(49),
          color: AppConstants.textGreenColor,
        ),
        child: Center(
          child: Text(
            "Club Charter",
            textAlign: TextAlign.center,
            style: lbBold.copyWith(
              fontSize: 18,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  /// Build leader admin button with notification indicator
  Widget _buildLeaderAdminButton(
    BuildContext context,
    UserClubDetailsProvider provider,
  ) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        NetworkAwareTap(
          onTap: () {
            // Pass meeting data to the admin screen
            Provider.of<UserClubDetailsProvider>(context, listen: false)
                .bookClubController
                ?.meeting(
                  provider.upcomingMeetings,
                  provider.previousMeetings,
                );
            
            context.pushNamed('clubsScreen4', queryParameters: {
              'bookClubId': bookClubId.toString(),
            });
          },
          child: Container(
            height: 45,
            width: MediaQuery.of(context).size.width / 2.4,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(49),
              color: AppConstants.textGreenColor,
            ),
            child: Center(
              child: Text(
                "Leader Admin",
                textAlign: TextAlign.center,
                style: lbBold.copyWith(
                  fontSize: 18,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
          ),
        ),
        // Notification indicator
        if (provider.hasNewStandingClubRequest)
          Positioned(
            top: -8,
            right: 10,
            child: Image.asset(
              AppConstants.notificationImagePath,
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
              height: 18,
              width: 18,
            ),
          ),
      ],
    );
  }
}
