import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/views/clubs/user_club_details/providers/user_club_details_provider.dart';
import 'package:eljunto/views/clubs/user_club_details/widgets/meeting_card.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// Widget for displaying upcoming meetings section
class UpcomingMeetingsSection extends StatefulWidget {
  final int bookClubId;

  const UpcomingMeetingsSection({
    super.key,
    required this.bookClubId,
  });

  @override
  State<UpcomingMeetingsSection> createState() =>
      _UpcomingMeetingsSectionState();
}

class _UpcomingMeetingsSectionState extends State<UpcomingMeetingsSection> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll for pagination
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      final provider =
          Provider.of<UserClubDetailsProvider>(context, listen: false);
      if (!provider.isUpcomingMeetingsLoading &&
          (provider.upcomingMeetings?.length ?? 0) <
              provider.upcomingMeetingCount) {
        provider.getUpcomingMeetings(widget.bookClubId, true, context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<UserClubDetailsProvider>(
      builder: (context, provider, child) {
        final upcomingMeetings = provider.upcomingMeetings;
        final hasUpcomingMeetings = upcomingMeetings?.isNotEmpty ?? false;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (hasUpcomingMeetings) ...[
              _buildSectionHeader(),
              const SizedBox(height: 10),
              _buildMeetingsList(provider),
            ] else ...[
              _buildNoDataWidget(provider),
            ],
          ],
        );
      },
    );
  }

  /// Build section header
  Widget _buildSectionHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            "Upcoming Meetings",
            style: lbRegular.copyWith(
              fontSize: 20,
              color: AppConstants.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Build horizontal scrollable meetings list with dynamic height
  Widget _buildMeetingsList(UserClubDetailsProvider provider) {
    final upcomingMeetings = provider.upcomingMeetings!;
    final isLoading = provider.isUpcomingMeetingsLoading;

    return SizedBox(
      height: 253,
      child: ListView.builder(
        physics: const ClampingScrollPhysics(),
        controller: _scrollController,
        padding: const EdgeInsets.only(left: 10, right: 20),
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount:
            isLoading ? upcomingMeetings.length + 1 : upcomingMeetings.length,
        itemBuilder: (context, index) {
          // Show loading indicator at the end
          if (index == upcomingMeetings.length && isLoading) {
            return const Padding(
              padding: EdgeInsets.only(left: 10.0),
              child: Center(
                child: CircularProgressIndicator(
                  color: AppConstants.primaryColor,
                ),
              ),
            );
          }

          final meeting = upcomingMeetings[index];
          return MeetingCard(
            meeting: meeting,
            meetingIndex: index,
            bookClubId: widget.bookClubId,
            isUpcoming: true,
          );
        },
      ),
    );
  }

  /// Build no data widget with skeleton loading
  Widget _buildNoDataWidget(UserClubDetailsProvider provider) {
    if (provider.isInitialLoading) {
      return Container(
        padding: const EdgeInsets.only(left: 20),
        margin: const EdgeInsets.symmetric(horizontal: 20),
        height: 50,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: AppConstants.skeletonBackgroundColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: const Align(
          alignment: Alignment.centerLeft,
          child: Text(
            "Loading...",
            textAlign: TextAlign.start,
          ),
        ),
      );
    }

    return NoDataWidget(message: 'No upcoming meetings');
  }
}
