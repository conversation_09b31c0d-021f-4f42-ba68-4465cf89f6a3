import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';

/// Widget for displaying section headers with "New" button
class SectionHeader extends StatelessWidget {
  final String title;
  final String clubType;

  const SectionHeader({
    super.key,
    required this.title,
    required this.clubType,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: lbRegular.copyWith(fontSize: 20),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 16),
          _buildNewButton(context),
        ],
      ),
    );
  }

  /// Build the "New" button for creating clubs
  Widget _buildNewButton(BuildContext context) {
    return NetworkAwareTap(
      onTap: () {
        context.pushNamed("NewClubScreen", extra: clubType);
      },
      child: Skeleton.replace(
        replacement: _buildNewButtonSkeleton(),
        child: Container(
          constraints: const BoxConstraints(
            minHeight: 30,
            minWidth: 85,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(90),
            border: Border.all(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
          ),
          child: IntrinsicHeight(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 6.0,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Flexible(
                    child: Text(
                      "New",
                      style: lbBold.copyWith(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 6),
                  const Icon(
                    Icons.add_circle_outline,
                    size: 18,
                    color: AppConstants.primaryColor,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build skeleton for the "New" button
  Widget _buildNewButtonSkeleton() {
    return Container(
      constraints: const BoxConstraints(
        minHeight: 30,
        minWidth: 85,
      ),
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(90),
      ),
      child: IntrinsicHeight(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 12.0,
            vertical: 6.0,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  "New",
                  style: lbBold.copyWith(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 6),
              const Icon(
                Icons.add_circle_outline,
                size: 18,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
