import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';

/// Global service for synchronizing clubs data across screens
/// Handles cross-screen state management and data consistency
class ClubsSyncService {
  static final ClubsSyncService _instance = ClubsSyncService._internal();
  factory ClubsSyncService() => _instance;
  ClubsSyncService._internal();

  // Stream controllers for cross-screen communication
  final StreamController<ClubSyncEvent> _syncController =
      StreamController<ClubSyncEvent>.broadcast();
  final StreamController<bool> _refreshController =
      StreamController<bool>.broadcast();

  // Getters for streams
  Stream<ClubSyncEvent> get syncStream => _syncController.stream;
  Stream<bool> get refreshStream => _refreshController.stream;

  // Cache for clubs data to avoid redundant API calls
  final Map<String, dynamic> _clubsCache = {};
  DateTime? _lastCacheUpdate;
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  /// Notify all listening screens about club data changes
  void notifyClubDataChanged(ClubSyncEventType type,
      {Map<String, dynamic>? data}) {
    log('ClubsSyncService: Notifying club data changed - $type');
    final event =
        ClubSyncEvent(type: type, data: data, timestamp: DateTime.now());
    _syncController.add(event);
  }

  /// Request refresh of clubs data across all screens
  void requestRefresh() {
    log('ClubsSyncService: Requesting refresh across all screens');
    _refreshController.add(true);
    _invalidateCache();
  }

  /// Cache clubs data to avoid redundant API calls
  void cacheClubsData(String key, dynamic data) {
    _clubsCache[key] = data;
    _lastCacheUpdate = DateTime.now();
    log('ClubsSyncService: Cached data for key: $key');
  }

  /// Get cached clubs data if still valid
  T? getCachedData<T>(String key) {
    if (_isCacheValid() && _clubsCache.containsKey(key)) {
      log('ClubsSyncService: Returning cached data for key: $key');
      return _clubsCache[key] as T?;
    }
    return null;
  }

  /// Check if cache is still valid
  bool _isCacheValid() {
    if (_lastCacheUpdate == null) return false;
    return DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// Invalidate cache to force fresh data fetch
  void invalidateCache() {
    _clubsCache.clear();
    _lastCacheUpdate = null;
    log('ClubsSyncService: Cache invalidated');
  }

  /// Private method for internal use
  void _invalidateCache() {
    invalidateCache();
  }

  /// Handle user leaving a club
  void onUserLeftClub(int clubId, String clubType) {
    notifyClubDataChanged(
      ClubSyncEventType.userLeftClub,
      data: {'clubId': clubId, 'clubType': clubType},
    );
  }

  /// Handle club data updates from admin screens
  void onClubDataUpdated(
      int clubId, String clubType, Map<String, dynamic> updatedData) {
    notifyClubDataChanged(
      ClubSyncEventType.clubDataUpdated,
      data: {
        'clubId': clubId,
        'clubType': clubType,
        'updatedData': updatedData,
      },
    );
  }

  /// Handle new club creation
  void onClubCreated(String clubType, Map<String, dynamic> clubData) {
    notifyClubDataChanged(
      ClubSyncEventType.clubCreated,
      data: {'clubType': clubType, 'clubData': clubData},
    );
  }

  /// Handle club membership changes
  void onMembershipChanged(int clubId, String clubType, String changeType) {
    notifyClubDataChanged(
      ClubSyncEventType.membershipChanged,
      data: {
        'clubId': clubId,
        'clubType': clubType,
        'changeType': changeType,
      },
    );
  }

  /// Handle invitation status changes
  void onInvitationStatusChanged(
      String invitationType, bool hasNewInvitations) {
    notifyClubDataChanged(
      ClubSyncEventType.invitationStatusChanged,
      data: {
        'invitationType': invitationType,
        'hasNewInvitations': hasNewInvitations,
      },
    );
  }

  /// Dispose resources
  void dispose() {
    _syncController.close();
    _refreshController.close();
    _clubsCache.clear();
  }
}

/// Event types for cross-screen synchronization
enum ClubSyncEventType {
  userLeftClub,
  clubDataUpdated,
  clubCreated,
  membershipChanged,
  invitationStatusChanged,
  generalRefresh,
}

/// Event data structure for cross-screen communication
class ClubSyncEvent {
  final ClubSyncEventType type;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  ClubSyncEvent({
    required this.type,
    this.data,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'ClubSyncEvent{type: $type, data: $data, timestamp: $timestamp}';
  }
}

/// Mixin for screens that need to listen to club sync events
mixin ClubSyncListener<T extends StatefulWidget> on State<T> {
  StreamSubscription<ClubSyncEvent>? _syncSubscription;
  StreamSubscription<bool>? _refreshSubscription;

  /// Initialize sync listeners
  void initializeSyncListeners() {
    _syncSubscription = ClubsSyncService().syncStream.listen(onClubSyncEvent);
    _refreshSubscription =
        ClubsSyncService().refreshStream.listen(onRefreshRequested);
  }

  /// Handle club sync events - override in implementing classes
  void onClubSyncEvent(ClubSyncEvent event) {
    log('ClubSyncListener: Received sync event - ${event.type}');
  }

  /// Handle refresh requests - override in implementing classes
  void onRefreshRequested(bool shouldRefresh) {
    log('ClubSyncListener: Refresh requested');
  }

  /// Dispose sync listeners
  void disposeSyncListeners() {
    _syncSubscription?.cancel();
    _refreshSubscription?.cancel();
  }

  @override
  void dispose() {
    disposeSyncListeners();
    super.dispose();
  }
}
