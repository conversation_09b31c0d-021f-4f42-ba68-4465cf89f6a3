import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dash_chat_2/dash_chat_2.dart';
import 'package:eljunto/constants/app_config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/controller/connectivity_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/reusableWidgets/emoji/custom_emoji_picker.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:eljunto/services/setup_locator.dart';
import 'package:eljunto/views/local_database.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart' as emoji;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:synchronized/synchronized.dart';

import '../../constants/common_helper.dart';
import '../../constants/config.dart';
import '../../constants/text_style.dart';
import '../../controller/book_club_controller.dart';
import '../../models/club_membership_model.dart';
import '../../reusableWidgets/message_app_bar.dart';

class FireBaseChat extends StatefulWidget {
  // final int? loggedInUserId;
  final String? bookClubName;
  final int? bookClubId;

  // Pass in the current user at login

  const FireBaseChat({
    // required this.loggedInUserId,
    super.key,
    this.bookClubName,
    this.bookClubId,
  });

  @override
  State createState() => _FireBaseChatState();
}

class _FireBaseChatState extends State<FireBaseChat> {
  final _messageCache = MessageCacheManager();
  static const int _messagesPerPage = 20;
  bool _isLoadingMore = false;
  DocumentSnapshot? _lastDocument;
  ChatUser loggedInUser = ChatUser(id: '0');

  // final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final String _firebaseServerUrl = AppConfig.shared.flavor == Flavor.dev
      ? 'https://fcm.googleapis.com/v1/projects/el-junto-development-server/messages:send'
      : 'https://fcm.googleapis.com/v1/projects/el-junto-4fb80/messages:send';

  // final String _serviceAccountFilePath =
  //     'assets/el-junto-4fb80-firebase-adminsdk-gwtik-f421cef23d.json';

  late CollectionReference _messagesCollection;
  StreamSubscription? _messageSubscription;
  StreamSubscription? _metadataSubscription;
  int? loggedInUserId;
  Map<String, dynamic>? selectedEmojis = {};
  // final ScrollController _scrollController = ScrollController();

  Future<void> _initializeUserId() async {
    loggedInUserId = await CommonHelper.getLoggedInUserId();
    await getBookClubMembers();
  }

  String? fcmToken;
  bool _isInternetConnected = true;
  final provider = locator<ConnectivityProvider>();

  @override
  void initState() {
    _setupConnectivityListener();
    super.initState();
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {});
    // Add scroll listener for pagination
    // _scrollController.addListener(_scrollListener);
    _initializeAndSetup();
  }

  // Add this method to handle scroll events
  // void _scrollListener() {
  //   if (_scrollController.position.pixels >=
  //           _scrollController.position.maxScrollExtent * 0.7 &&
  //       !_isLoadingMore) {
  //     _loadMoreMessages();
  //   }
  // }

  Future<void> _initializeAndSetup() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    fcmToken = pref.getString('fcmToken');

    try {
      // Initialize user ID and fetch book club members
      await _initializeUserId();

      // Dynamically set the collection reference using bookClubId
      if (widget.bookClubId != null) {
        _messagesCollection = FirebaseFirestore.instance
            .collection('Club_Collection')
            .doc('${widget.bookClubId}')
            .collection('messages');
        markMessagesAsSeen(); // Unique collection for each book club
      } else {
        throw Exception("BookClubId is required to load chat messages.");
      }

      loggedInUser = ChatUser(
        id: loggedInUserId.toString(),
      );

      // Start listening to messages only after members are loaded
      receiveMessage();
      receiveMetadata();
    } catch (e) {
      log("Error initializing chat: ${e.toString()}");
      // Handle initialization errors here (e.g., show a Snackbar or error UI)
    }
  }

  void _setupConnectivityListener() {
    provider.statusStream.listen(
      (status) => _isInternetConnected = status == InternetStatus.connected,
    );
  }

  final _lock = Lock();

  /// GET MESSAGE FROM LOCALDATABASE AND FECH ON FIREBASE TO STORE LOCAL DATABASE IMPLEMENT 12 DEC 2024
  // Future<void> receiveMessage() async {
  //   // First, load messages from local database immediately
  //   await _lock.synchronized(() async {
  //     final localMessages = await DatabaseHelper.instance
  //         .getMessagesByBookClub(widget.bookClubId.toString(), orderBy: 'DESC');
  //     setState(() {
  //       chatMessages = localMessages;
  //       isLoading = true; // Indicate we're fetching fresh data
  //     });
  //   });

  //   _messageSubscription = _messagesCollection
  //       .orderBy('createdAt', descending: true)
  //       .snapshots()
  //       .listen((snapshot) async {
  //     List<ChatMessage> fetchedMessages = snapshot.docs.map((doc) {
  //       final data = doc.data() as Map;

  //       // Your existing message processing logic
  //       final text = data['text'] as String? ?? "";
  //       final userId = data['user_id'];
  //       final createdAt = data['createdAt'] != null
  //           ? (data['createdAt'] as Timestamp).toDate()
  //           : DateTime.now();

  //       // Existing user matching logic...
  //       final matchingUser = memberList?.firstWhere(
  //         (element) => element.userId == userId,
  //         orElse: () => ClubMembershipModel(),
  //       );

  //       final memberProfilePicture = matchingUser?.userProfilePicture != null
  //           ? Config.imageBaseUrl + (matchingUser?.userProfilePicture ?? '')
  //           : AppConstants.profileLogoImagePath;

  //       // Check and update 'isSeen' for the current user
  //       List<dynamic>? seenBy = data['seenBy'] as List<dynamic>?;
  //       if (seenBy != null) {
  //         final currentUserSeenStatus = seenBy.firstWhere(
  //           (entry) => entry['user_id'] == loggedInUserId,
  //           orElse: () => null,
  //         );

  //         if (currentUserSeenStatus != null &&
  //             currentUserSeenStatus['isSeen'] == false) {
  //           doc.reference.update({
  //             'seenBy': seenBy.map((entry) {
  //               if (entry['user_id'] == loggedInUserId) {
  //                 return {
  //                   'user_id': entry['user_id'],
  //                   'isSeen': true,
  //                 };
  //               }
  //               return entry;
  //             }).toList(),
  //           });
  //         }
  //       }
  //       final messageType = data['message_type'];
  //       selectedEmojis = data['reactions'];

  //       // Create ChatMessage
  //       final chatMessage = ChatMessage(
  //         text: text,
  //         user: userId == loggedInUser.id
  //             ? loggedInUser
  //             : ChatUser(
  //                 customProperties: {
  //                   "seenBy": seenBy,
  //                   "message_type": messageType,
  //                 },
  //                 firstName: matchingUser?.userHandle,
  //                 id: userId.toString(),
  //                 profileImage: memberProfilePicture,
  //               ),
  //         createdAt: createdAt,
  //         customProperties: {
  //           "message_id": doc.id, // "messageId"
  //           "seenBy": seenBy,
  //           "message_type": messageType,
  //           "reactions": selectedEmojis,
  //         },
  //       );

  //       _lock.synchronized(() async {
  //         // Store message locally
  //         DatabaseHelper.instance
  //             .insertLocallyMessage(chatMessage, widget.bookClubId ?? 0);
  //       });
  //       return chatMessage;
  //     }).toList();

  //     setState(() {
  //       chatMessages = fetchedMessages;
  //       isLoading = false;
  //     });
  //   });
  // }

  // Optimize message loading with pagination and caching
  Future<void> receiveMessage() async {
    await _lock.synchronized(() async {
      // Check cache first for immediate display
      final cachedMessages =
          _messageCache.getMessages(widget.bookClubId.toString());
      if (cachedMessages != null && cachedMessages.isNotEmpty) {
        setState(() {
          chatMessages = cachedMessages;
          isLoading = false;
        });
      }

      // Load from local database if cache is empty
      if (cachedMessages == null || cachedMessages.isEmpty) {
        final localMessages = await DatabaseHelper.instance
            .getMessagesByBookClub(widget.bookClubId.toString(),
                orderBy: 'DESC', limit: _messagesPerPage);

        if (localMessages.isNotEmpty) {
          setState(() {
            chatMessages = localMessages;
            isLoading = true; // Still loading from Firebase
          });
          _messageCache.updateMessages(
              widget.bookClubId.toString(), localMessages);
        }
      }

      // Setup Firebase listener with pagination
      _messageSubscription = _messagesCollection
          .orderBy('createdAt', descending: true)
          .limit(_messagesPerPage)
          .snapshots()
          .listen((snapshot) async {
        if (snapshot.docs.isNotEmpty) {
          _lastDocument = snapshot.docs.last;

          // Process messages in parallel for better performance
          final fetchedMessages = await Future.wait(
              snapshot.docs.map((doc) => _processMessage(doc)).toList());

          // Update cache and UI
          _messageCache.updateMessages(
              widget.bookClubId.toString(), fetchedMessages);

          if (mounted) {
            setState(() {
              chatMessages = fetchedMessages;
              isLoading = false;
            });
          }

          // Batch update local database in background
          _updateLocalDatabase(fetchedMessages);
        }
      });
    });
  }

  // Extract message processing to a separate method
  Future<ChatMessage> _processMessage(DocumentSnapshot doc) async {
    final data = doc.data() as Map;
    final text = data['text'] as String? ?? "";
    final userId = data['user_id'];
    final createdAt = data['createdAt'] != null
        ? (data['createdAt'] as Timestamp).toDate()
        : DateTime.now();

    // Optimize user matching with caching
    final matchingUser = _findUserInMemberList(userId);
    final memberProfilePicture = _getUserProfilePicture(matchingUser);

    // Process seen status
    await _processSeen(doc, data, userId);

    // Safely handle potentially missing fields
    final messageType = data['message_type'] ?? 'text';
    selectedEmojis = data['reactions'] as Map<String, dynamic>? ?? {};

    // Create ChatMessage
    return ChatMessage(
      text: text,
      user: userId == loggedInUser.id
          ? loggedInUser
          : ChatUser(
              customProperties: {
                "seenBy": data['seenBy'] ?? [],
                "message_type": messageType,
              },
              firstName: matchingUser?.userHandle ?? 'Unknown User',
              id: userId?.toString() ?? '0',
              profileImage: memberProfilePicture,
            ),
      createdAt: createdAt,
      customProperties: {
        "message_id": doc.id,
        "seenBy": data['seenBy'] ?? [],
        "message_type": messageType,
        "reactions": selectedEmojis,
      },
    );
  }

  // Helper methods for better code organization
  ClubMembershipModel? _findUserInMemberList(dynamic userId) {
    return memberList?.firstWhere(
      (element) => element.userId == userId,
      orElse: () => ClubMembershipModel(),
    );
  }

  String _getUserProfilePicture(ClubMembershipModel? user) {
    return user?.userProfilePicture != null
        ? Config.imageBaseUrl + (user?.userProfilePicture ?? '')
        : AppConstants.profileLogoImagePath;
  }

  Future<void> _processSeen(
      DocumentSnapshot doc, Map data, dynamic userId) async {
    try {
      List<dynamic>? seenBy = data['seenBy'] as List<dynamic>?;
      if (seenBy != null) {
        final currentUserSeenStatus = seenBy.firstWhere(
          (entry) => entry['user_id'] == loggedInUserId,
          orElse: () => null,
        );

        if (currentUserSeenStatus != null &&
            currentUserSeenStatus['isSeen'] == false) {
          // Update Firebase in background
          doc.reference.update({
            'seenBy': seenBy.map((entry) {
              if (entry['user_id'] == loggedInUserId) {
                return {
                  'user_id': entry['user_id'],
                  'isSeen': true,
                };
              }
              return entry;
            }).toList(),
          });

          // Update local database in parallel
          await DatabaseHelper.instance.insertOrUpdateSeenStatus(
              loggedInUserId ?? 0, widget.bookClubId.toString(), true);
        }
      }
    } catch (e) {
      log('Error processing seen status: $e');
      // Continue execution even if seen status processing fails
    }
  }

  // Batch update local database
  Future<void> _updateLocalDatabase(List<ChatMessage> messages) async {
    // Use a background isolate or compute for better performance
    await _lock.synchronized(() async {
      await DatabaseHelper.instance
          .batchInsertMessages(messages, widget.bookClubId ?? 0);
    });
  }

  // Load more messages when scrolling
  Future<void> _loadMoreMessages() async {
    if (_isLoadingMore || _lastDocument == null) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final snapshot = await _messagesCollection
          .orderBy('createdAt', descending: true)
          .startAfterDocument(_lastDocument!)
          .limit(_messagesPerPage)
          .get();

      if (snapshot.docs.isNotEmpty) {
        _lastDocument = snapshot.docs.last;

        // Process messages with error handling
        final newMessages = await Future.wait(
          snapshot.docs.map((doc) async {
            try {
              return await _processMessage(doc);
            } catch (e) {
              log('Error processing message ${doc.id}: $e');
              // Return a placeholder message for corrupted documents
              return ChatMessage(
                text: "Message could not be loaded",
                user: ChatUser(id: '0'),
                createdAt: DateTime.now(),
                customProperties: {
                  "message_id": doc.id,
                  "message_type": "system",
                },
              );
            }
          }).toList(),
        );

        // Update local database in background
        _updateLocalDatabase(newMessages);

        if (mounted) {
          setState(() {
            chatMessages.addAll(newMessages);
            // _isLoadingMore = false;
          });
        }
      }
      // else {
      //   setState(() {
      //     _isLoadingMore = false;
      //   });
      // }
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      log('Error loading more messages: $e');
      setState(() {
        _isLoadingMore = false;
      });
    }
    // }
  }

  // Optimize message sending with optimistic updates
  void onSend(ChatMessage message) async {
    // Add message to UI immediately for better UX
    setState(() {
      chatMessages.insert(0, message);
    });

    try {
      // Find sender details
      final matchingUser = memberList?.firstWhere(
        (element) => element.userId.toString() == loggedInUserId.toString(),
      );

      final userProfile =
          Config.imageBaseUrl + (matchingUser?.userProfilePicture ?? '');

      // Prepare message data
      final newMessage = {
        'text': message.text,
        'user_id': loggedInUserId,
        'user_name': matchingUser?.userHandle,
        'profile_image': userProfile,
        'createdAt': DateTime.now(),
        'seenBy': memberList?.map((member) {
          return {
            'user_id': member.userId,
            'isSeen': member.userId == loggedInUserId,
          };
        }).toList(),
      };

      // Perform operations in parallel
      await Future.wait([
        // Add to Firestore
        _messagesCollection.add(newMessage),

        // Update metadata
        FirebaseFirestore.instance
            .collection('Club_Collection')
            .doc('${widget.bookClubId}')
            .set({
          'createdAt': DateTime.now(),
          'bookClubId': widget.bookClubId,
          'bookClubName': memberList?[0].bookClubName,
        }, SetOptions(merge: true)),

        // Add to local database
        DatabaseHelper.instance
            .insertLocallyMessage(message, widget.bookClubId ?? 0),
      ]);

      // Send notifications in background
      _sendNotificationsForMessage(message);
    } catch (e) {
      log('Error sending message: $e');
      // Remove message from UI if sending failed
      setState(() {
        chatMessages.removeAt(0);
      });
    }
  }

  // Send notifications in background
  Future<void> _sendNotificationsForMessage(ChatMessage message) async {
    for (final member in memberList ?? []) {
      if (member.userId != loggedInUserId && member.isMsgNotifyEnabled) {
        await _sendNotification(
          fcmTokens: member.fcmTokens,
          title: "New Message from ${memberList?[0].bookClubName}",
          body: message.text,
          bookClubId: member.bookClubId.toString(),
          receiverId: member.userId.toString(),
        );
      }
    }
  }

  @override
  void dispose() {
    _messageCache.clear();
    chatMessages.clear();
    _controller.dispose();
    // _scrollController.dispose();
    _messageSubscription?.cancel();
    _metadataSubscription?.cancel();
    super.dispose();
  }

  Future<void> receiveMetadata() async {
    _metadataSubscription = FirebaseFirestore.instance
        .collection('Club_Collection')
        .doc('${widget.bookClubId}')
        .snapshots()
        .listen((value) async {
      final data = value.data();
      // log('message data: $data');
      // Safely retrieve and handle `createdAt` field
      final Timestamp? createdAtTimestamp = data?['createdAt'];
      final DateTime? createdAt = createdAtTimestamp?.toDate();
      final bookClubName = data?['bookClubName'];
      final bookClubId = data?['bookClubId'];

      // log("BookClubId : $bookClubId");
      // log("Metadata CreatedAt: $createdAt");
      // log("Metadata ClubId: ${data?['bookClubId']}");

      await DatabaseHelper.instance.insertOrUpdateClubMetadata(
          bookClubId, (createdAt ?? DateTime.now()), bookClubName);
    });
  }

  void markMessagesAsSeen() {
    _messagesCollection.get().then((snapshot) {
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final List seenBy = data['seenBy'] ?? [];
        // log('isSeen: $seenBy');

        // Update the user's "isSeen" status to true
        for (var item in seenBy) {
          if (item['user_id'] == loggedInUserId) {
            item['isSeen'] = true;
          }
        }

        // Update the document in Firestore
        doc.reference.update({'seenBy': seenBy});
      }
    });
  }

  // void onSend(ChatMessage message) async {
  //   // Find the sender's details
  //   final matchingUser = memberList?.firstWhere(
  //     (element) => element.userId.toString() == loggedInUserId.toString(),
  //   );
  //   // Add the message to the UI immediately
  //   setState(() {
  //     chatMessages.insert(0, message);
  //   });
  //   final userProfile =
  //       Config.imageBaseUrl + (matchingUser?.userProfilePicture ?? '');
  //   print("Value : ${memberList?.map((member) {
  //     return {
  //       'user_id': member.userId,
  //       'isSeen': member.userId == loggedInUserId, // Set true for sender
  //     };
  //   }).toList()}");
  //   // Create the message document
  //   final newMessage = {
  //     'text': message.text,
  //     'user_id': loggedInUserId,
  //     'user_name': matchingUser?.userHandle,
  //     'profile_image': userProfile,
  //     'createdAt': DateTime.now(),
  //     'seenBy': memberList?.map((member) {
  //       return {
  //         'user_id': member.userId,
  //         'isSeen': member.userId == loggedInUserId, // Set true for sender
  //       };
  //     }).toList(),
  //   };

  //   // Add the message to Firestore
  //   await _messagesCollection.add(newMessage);

  //   // Update metadata in Club_Collection if needed
  //   await FirebaseFirestore.instance
  //       .collection('Club_Collection')
  //       .doc('${widget.bookClubId}')
  //       .set(
  //     {
  //       'createdAt': DateTime.now(),
  //       'bookClubId': widget.bookClubId,
  //       'bookClubName': memberList?[0].bookClubName,
  //     },
  //     SetOptions(merge: true),
  //   );

  //   /// FOR SEND NOTIFICATION TO OTHER USERS

  //   for (final member in memberList!) {
  //     log('member UserId : ${member.userId}, member Send Message : ${member.isMsgNotifyEnabled}');
  //     if (member.userId != loggedInUserId && member.isMsgNotifyEnabled) {
  //       log('fcm tokens: ${member.fcmTokens}');
  //       await _sendNotification(
  //         fcmTokens: member.fcmTokens,
  //         title: "New Message from ${member.bookClubName}",
  //         body: message.text,
  //         bookClubId: member.bookClubId.toString(),
  //         receiverId: member.userId.toString(),
  //       );
  //     }
  //   }

  //   _scrollToBottom();
  // }

  Future<void> addEmojiReaction(
      String messageId, String userId, String emoji) async {
    final messageDoc = _messagesCollection.doc(messageId);

    try {
      // Get the current reactions map
      final snapshot = await messageDoc.get();
      final data = snapshot.data() as Map<String, dynamic>?;
      final reactions = data?['reactions'] as Map<String, dynamic>? ?? {};

      // Add or update the emoji for the current user
      reactions[userId] = emoji;

      // Update Firestore with the modified reactions map
      await messageDoc.update({'reactions': reactions});

      log('Emoji reaction added/updated successfully: $emoji');
      final currentUser =
          memberList?.firstWhere((element) => element.userId == loggedInUserId);

      /// SEND FCM NOTIFICATION TO OTHER MEMBERS
      for (final member in memberList ?? <ClubMembershipModel>[]) {
        if (member.userId.toString() != userId) {
          log('Sending FCM notification to: ${member.userId}');
          await _sendNotification(
            fcmTokens: member.fcmTokens,
            title: "New Reaction on a Message in ${member.bookClubName}",
            body: "${currentUser?.userHandle} reacted with $emoji",
            bookClubId: member.bookClubId.toString(),
            receiverId: member.userId.toString(),
          );
        }
      }
    } catch (e) {
      log('Failed to add emoji reaction: $e');
    }
  }

  Future<void> removeEmojiReaction(String messageId, String userId) async {
    final messageDoc = _messagesCollection.doc(messageId);

    try {
      // Get the current reactions map
      final snapshot = await messageDoc.get();
      final data = snapshot.data() as Map<String, dynamic>?;
      final reactions = data?['reactions'] as Map<String, dynamic>? ?? {};

      // Check if the user's reaction exists and remove it
      if (reactions.containsKey(userId)) {
        reactions.remove(userId);

        // Update Firestore with the modified reactions map
        await messageDoc.update({'reactions': reactions});

        log('Emoji reaction removed successfully for user: $userId');
      } else {
        log('No reaction found for user: $userId');
      }
    } catch (e) {
      log('Failed to remove emoji reaction: $e');
    }
  }

  Future<void> _sendNotification({
    required List<String>? fcmTokens,
    required String title,
    required String body,
    required String bookClubId,
    required String receiverId,
  }) async {
    if (fcmTokens == null) return;

    final url = Uri.parse(_firebaseServerUrl);
    // log('here it is');
    final bearerToken = await getBearerToken(AppConfig.shared.flavor ==
            Flavor.dev
        ? "assets/el-junto-development-server-firebase-adminsdk-vmec3-eb3a739632.json"
        : "assets/el-junto-4fb80-firebase-adminsdk-gwtik-f421cef23d.json");
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $bearerToken',
    };
    for (final token in fcmTokens) {
      final payload = {
        'message': {
          'token': token,
          'notification': {
            'title': title,
            'body': body,
          },
          'data': {
            'sound': 'default',
            'deepLink':
                'https://eljunto.com/chat-screen?userId=$receiverId&bookClubId=$bookClubId',
          },
          "apns": {
            "headers": {
              "apns-priority": "10",
            },
            "payload": {
              "aps": {"sound": "default"}
            }
          },
          "android": {
            "priority": "high",
            "notification": {
              "channel_id": "high_importance_channel",
            }
          },
        },
      };
      log('payload: $payload');

      try {
        final response = await http.post(
          url,
          headers: headers,
          body: json.encode(payload),
        );

        // log('response: ${response.body}');
        // log('response: ${response.statusCode}');

        if (response.statusCode != 200) {
          throw Exception('Failed to send notification: ${response.body}');
        }
      } catch (e) {
        debugPrint('Error sending notification: $e');
      }
    }
  }

  Future<String> getBearerToken(String serviceAccountKeyPath) async {
    // Load the service account JSON file
    // final serviceAccountKey = File(serviceAccountKeyPath).readAsStringSync();
    final serviceAccountKey =
        await rootBundle.loadString(serviceAccountKeyPath);
    final credentials = ServiceAccountCredentials.fromJson(serviceAccountKey);

    // Scopes required for Firebase messaging
    const scopes = ['https://www.googleapis.com/auth/firebase.messaging'];

    // Obtain an authenticated client
    final authClient = await clientViaServiceAccount(credentials, scopes);

    // Extract the Bearer token
    final accessToken = authClient.credentials.accessToken.data;
    // log('accessToken: $accessToken');

    // Close the client to release resources
    authClient.close();

    return accessToken;
  }

  List<ChatMessage> chatMessages = <ChatMessage>[];
  final TextEditingController _controller = TextEditingController();
  bool isLoading = true;

  // @override
  // void dispose() {
  //   chatMessages.clear();
  //   _controller.dispose();
  //   _scrollController.dispose();
  //   _messageSubscription?.cancel();
  //   _metadataSubscription?.cancel();
  //   super.dispose();
  // }

  List<ClubMembershipModel>? memberList;

  Future<void> getBookClubMembers() async {
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubMembers(widget.bookClubId ?? 0, context)
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        // log(responseMap["data"].toString());
        memberList = (responseMap["data"] as List)
            .map((item) => ClubMembershipModel.fromJson(item))
            .toList();
        // log('tokens: ${memberList?.map((e) => e.fcmTokens).toList()}');
        // print("USER HANDLE : ${memberList?[0].userHandle}");
      } else {}
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: MessageScreenAppBar(
            bookName: memberList?[0].bookClubName,
            isSetProfile: true,
            onTap: () => context.goNamed(
              "message-user-club-details",
              queryParameters: {
                'bookClubId': widget.bookClubId.toString(),
                'userId': loggedInUserId.toString(),
              },
            ),
            clubMembers: memberList,
            // impromptuCount: widget.impromptuCount,
            // showImpromptu: true,
          ),
        ),
      ),
      body: isLoading
          ? Container(
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    AppConstants.bgImagePath,
                  ),
                  filterQuality: FilterQuality.high,
                  fit: BoxFit.cover,
                ),
              ),
              child: const Center(
                child: CircularProgressIndicator(
                  color: AppConstants.primaryColor,
                ),
              ),
            )
          : Container(
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    AppConstants.bgImagePath,
                  ),
                  fit: BoxFit.cover,
                  filterQuality: FilterQuality.high,
                ),
              ),
              child: Stack(
                children: [
                  NoConnectionTag(bottomPosition: 55),
                  DashChat(
                    scrollToBottomOptions: ScrollToBottomOptions(
                      scrollToBottomBuilder: (scrollController) => Align(
                        alignment: Alignment.bottomCenter,
                        child: GestureDetector(
                          onTap: () => scrollController.animateTo(
                            scrollController.position.minScrollExtent,
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeOut,
                          ),
                          child: Container(
                            padding: EdgeInsets.all(5),
                            decoration: BoxDecoration(
                              color: AppConstants.textGreenColor,
                              borderRadius: BorderRadius.circular(50),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.5),
                                  spreadRadius: 2,
                                  blurRadius: 5,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.arrow_downward_outlined,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                    inputOptions: InputOptions(
                      textController: _controller,
                      textCapitalization: TextCapitalization.sentences,
                      sendButtonBuilder: (send) {
                        return Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: IconButton(
                            onPressed: () {
                              if (_isInternetConnected) {
                                send();
                                _controller.clear();
                              }
                            },
                            icon: Icon(
                              Icons.send,
                              color: _isInternetConnected
                                  ? AppConstants.primaryColor
                                  : Colors.grey.withOpacity(.5),
                              size: 30,
                            ),
                          ),
                        );
                      },
                      inputToolbarPadding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      inputToolbarMargin: const EdgeInsets.only(top: 10),
                      inputToolbarStyle: const BoxDecoration(
                        color: Color.fromRGBO(255, 245, 214, 0),
                        border: Border(
                          top: BorderSide(
                            color: Colors.black,
                            width: 1.5,
                          ),
                        ),
                      ),
                      inputTextStyle: lbRegular.copyWith(
                        fontSize: 16,
                        color: AppConstants.primaryColor,
                      ),
                      alwaysShowSend: true,
                      showTraillingBeforeSend: true,
                      cursorStyle: const CursorStyle(
                        color: AppConstants.textGreenColor,
                      ),
                      inputMaxLines: 5,
                      inputDecoration: InputDecoration(
                        labelText: "Message",
                        labelStyle: lbRegular.copyWith(
                          fontSize: 14,
                          color: AppConstants.primaryColor,
                        ),
                        isDense: true,
                        contentPadding: const EdgeInsets.all(10),
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            width: 1.5,
                            color: AppConstants.primaryColor,
                          ),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        border: OutlineInputBorder(
                          borderSide: const BorderSide(
                            width: 1.5,
                            color: AppConstants.primaryColor,
                          ),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            width: 1.5,
                            color: AppConstants.primaryColor,
                          ),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        filled: true,
                        fillColor: const Color.fromRGBO(255, 245, 214, 0),
                      ),
                    ),
                    messageListOptions: MessageListOptions(
                      // scrollController: _scrollController,
                      dateSeparatorBuilder: (date) {
                        final now = DateTime.now();
                        final today = DateTime(now.year, now.month, now.day);
                        final messageDate =
                            DateTime(date.year, date.month, date.day);
                        final difference = today.difference(messageDate).inDays;

                        String formattedDate;
                        if (difference == 0) {
                          formattedDate = 'Today';
                        } else if (difference == 1) {
                          formattedDate = 'Yesterday';
                        } else {
                          formattedDate =
                              DateFormat('EEE, MMM dd, yyyy').format(date);
                        }

                        return Center(
                          child: Padding(
                            padding: const EdgeInsets.only(
                              left: 10.0,
                              top: 10,
                              right: 10,
                            ),
                            child: Text(
                              formattedDate,
                              style: lbRegular.copyWith(
                                fontSize: 12,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          ),
                        );
                      },
                      onLoadEarlier: () => _loadMoreMessages(),
                      loadEarlierBuilder: _isLoadingMore
                          ? const Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.0,
                                vertical: 20,
                              ),
                              child: Center(
                                child: CircularProgressIndicator(
                                  color: AppConstants.primaryColor,
                                  strokeWidth: 5,
                                ),
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),

                    messageOptions: MessageOptions(
                      messagePadding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      messageDecorationBuilder:
                          (message, previousMessage, nextMessage) {
                        final isOutgoing = message.user.id == loggedInUser.id;
                        return message.customProperties?['message_type'] !=
                                'system'
                            ? BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: isOutgoing
                                    ? AppConstants.textGreenColor
                                    : Colors.white,
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                ),
                              )
                            : const BoxDecoration();
                      },
                      avatarBuilder: (user, onPressAvatar, onLongPressAvatar) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: NetworkAwareTap(
                            onTap: () => context.goNamed(
                              'message-club-member-profile',
                              extra: {
                                'userId': int.parse(user.id),
                                'bookClubId': widget.bookClubId.toString(),
                                // 'userName': user.firstName,
                              },
                            ),
                            child: user.customProperties?['message_type'] !=
                                    'system'
                                ? user.id == loggedInUser.id
                                    ? SizedBox.shrink()
                                    : CustomCachedNetworkImage(
                                        imageUrl: user.profileImage,
                                        width: 40,
                                        height: 40,
                                        errorImage:
                                            AppConstants.profileLogoImagePath,
                                      )
                                : const CircleAvatar(
                                    backgroundColor: Colors.transparent),
                            // child: CircleAvatar(
                            //   backgroundColor: Colors.white,
                            //   child: (user.profileImage?.contains('https') ?? false)
                            //       ? Image.network(
                            //           user.profileImage ?? '',
                            //           loadingBuilder:
                            //               (context, child, loadingProgress) {
                            //             if (loadingProgress == null) return child;
                            //             return const Center(
                            //               child: CircularProgressIndicator(
                            //                 color: AppConstants.primaryColor,
                            //                 strokeWidth: 1,
                            //               ),
                            //             );
                            //           },
                            //         )
                            //       : Image.asset(AppConstants.profileLogoImagePath),
                            // ),
                          ),
                        );
                      },
                      userNameBuilder: (user) {
                        return Column(
                          children: [
                            user.customProperties?['message_type'] == 'system'
                                ? const SizedBox.shrink()
                                : Text(
                                    user.firstName ?? '',
                                    style: lbRegular.copyWith(
                                      fontSize: 14,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                            const SizedBox(height: 5),
                          ],
                        );
                      },
                      showCurrentUserAvatar: true,
                      showOtherUsersAvatar: true,
                      showOtherUsersName: true,
                      showTime: true,
                      currentUserContainerColor: AppConstants.textGreenColor,
                      messageTextBuilder:
                          (message, previousMessage, nextMessage) {
                        final seenBy = message.customProperties?['seenBy'];
                        final messageType = message
                            .customProperties?['message_type']
                            .toString();
                        // print("SEEN BY : $seenBy");
                        bool isSeen = false;
                        // print("Is Seen value: ${seenBy?[0]['isSeen']}");
                        if (seenBy != null && seenBy is List) {
                          for (var entry in seenBy) {
                            // print("Seen By User ID: ${entry['user_id']}");
                            // print("Is Seen: ${entry['isSeen']}");
                            if (entry['isSeen'] == true &&
                                entry['user_id'] != loggedInUserId) {
                              isSeen = true;
                            }
                          }
                        } else {
                          // print("Seen By: No data");
                        }
                        // isSeen = seenBy?.any((entry) {
                        //   // print("Entry : $entry");
                        //   // print("User Id : ${widget.loggedInUserId}");
                        //   // print("Is Seen : ${entry['isSeen']}");
                        //   return entry['isSeen'] == true &&
                        //       entry['user_id'] != widget.loggedInUserId;
                        // });
                        // print("IS SEEN 1: $isSeen");
                        final date =
                            DateFormat('hh:mm a').format(message.createdAt);
                        final isOutgoing = message.user.id == loggedInUser.id;

                        if (messageType == 'system') {
                          // Center system messages across the full chat width
                          return Center(
                            child: Text(
                              message.text,
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 12,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          );
                        }

                        CrossAxisAlignment crossAxisAlignment;
                        TextAlign textAlign;
                        textAlign = TextAlign.start;
                        if (isOutgoing) {
                          crossAxisAlignment = CrossAxisAlignment.end;
                        } else {
                          crossAxisAlignment = CrossAxisAlignment.start;
                        }

                        return Column(
                          crossAxisAlignment: crossAxisAlignment,
                          children: [
                            Theme(
                              data: Theme.of(context).copyWith(
                                textSelectionTheme: TextSelectionThemeData(
                                  cursorColor: AppConstants.textGreenColor,
                                  selectionColor: isOutgoing
                                      ? AppConstants.primaryColor
                                      : AppConstants.textGreenColor,
                                  selectionHandleColor: isOutgoing
                                      ? AppConstants.primaryColor
                                      : AppConstants.textGreenColor,
                                ),
                              ),
                              child: SelectableText(
                                message.text,
                                textAlign: textAlign,
                                style: lbRegular.copyWith(
                                  fontSize: 16,
                                  color: isOutgoing
                                      ? Colors.white
                                      : AppConstants.primaryColor,
                                ),
                                enableInteractiveSelection: true,
                                selectionControls:
                                    MaterialTextSelectionControls(),
                                // contextMenuBuilder: (context, editableTextState) {
                                //   return AdaptiveTextSelectionToolbar(
                                //     anchors: editableTextState.contextMenuAnchors,
                                //     children: [
                                //       CupertinoTextSelectionToolbarButton(
                                //         child: Text(
                                //           'Copy',
                                //           style: lbRegular.copyWith(
                                //             fontSize: 14,
                                //             color: AppConstants.textGreenColor,
                                //           ),
                                //         ),
                                //         onPressed: () {
                                //           Clipboard.setData(
                                //             ClipboardData(text: message.text),
                                //           );
                                //         },
                                //       ),
                                //     ],
                                //   );
                                // },
                              ),
                            ),
                            const SizedBox(height: 4),
                            !isOutgoing
                                ? Text(
                                    date,
                                    textAlign: TextAlign.start,
                                    style: lbRegular.copyWith(
                                      fontSize: 12,
                                      color: isOutgoing
                                          ? Colors.white
                                          : AppConstants.primaryColor,
                                    ),
                                  )
                                : Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Text(
                                        date,
                                        style: lbRegular.copyWith(
                                          fontSize: 12,
                                          color: isOutgoing
                                              ? Colors.white
                                              : AppConstants.primaryColor,
                                        ),
                                      ),
                                      const SizedBox(width: 15),
                                      if (isSeen && isOutgoing)
                                        const Icon(
                                          Icons.done_all,
                                          color: AppConstants.primaryColor,
                                          size: 14,
                                        ),
                                    ],
                                  ),
                          ],
                        );
                      },
                      onLongPressMessage: (message) =>
                          _buildCustomMsgLongPress(message),
                      bottom: (message, previousMessage, nextMessage) =>
                          _buildCustomMsgBottom(message),
                    ),
                    currentUser: loggedInUser,
                    onSend: (ChatMessage message) {
                      onSend(message);
                    },
                    messages: chatMessages, //snapshot.data?.toList() ?? []
                  ),
                ],
              ),
            ),
    );
  }

  Future<void> _buildCustomMsgLongPress(ChatMessage msg) async {
    final result = await showModalBottomSheet<emoji.Emoji>(
      context: context,
      isScrollControlled: true,
      backgroundColor: AppConstants.primaryColor,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      builder: (context) {
        return CustomEmojiPicker(message: msg);
      },
    );
    // If an emoji is selected, update Firestore and local UI
    if (result != null) {
      final selected = {loggedInUserId.toString(): result.emoji};

      // Update Firestore
      addEmojiReaction(
        msg.customProperties?['message_id'],
        loggedInUserId.toString(),
        result.emoji,
      );

      // Update local state for real-time UI updates
      setState(() {
        if (msg.customProperties?["reactions"] != null) {
          msg.customProperties?["reactions"].addAll(
            {loggedInUserId.toString(): result.emoji},
          );
        } else {
          msg.customProperties?["reactions"] = selected;
        }
      });
    }
  }

  Widget _buildCustomMsgBottom(ChatMessage msg) {
    // Safely extract reactions with proper error handling
    Map<String, dynamic>? reactionsMap;
    try {
      reactionsMap =
          msg.customProperties?["reactions"] as Map<String, dynamic>?;
    } catch (e) {
      log('Error casting reactions to Map: $e');
      reactionsMap = null;
    }

    if (reactionsMap == null || reactionsMap.isEmpty) {
      return const SizedBox.shrink();
    }

    final List<Map<String, String>> reactionList = [];
    // Group emojis and count occurrences
    final Map<String, int> emojiCounts = {};

    try {
      reactionsMap.forEach((userId, emoji) {
        if (emoji != null) {
          final emojiStr = emoji.toString();
          emojiCounts[emojiStr] = (emojiCounts[emojiStr] ?? 0) + 1;

          ClubMembershipModel? user;
          try {
            user = memberList?.firstWhere(
              (element) => element.userId.toString() == userId,
              orElse: () => ClubMembershipModel(),
            );
          } catch (e) {
            log('Error finding user for reaction: $e');
            user = null;
          }

          reactionList.add({
            'userId': userId,
            'userName': user?.userId == loggedInUserId
                ? 'You'
                : (user?.userHandle ?? 'Unknown'),
            'emoji': emojiStr,
            'profilePicture': user?.userProfilePicture != null
                ? '${Config.imageBaseUrl}${user?.userProfilePicture}'
                : '',
          });
        }
      });
    } catch (e) {
      log('Error processing reactions: $e');
      return const SizedBox.shrink();
    }

    return NetworkAwareTap(
      onTap: () {
        showModalBottomSheet(
          context: context,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          backgroundColor: AppConstants.textGreenColor,
          useSafeArea: true,
          builder: (context) => Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: reactionList.map(
                (reaction) {
                  final id = reaction['userId'];
                  final name = reaction['userName'];
                  final emoji = reaction['emoji'];
                  final profilePicture = reaction['profilePicture'];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.white,
                      child: (profilePicture?.contains('https') ?? false)
                          ? Image.network(
                              profilePicture ?? '',
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) {
                                  return child;
                                }
                                return const Center(
                                  child: CircularProgressIndicator(
                                    color: AppConstants.primaryColor,
                                    strokeWidth: 1,
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return Image.asset(
                                    AppConstants.profileLogoImagePath);
                              },
                            )
                          : Image.asset(AppConstants.profileLogoImagePath),
                    ),
                    trailing: Text(
                      emoji ?? '',
                      style: lbBold.copyWith(fontSize: 25),
                    ),
                    title: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          name ?? '',
                          style: lbBold.copyWith(fontSize: 14),
                        ),
                        id == loggedInUserId.toString()
                            ? Text(
                                'Tap to remove',
                                style: lbRegular.copyWith(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w300,
                                ),
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                    onTap: () {
                      context.pop();
                      if (id == loggedInUserId.toString()) {
                        removeEmojiReaction(
                            msg.customProperties?['message_id'], id ?? '');
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'You cannot remove other users\' reactions'),
                          ),
                        );
                      }
                    },
                  );
                },
              ).toList(),
            ),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(top: 3),
        padding: const EdgeInsets.symmetric(horizontal: 5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppConstants.primaryColor,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: emojiCounts.entries.map((entry) {
            final emoji = entry.key;
            final count = entry.value;
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Row(
                children: [
                  Text(
                    emoji,
                    style: const TextStyle(fontSize: 16),
                  ),
                  if (count > 1)
                    Padding(
                      padding: const EdgeInsets.only(left: 2),
                      child: Text(
                        '$count',
                        style: lbRegular.copyWith(
                          fontSize: 12,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  // void _showEmojiPicker(
  //     BuildContext context, Offset position, ChatMessage message) {
  //   // Remove any existing overlay to avoid duplicates
  //   _emojiOverlay?.remove();
  //
  //   _emojiOverlay = OverlayEntry(
  //     builder: (context) => Positioned(
  //       left: position.dx,
  //       top: position.dy - 100, // Position above the message
  //       child: Material(
  //         color: Colors.transparent,
  //         child: Container(
  //           decoration: BoxDecoration(
  //             color: Colors.white,
  //             borderRadius: BorderRadius.circular(8.0),
  //             boxShadow: [
  //               BoxShadow(
  //                 color: Colors.black.withOpacity(0.1),
  //                 blurRadius: 8.0,
  //                 spreadRadius: 2.0,
  //               ),
  //             ],
  //           ),
  //           child: Wrap(
  //             children: getSystemEmojis()
  //                 .map((emoji) => NetworkAwareTap(
  //                       onTap: () {
  //                         setState(() =>
  //                             selectedEmojis[message.createdAt.toString()] =
  //                                 emoji);
  //                         _emojiOverlay?.remove(); // Remove the popup
  //                         _emojiOverlay = null;
  //                       },
  //                       child: Padding(
  //                         padding: const EdgeInsets.all(8.0),
  //                         child: Text(
  //                           emoji,
  //                           style: const TextStyle(fontSize: 24),
  //                         ),
  //                       ),
  //                     ))
  //                 .toList(),
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  //
  //   Overlay.of(context).insert(_emojiOverlay!);
  // }

  // Utility method to get system emojis
  List<String> getSystemEmojis() {
    // Unicode ranges for common emoji categories
    final ranges = [
      (0x1F600, 0x1F64F), // Emoticons
      (0x1F300, 0x1F5FF), // Misc Symbols and Pictographs
      (0x1F680, 0x1F6FF), // Transport and Map
      (0x2600, 0x26FF), // Misc symbols
      (0x2700, 0x27BF), // Dingbats
      (0x1F900, 0x1F9FF), // Supplemental Symbols and Pictographs
    ];

    final emojis = <String>[];

    for (final range in ranges) {
      for (int i = range.$1; i <= range.$2; i++) {
        final emoji = String.fromCharCode(i);
        if (emoji.characters.length == 1) {
          // Ensure valid emoji
          emojis.add(emoji);
        }
      }
    }

    return emojis;
  }
}

class _CacheEntry {
  final List<ChatMessage> messages;
  final DateTime timestamp;
  static const Duration _cacheDuration = Duration(minutes: 30);

  _CacheEntry(this.messages) : timestamp = DateTime.now();

  bool get isExpired => DateTime.now().difference(timestamp) > _cacheDuration;
}

class MessageCacheManager {
  final Map<String, _CacheEntry> _cache = {};
  static const int _maxCacheSize = 100;

  List<ChatMessage>? getMessages(String clubId) {
    final entry = _cache[clubId];
    if (entry == null || entry.isExpired) {
      _cache.remove(clubId);
      return null;
    }
    return List<ChatMessage>.from(entry.messages);
  }

  void updateMessages(String clubId, List<ChatMessage> messages) {
    _cache[clubId] = _CacheEntry(List<ChatMessage>.from(messages));
    _cleanCache();
  }

  void _cleanCache() {
    if (_cache.length > _maxCacheSize) {
      final sortedEntries = _cache.entries.toList()
        ..sort((a, b) => a.value.timestamp.compareTo(b.value.timestamp));

      final entriesToRemove =
          sortedEntries.take((_cache.length - _maxCacheSize) ~/ 2);

      for (var entry in entriesToRemove) {
        _cache.remove(entry.key);
      }
    }

    // Remove expired entries
    _cache.removeWhere((_, entry) => entry.isExpired);
  }

  void clear() => _cache.clear();

  bool hasCache(String clubId) =>
      _cache.containsKey(clubId) && !_cache[clubId]!.isExpired;
}
