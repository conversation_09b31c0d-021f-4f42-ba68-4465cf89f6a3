import 'dart:async';
import 'dart:developer';

import 'package:async/async.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/views/local_database.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shimmer/shimmer.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/constants.dart';
import '../../constants/text_style.dart';
import '../../reusableWidgets/appbar.dart';
import '../../reusable_api_function/club/club_function.dart';
import '../clubs/clubs_home/services/clubs_sync_service.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

int batchSize = 0;
List<int?> clubIds = [];

class _MessagesScreenState extends State<MessagesScreen>
    with SingleTickerProviderStateMixin, ClubSyncListener {
  Map<int, bool> notificationStatus = {}; // To track notification status
  StreamSubscription? _streamSubscription;
  bool isFirstLoading = true;

  Stream<List<DocumentSnapshot>> getGroupsStream() async* {
    // getAllBookClubsByUserId(true);
    receiveMetadata();
    if ((clubIds.isNotEmpty)) {
      batchSize += 10;
      // log("ClubIds length : ${clubIds.length}");
    } else {
      Future.delayed(Duration(seconds: 2)).then((_) {
        setState(() {
          isFirstLoading = false;
        });
      });
    }

    // Firestore's limit
    List<Stream<QuerySnapshot>> streams = [];

    // log("ClubIds : $clubIds");

    // Create multiple streams for chunks of clubIds
    for (int i = 0; i < clubIds.length; i += batchSize) {
      final chunk = clubIds.sublist(
        i,
        i + batchSize > clubIds.length ? clubIds.length : i + batchSize,
      );

      streams.add(
        FirebaseFirestore.instance
            .collection('Club_Collection')
            .where('bookClubId', whereIn: chunk)
            .orderBy('createdAt', descending: true)
            .snapshots(),
      );
    }

    // Merge streams and unify their results
    final List<DocumentSnapshot> allDocuments = [];

    yield* StreamGroup.merge(streams).map((querySnapshot) {
      // Add/Update documents while persisting the previous data
      for (var doc in querySnapshot.docs) {
        final index = allDocuments.indexWhere((d) => d.id == doc.id);
        if (index >= 0) {
          allDocuments[index] = doc; // Update if document already exists
        } else {
          allDocuments.add(doc); // Add if new document
        }
      }

      isFirstLoading = false;
      setState(() {}); // Notify the UI
      return allDocuments.toList(); // Return a copy of the unified list
    });
  }

  bool hasNewNotifications = false;

  int? loggedInUserId;
  List<BookClubModel> bookClubList = [];
  final ScrollController _currentReadScrollController = ScrollController();
  ClubController? clubController;
  bool isLoading = false;
  MessageController? messageController;

  @override
  void initState() {
    messageController = Provider.of<MessageController>(context, listen: false);
    clubController = Provider.of(context, listen: false);
    _currentReadScrollController.addListener(_currentReadOnScroll);
    _initializeUserIdAndFetchData();
    _newClubsAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Initialize sync listeners for cross-screen communication
    initializeSyncListeners();

    super.initState();
  }

  @override
  void onClubSyncEvent(ClubSyncEvent event) {
    super.onClubSyncEvent(event);

    switch (event.type) {
      case ClubSyncEventType.clubCreated:
      case ClubSyncEventType.userLeftClub:
      case ClubSyncEventType.membershipChanged:
        // Refresh clubs data when changes occur
        log('MessagesScreen: Refreshing clubs data due to sync event - ${event.type}');
        getAllBookClubsByUserId(false);
        break;
      default:
        break;
    }
  }

  @override
  void onRefreshRequested(bool shouldRefresh) {
    super.onRefreshRequested(shouldRefresh);
    if (shouldRefresh) {
      log('MessagesScreen: Refreshing clubs data due to refresh request');
      getAllBookClubsByUserId(false);
    }
  }

  SharedPreferences? pref;
  DateTime? dateTime;

  Future _initializeUserIdAndFetchData() async {
    setState(() {
      isLoading = true;
    });

    await _initializeUserId();
    // await fetchAndFilterSeenByData(loggedInUserId ?? 0);
    await Future.wait(
      [
        getAllBookClubsByUserId(false),
        // getBookClubsForChat(),
      ],
    ).then((value) {
      setState(() {
        isLoading = false;
        isInitialized = true;
      });
    });
  }

  Future<void> _initializeUserId() async {
    loggedInUserId = await CommonHelper.getLoggedInUserId();
  }

  void _currentReadOnScroll() {
    if (_currentReadScrollController.position.pixels >=
            _currentReadScrollController.position.maxScrollExtent &&
        !clubController!.allClubLoading &&
        (messagesBookclubList?.length ?? 0) <
            (clubController?.allClubCount ?? 0)) {
      getAllBookClubsByUserId(true);
      // getFellowReaders(true); // Fetch more data
    }
  }

  bool isInitialized = false;
  List<BookClubModel>? messagesBookclubList = [];
  Future<void> getAllBookClubsByUserId(bool isMore) async {
    try {
      await Provider.of<BookClubController>(context, listen: false)
          .getBookClubs(
        '',
        loggedInUserId,
        null,
        context,
        null,
        null,
      )
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookClubModel> bookClubList = [];

          if (responseMap["data"].isNotEmpty) {
            bookClubList = (responseMap["data"] as List)
                .map((item) => BookClubModel.fromJson(item))
                .toList();
            log("BookClubsIds List : ${bookClubList.length}");
            messagesBookclubList = bookClubList;
            clubIds.clear();
            clubIds =
                messagesBookclubList?.map((e) => e.bookClubId).toList() ?? [];
            setState(() {});
          } else {
            messagesBookclubList?.clear();
            messagesBookclubList = [];
          }
        } else {}
        await addStandingBookClubsToLocal();
        // loadMessagesFromDatabase();
      }).whenComplete(() {
        // Future.delayed(const Duration(seconds: 1), () async {
        //   setState(() {
        //     isFirstLoading = false; // Stop shimmer effect after initial load
        //   });
        // });
      });
    } catch (e) {
      log("Error : $e");
    }
  }

  bool hasNewNotification = false;

  Future<void> receiveMetadata() async {
    try {
      if (messagesBookclubList == null ||
          (messagesBookclubList?.isEmpty ?? false)) {
        log('No clubs found for the user.');
        return;
      }

      // Step 2: Extract club IDs from the list
      List<int?> clubIds =
          messagesBookclubList?.map((e) => e.bookClubId).toList() ?? [];
      // _userBookClubIds = clubIds;

      for (var clubId in clubIds) {
        await getLatestMessageSeenBy(
            clubId.toString(), clubId ?? 0, loggedInUserId ?? 0);
        // log("Latest message ID for club $clubId: $latestMessageId");
      }
      hasNewNotification = messagesBookclubList?.any(
            (e) => notificationStatus.values.any((element) => element == false),
          ) ??
          false;

      // Step 7: Notify the NotificationController
      if (mounted) {
        await Provider.of<MessageController>(context, listen: false)
            .unSeenClubMessages(hasNewNotification);
      }
      // notifyListeners();

      // Step 7: Notify the NotificationController
    } catch (e) {
      log('Error fetching metadata: $e');
    }
  }

  Future<void> getLatestMessageSeenBy(
      String clubId, int userClubId, int loggedinUserId) async {
    try {
      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection('Club_Collection')
          .doc(clubId)
          .collection('messages')
          .orderBy('createdAt', descending: true) // Ensure 'timestamp' exists
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        var latestMessage = querySnapshot.docs.first;

        List<dynamic>? seenByList = latestMessage['seenBy'];

        // print("Latest Message ID: $latestMessageId");

        if (seenByList != null && seenByList.isNotEmpty) {
          var userSeenData = seenByList.firstWhere(
            (entry) => entry['user_id'] == loggedinUserId,
            orElse: () => null, // Return null if user not found
          );

          if (userSeenData != null) {
            bool isSeen = userSeenData['isSeen'] ?? false;
            notificationStatus[userClubId] = isSeen;
            // notifyListeners();
            // print("User ID: $loggedinUserId, Is Seen: $isSeen");
          } else {
            // print("User ID: $loggedinUserId not found in seenBy list.");
          }
        } else {
          // print("No seenBy data found.");
        }
      } else {
        // print("No messages found.");
      }
    } catch (e) {
      // print("Error fetching seen status: $e");
    }
  }

  Future<void> addStandingBookClubsToLocal() async {
    if (messagesBookclubList == null) return;

    for (final club in messagesBookclubList!) {
      await DatabaseHelper.instance.insertUserClub(
        club.bookClubId.toString(),
        club.bookClubName ?? '',
        loggedInUserId ?? 0,
      );
    }
  }

  @override
  void dispose() {
    _newClubsAnimationController.dispose();
    _currentReadScrollController.dispose();
    _streamSubscription?.cancel();

    // Dispose sync listeners
    disposeSyncListeners();

    super.dispose();
  }

  bool? isEmptyData;
  late AnimationController _newClubsAnimationController;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(right: 7.0),
        child: NetworkAwareTap(
          onTap: () async {
            _newClubsAnimationController.reset();
            _newClubsAnimationController.forward();
            if (clubIds.isNotEmpty) {
              _currentReadScrollController.position
                  .animateTo(
                0.0,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeOut,
              )
                  .then(
                (value) async {
                  if (context.mounted) {
                    await Provider.of<MessageController>(context, listen: false)
                        .getAllBookClubsByUserId(
                            true, context, loggedInUserId ?? 0);
                  }
                },
              );
            } else {
              await Provider.of<MessageController>(context, listen: false)
                  .getAllBookClubsByUserId(true, context, loggedInUserId ?? 0);
            }
            log("ClubList Length : ${messagesBookclubList?.length}");
            // await Provider.of<MessageController>(context, listen: false)
            //     .receiveMetadata(
            //         messagesBookclubList, context, loggedInUserId ?? 0);

            setState(() {});
          },

          // backgroundColor: AppConstants.backgroundColor,
          // shape: OutlineInputBorder(
          //   borderRadius: BorderRadius.circular(16),
          //   borderSide: BorderSide(
          //     color: AppConstants.primaryColor,
          //   ),
          // ),
          child: Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppConstants.backgroundColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppConstants.primaryColor,
              ),
            ),
            child: RotationTransition(
              turns: Tween(begin: 0.0, end: 1.0)
                  .animate(_newClubsAnimationController),
              child: Icon(
                Icons.autorenew_rounded,
                size: 30,
              ),
            ),
          ),
        ),
      ),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const AppBarWidget(
            appBarText: 'Messages',
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Consumer<MessageController>(
          builder: (context, messageController, child) {
            final clubs = messageController.documentList;
            if (messageController.isFirstLoading) {
              return _buildSkeletonLoader();
            } else {
              if (clubs.isEmpty) {
                isEmptyData = true;
              } else {
                isEmptyData = false;
              }

              if (isEmptyData ?? false) {
                return Column(
                  children: [
                    Container(
                      margin:
                          const EdgeInsets.only(top: 25, left: 20, right: 20),
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(10)),
                        border: Border.all(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      child: ListTile(
                        titleTextStyle: lbBold.copyWith(
                          fontSize: 14,
                          height: 1.3,
                        ),
                        title: const Text(
                          "When you join or start a club, you’ll be able to message fellow club members here to coordinate meeting times, chat, etc.\n\nGet into a club & start chatting with your book people!",
                          // overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                );
              } else {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Skeletonizer(
                    effect: const SoldColorEffect(
                      color: AppConstants.skeletonforgroundColor,
                      lowerBound: 0.1,
                      upperBound: 0.5,
                    ),
                    enabled: messageController.isFirstLoading || clubs.isEmpty,
                    child: Column(
                      children: [
                        Expanded(
                          child: ListView.builder(
                            controller: _currentReadScrollController,
                            padding: const EdgeInsets.only(bottom: 25),
                            itemCount: clubs.length,
                            itemBuilder: (context, index) {
                              int bookClubId = clubs[index]['bookClubId'] ?? 0;

                              return Skeleton.replace(
                                replacement: NetworkAwareTap(
                                  onTap: () => _handleChatTap(
                                      clubs[index]['bookClubId'].toString(),
                                      clubs[index]['bookClubId']),
                                  child: Stack(
                                    children: [
                                      Container(
                                        margin: const EdgeInsets.only(top: 25),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 15),
                                        height: 50,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                            color: AppConstants.primaryColor,
                                            width: 1.5,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                clubs[index]['bookClubName'] ??
                                                    '',
                                                overflow: TextOverflow.ellipsis,
                                                style: lbBold.copyWith(
                                                  fontSize: 18,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            Image.asset(
                                              "assets/icons/Messages.png",
                                              height: 30,
                                              width: 48,
                                              filterQuality: FilterQuality.high,
                                              fit: BoxFit.contain,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Visibility(
                                        visible: messageController
                                                    .notificationStatus[
                                                bookClubId] ==
                                            false,
                                        child: Positioned(
                                          top: 15,
                                          right: 2,
                                          child: Image.asset(
                                            AppConstants.notificationImagePath,
                                            height: 18,
                                            width: 18,
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                child: NetworkAwareTap(
                                  // onTap: () async {
                                  //   messageController.markMessageAsSeen(
                                  //       bookClubId.toString(),
                                  //       bookClubId,
                                  //       loggedInUserId ?? 0,
                                  //       context);
                                  //   if (context.mounted) {
                                  //     context.pushNamed(
                                  //       'chat-screen',
                                  //       queryParameters: {
                                  //         "userId": loggedInUserId.toString(),
                                  //         'bookClubId':
                                  //             clubs[index]['bookClubId'].toString(),
                                  //       },
                                  //     );
                                  //   }
                                  // },
                                  onTap: () => _handleChatTap(
                                      clubs[index]['bookClubId'].toString(),
                                      clubs[index]['bookClubId']),
                                  child: Stack(
                                    children: [
                                      Container(
                                        margin: const EdgeInsets.only(top: 25),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 15),
                                        height: 50,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          border: Border.all(
                                            color: AppConstants.primaryColor,
                                            width: 1.5,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                clubs[index]['bookClubName'] ??
                                                    '',
                                                overflow: TextOverflow.ellipsis,
                                                style: lbBold.copyWith(
                                                  fontSize: 18,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            Image.asset(
                                              "assets/icons/Messages.png",
                                              height: 30,
                                              width: 48,
                                              filterQuality: FilterQuality.high,
                                              fit: BoxFit.contain,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Visibility(
                                        visible: messageController
                                                    .notificationStatus[
                                                bookClubId] ==
                                            false,
                                        child: Positioned(
                                          top: 15,
                                          right: 2,
                                          child: Image.asset(
                                            AppConstants.notificationImagePath,
                                            height: 18,
                                            width: 18,
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }
            }
          },
        ),
      ),
    );
  }

// Skeleton Loader
  Widget _buildSkeletonLoader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Skeleton.replace(
        replacement: ListView.builder(
          itemCount: 10, // Number of skeleton items to show
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(top: 25),
              child: Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            );
          },
        ),
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.only(top: 25, left: 20, right: 20),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                border: Border.all(
                  color: AppConstants.primaryColor,
                  width: 1.5,
                ),
              ),
              child: ListTile(
                titleTextStyle: lbBold.copyWith(
                  fontSize: 14,
                  height: 1.3,
                ),
                title: const Text(
                  "When you join or start a club, you’ll be able to message fellow club members here to coordinate meeting times, chat, etc.\n\nGet into a club & start chatting with your book people!",
                  // overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleChatTap(String clubId, int userClubId) async {
    // Navigate to chat screen
    if (mounted) {
      context.push('/chat-screen?bookClubId=$clubId').then((_) {
        if (mounted) {
          Provider.of<MessageController>(context, listen: false)
              .markMessageAsSeen(
            clubId,
            userClubId,
            loggedInUserId ?? 0,
            context,
          );
        }
      });
    }
  }
}
