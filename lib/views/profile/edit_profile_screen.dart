import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/profile_controller.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/models/profile_model/edit_profile/update_user_profile_model.dart';
import 'package:eljunto/models/profile_model/location_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:go_router/go_router.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/common_helper.dart';
import '../../models/user_model.dart';
import '../../reusableWidgets/custom_button.dart';
import '../../reusableWidgets/previous_screen_appbar.dart';

class EditProfileScreen extends StatefulWidget {
  final String? buttonName;
  final bool updateProfile;
  final UserModel? userModel;

  const EditProfileScreen({
    super.key,
    this.buttonName,
    this.updateProfile = false,
    this.userModel,
  });

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  TextEditingController handleController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController locationController = TextEditingController();
  TextEditingController bioController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  String? clubInvitation = "";
  bool isNotification = true;
  bool nameValidation = false;
  bool locationValidation = false;
  bool bioValidation = false;
  String errorMsg = '';
  int? userId;
  String? logginedMail;
  bool isImageLoading = false;
  bool isLoading = false;
  File? _image;
  String userProfilePicture = '';
  String imageValidation = '';
  UserController? userController;
  bool isDataLoading = false;

  @override
  void initState() {
    userController = Provider.of(context, listen: false);
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      await _initializeUserId().then((value) async {
        // if (widget.updateProfile == false) {
        //   getUserDetails();
        // } else {
        //   userDataInitialize();
        // }
        getUserDetails();
      });
    });
    super.initState();
  }

  UserModel userModel = UserModel();

  Future<void> getUserDetails() async {
    setState(() => isDataLoading = true);
    try {
      await Provider.of<UserController>(context, listen: false)
          .getUserDetailsByUserId(userId ?? 0, context)
          .then((_) {
        userModel = userController?.userModel ?? UserModel();
      });
      // if (responseMap.containsKey('error')) {
      //   log(responseMap['error']);
      // } else {
      //   userModel = UserModel.fromJson(responseMap);
      // }
      await userDataInitialize();
    } catch (e) {
      log('An error occurred: $e');
    }
    setState(() => isDataLoading = false);
  }

  List<Location>? locationList;
  int? locationCountryId;

  Future<List<Location>> _fetchLocationsData(String query) async {
    locationList = await Provider.of<ProfileController>(context, listen: false)
        .getLocationData(locationController.text, context);
    List<Location>? list;

    setState(() {
      final data = locationList?.where(
        (element) => element.name!.toLowerCase().contains(
              query.toLowerCase(),
            ),
      );
      list = data?.toList();
      list?.sort((a, b) => a.name?.compareTo(b.name ?? '') ?? 0);
    });
    return list ?? [];
  }

  Future<void> userDataInitialize() async {
    checkFunction();
    print("Invitation : ${widget.updateProfile}");
    // if (widget.updateProfile) {
    //   handleController.text = widget.userModel?.data?.userHandle ?? '';
    //   locationController.text = widget.userModel?.data?.userLocation ?? '';
    //   bioController.text = widget.userModel?.data?.userBio ?? '';
    //   nameController.text = widget.userModel?.data?.userName ?? '';
    //   userProfilePicture =
    //       widget.userModel?.data?.userProfilePicture?.isNotEmpty ?? false
    //           ? Config.imageBaseUrl +
    //               (widget.userModel?.data?.userProfilePicture ?? '')
    //           : AppConstants.profileLogoImagePath;
    // } else {
    handleController.text = userModel.data?.userHandle ?? '';
    locationController.text = userModel.data?.userLocation ?? '';
    bioController.text = userModel.data?.userBio ?? '';
    nameController.text = userModel.data?.userName ?? '';
    userProfilePicture = userModel.data?.userProfilePicture?.isNotEmpty ?? false
        ? Config.imageBaseUrl + (userModel.data?.userProfilePicture ?? '')
        : AppConstants.profileLogoImagePath;
    // }
  }

  void checkFunction() {
    if (userModel.data?.userClubInvitation == true) {
      clubInvitation = "Yes";
    } else {
      clubInvitation = "No";
    }
    setState(() {});
  }

  Future<void> _initializeUserId() async {
    userId = await CommonHelper.getLoggedInUserId();

    String? userMail = await CommonHelper.getLoggedinUserMail();
    String? userLocation = await CommonHelper.getLoggedinUserLocation();
    String? userBio = await CommonHelper.getLoggedinUserBio();

    if (userLocation != null && userBio != null) {}
    if (userId != null && userMail != null) {
      setState(() {
        logginedMail = userMail;
      });
    } else {}
  }

  Future<void> isLocationUpdate() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    nameController.text = pref.getString('').toString();
  }

  Future<void> _pickImage() async {
    setState(() {
      isImageLoading = true;
    });

    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 100,
      maxHeight: 1000,
      maxWidth: 1000,
    );

    if (pickedFile != null) {
      final fileSize = await File(pickedFile.path).length();
      const maxSizeInBytes = 10 * 1024 * 1024;

      if (fileSize > maxSizeInBytes) {
        setState(() {
          isImageLoading = false;
          imageValidation =
              'Selected image exceeds the maximum allowed size of 10 MB.';
        });
        return;
      }

      // Validate file type (MIME type)
      final mimeType = CommonHelper.getMimeType(
          pickedFile.path); //pickedFile.mimeType?.toLowerCase();
      const allowedMimeTypes = ['image/jpeg', 'image/png'];

      if (mimeType == null || !allowedMimeTypes.contains(mimeType)) {
        setState(() {
          isImageLoading = false;
          imageValidation =
              'Selected file is not a valid image type (JPEG/PNG).';
        });
        return;
      }
      final file = await cropUserImage(pickedFile);

      final croppedImage = await convertSquareImageToCircle(File(file!.path));
      if (croppedImage.path.isNotEmpty) {
        final renamedCroppedImage =
            await CommonHelper.renameFile(File(pickedFile.path), croppedImage);
        setState(() {
          _image = renamedCroppedImage;
        });

        try {
          if (mounted) {
            final responseMap =
                await Provider.of<UserController>(context, listen: false)
                    .uploadFile(_image!, userId ?? 0, context);
            if (responseMap.containsKey('error')) {
              log(responseMap['error']);
            } else {
              setState(() {
                userProfilePicture = Config.imageBaseUrl + responseMap["data"];
              });
            }
          }
        } catch (e) {
          log('Error uploading image: $e');
          // Handle error
        }
      }
    } else {
      log('Image not picked');
      setState(() {
        _image = null;
      });
    }

    setState(() {
      isImageLoading = false; // Stop loading indicator
    });
  }

  Future<File> convertSquareImageToCircle(File imageFile) async {
    final image = await decodeImageFromList(await imageFile.readAsBytes());
    final output = await _createCircleImage(image);
    final file = File(imageFile.path);
    await file.writeAsBytes(output);
    return file;
  }

  Future<Uint8List> _createCircleImage(ui.Image image) async {
    final pictureRecorder = ui.PictureRecorder();
    final canvas = Canvas(pictureRecorder);

    final size = image.width;
    final radius = size / 2;

    // Draw the circle
    final paint = Paint();

    paint.color = Colors.transparent;

    canvas.drawCircle(Offset(radius, radius), radius, paint);

    // Clip the image to the circle

    canvas.clipPath(
      Path()
        ..addOval(
          Rect.fromCircle(
            center: Offset(radius, radius),
            radius: radius,
          ),
        ),
    );

    // Draw the image centered in the circle

    canvas.drawImage(image, const Offset(0, 0), Paint());

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size, size);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  Future<CroppedFile?> cropUserImage(XFile pickedFile) async {
    final croppedImage = await ImageCropper().cropImage(
      sourcePath: pickedFile.path,
      aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
      compressFormat: ImageCompressFormat.png,
      compressQuality: 100,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Crop your image',
          toolbarColor: AppConstants.primaryColor,
          cropFrameColor: AppConstants.primaryColor,
          toolbarWidgetColor: Colors.white,
          cropStyle: CropStyle.circle,
          hideBottomControls: true,
          // initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: true,
          showCropGrid: true,
        ),
        IOSUiSettings(
          minimumAspectRatio: 1,
          title: 'Crop your image',
          doneButtonTitle: 'Save',
          cancelButtonTitle: 'Cancel',
          cropStyle: CropStyle.circle,
          showCancelConfirmationDialog: true,
          hidesNavigationBar: false,
          rotateButtonsHidden: true,
          rotateClockwiseButtonHidden: true,
          aspectRatioPickerButtonHidden: true,
          aspectRatioLockEnabled: true,
        ),
      ],
    );
    return croppedImage;
  }

  @override
  void dispose() {
    handleController.dispose();
    nameController.dispose();
    bioController.dispose();
    locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          // color: Colors.white,
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fill,
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(80),
            child: Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    width: 1.5,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
              child: PreviousScreenAppBar(
                bookName: widget.buttonName,
                isSetProfile: widget.updateProfile,
              ),
            ),
          ),
          body: Skeletonizer(
            enabled: isDataLoading,
            child: Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(
                              height: 25,
                            ),
                            ListTile(
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 1.5,
                                horizontal: 16,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                                side: const BorderSide(
                                  color: AppConstants.primaryColor,
                                  width: 1.5,
                                ),
                              ),
                              dense: true,
                              tileColor: Colors.transparent,
                              title: Text(
                                'Edit Profile Photo',
                                // textAlign: ui.TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                              leading: ClipRRect(
                                clipBehavior: ui.Clip.none,
                                borderRadius: BorderRadius.circular(50),
                                child: CustomCachedNetworkImage(
                                  errorImage: AppConstants.profileLogoImagePath,
                                  height: 45,
                                  width: 45,
                                  imageUrl: userProfilePicture,
                                ),
                              ),
                              onTap: () {
                                _pickImage();
                              },
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              child: Row(
                                children: [
                                  (imageValidation.isNotEmpty)
                                      ? Text(
                                          imageValidation,
                                          overflow: TextOverflow.clip,
                                          style: lbBold.copyWith(
                                            fontSize: 14,
                                            color: AppConstants.redColor,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 25,
                            ),
                            Text(
                              'Handle:',
                              overflow: TextOverflow.ellipsis,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            TextFormField(
                              controller: handleController,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                              // maxLines: null,
                              enabled: false,
                              decoration: InputDecoration(
                                filled: true,
                                fillColor:
                                    const Color.fromRGBO(255, 255, 255, 1),
                                contentPadding: const EdgeInsets.all(10),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: const BorderSide(
                                    color: AppConstants.primaryColor,
                                    width: 1.5,
                                  ),
                                ),
                              ),
                              readOnly: true,
                            ),
                            const SizedBox(
                              height: 25,
                            ),
                            Text(
                              'Name:',
                              overflow: TextOverflow.ellipsis,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Stack(
                              clipBehavior: ui.Clip.none,
                              children: [
                                TextFormField(
                                  controller: nameController,
                                  textCapitalization:
                                      TextCapitalization.sentences,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                  maxLines: null,
                                  // maxLength: 45,
                                  decoration: InputDecoration(
                                    // counterStyle: lbRegular.copyWith(
                                    //   fontSize: 14,
                                    // ),
                                    suffixIcon: GestureDetector(
                                      onTap: () {},
                                      child: Image.asset(
                                        'assets/icons/Edit.png',
                                        filterQuality: FilterQuality.high,
                                        fit: BoxFit.cover,
                                        height: 38,
                                        width: 38,
                                      ),
                                    ),
                                    filled: true,
                                    fillColor:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    contentPadding: const EdgeInsets.all(10),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(5),
                                      borderSide: const BorderSide(
                                        color: AppConstants.primaryColor,
                                        width: 1.5,
                                      ),
                                    ),
                                  ),
                                  onChanged: (value) {
                                    setState(() {
                                      nameValidation = false;
                                    });
                                  },
                                  validator: (value) {
                                    nameValidation = false;
                                    if (value!.isEmpty) {
                                      setState(() {
                                        nameValidation = true;
                                      });
                                      return null;
                                    } else {
                                      return null;
                                    }
                                  },
                                ),
                                Positioned(
                                  left: 0,
                                  right: 0,
                                  top: 55,
                                  child: nameValidation
                                      ? Text(
                                          '*Enter name',
                                          overflow: TextOverflow.clip,
                                          style: lbBold.copyWith(
                                            fontSize: 14,
                                            color: AppConstants.redColor,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                ),
                              ],
                            ),
                            nameValidation
                                ? const SizedBox(
                                    height: 15,
                                  )
                                : const SizedBox.shrink(),
                            const SizedBox(
                              height: 25,
                            ),
                            Text(
                              'Location:',
                              overflow: TextOverflow.ellipsis,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Stack(
                              clipBehavior: ui.Clip.none,
                              children: [
                                TextFormField(
                                  controller: locationController,
                                  textCapitalization:
                                      TextCapitalization.sentences,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                  maxLength: 30,
                                  maxLines: null,
                                  decoration: InputDecoration(
                                    counterStyle: lbRegular.copyWith(
                                      fontSize: 14,
                                    ),
                                    suffixIcon: NetworkAwareTap(
                                      onTap: () {},
                                      child: Image.asset(
                                        'assets/icons/Edit.png',
                                        filterQuality: FilterQuality.high,
                                        fit: BoxFit.cover,
                                        height: 38,
                                        width: 38,
                                      ),
                                    ),
                                    filled: true,
                                    fillColor:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    contentPadding: const EdgeInsets.all(10),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(5),
                                      borderSide: const BorderSide(
                                        color: AppConstants.primaryColor,
                                        width: 1.5,
                                      ),
                                    ),
                                  ),
                                  onChanged: (value) {
                                    setState(() {
                                      locationValidation = false;
                                    });
                                  },
                                  validator: (value) {
                                    locationValidation = false;
                                    if (value!.isEmpty) {
                                      setState(() {
                                        locationValidation = true;
                                      });
                                      return null;
                                    } else {
                                      return null;
                                    }
                                  },
                                ),
                                // TypeAheadField(
                                //   loadingBuilder: (context) {
                                //     return Container(
                                //       decoration: BoxDecoration(
                                //         color: Colors.white,
                                //         border: Border.all(
                                //             color: AppConstants.primaryColor),
                                //         borderRadius: BorderRadius.circular(10),
                                //       ),
                                //       child: const Center(
                                //         child: CircularProgressIndicator(
                                //           color: AppConstants.primaryColor,
                                //         ),
                                //       ),
                                //     );
                                //   },
                                //   emptyBuilder: (context) {
                                //     return ListTile(
                                //       title: Text(
                                //         'Location not found? Create your own now!',
                                //         style: lbRegular.copyWith(
                                //           fontSize: 14,
                                //         ),
                                //       ),
                                //     );
                                //   },
                                //   controller: locationController,
                                //   builder: (context, controller, focusNode) {
                                //     return TextFormField(
                                //       style: lbRegular.copyWith(
                                //         fontSize: 18,
                                //       ),
                                //       controller: controller,
                                //       focusNode: focusNode,
                                //       maxLength: 30,
                                //       decoration: InputDecoration(
                                //         counterStyle: lbRegular.copyWith(
                                //           fontSize: 14,
                                //           color: AppConstants.primaryColor,
                                //         ),
                                //         suffixIcon: const Icon(
                                //           Icons.search_rounded,
                                //           size: 25,
                                //           color: AppConstants.primaryColor,
                                //         ),
                                //         filled: true,
                                //         fillColor: Colors.white,
                                //         contentPadding: const EdgeInsets.all(10),
                                //         border: OutlineInputBorder(
                                //           borderRadius: BorderRadius.circular(5),
                                //           borderSide: const BorderSide(
                                //             color: AppConstants.primaryColor,
                                //             width: 1.5,
                                //           ),
                                //         ),
                                //       ),
                                //       validator: (value) {
                                //         locationValidation = false;
                                //         if (value!.isEmpty) {
                                //           setState(() {
                                //             locationValidation = true;
                                //           });
                                //           return null;
                                //         } else {
                                //           return null;
                                //         }
                                //       },
                                //       onChanged: (value) {
                                //         setState(
                                //           () {
                                //             locationValidation = false;
                                //           },
                                //         );
                                //       },
                                //     );
                                //   },
                                //   decorationBuilder: (context, child) {
                                //     return Material(
                                //       type: MaterialType.card,
                                //       elevation: 0,
                                //       color: Colors.white,
                                //       shape: RoundedRectangleBorder(
                                //         borderRadius: BorderRadius.circular(10),
                                //         side: const BorderSide(
                                //           color: AppConstants.primaryColor,
                                //         ),
                                //       ),
                                //       child: child,
                                //     );
                                //   },
                                //   itemBuilder: (context, value) {
                                //     return ListTile(
                                //       title: Text(
                                //         "${value.name ?? ''}, ${value.stateCode}",
                                //         style: lbRegular.copyWith(fontSize: 15),
                                //       ),
                                //       shape: const Border(
                                //         bottom: BorderSide(
                                //           color: AppConstants.primaryColor,
                                //         ),
                                //       ),
                                //     );
                                //   },
                                //   onSelected: (value) {
                                //     locationController.text =
                                //         "${value.name}, ${value.stateCode}";
                                //     locationCountryId = value.countryId;
                                //     // print("BookName : ${bookController.text}");
                                //     // print("BookId : $bookId");
                                //     setState(() {});
                                //   },
                                //   suggestionsCallback: (search) {
                                //     if (locationController.length >= 3) {
                                //       return _fetchLocationsData(search);
                                //     }
                                //     return null;
                                //   },
                                // ),
                                Positioned(
                                  left: 0,
                                  right: 0,
                                  top: 55,
                                  child: locationController.text.isEmpty
                                      ? Text(
                                          '*Enter location',
                                          overflow: TextOverflow.clip,
                                          style: lbBold.copyWith(
                                            fontSize: 14,
                                            color: AppConstants.redColor,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                ),
                              ],
                            ),
                            locationValidation
                                ? const SizedBox(
                                    height: 15,
                                  )
                                : const SizedBox.shrink(),
                            const SizedBox(
                              height: 25,
                            ),
                            Text(
                              'Bio (150 Characters max):',
                              overflow: TextOverflow.ellipsis,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Stack(
                              clipBehavior: ui.Clip.none,
                              children: [
                                TextFormField(
                                  maxLength: 150,
                                  controller: bioController,
                                  textCapitalization:
                                      TextCapitalization.sentences,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                  maxLines: null,
                                  decoration: InputDecoration(
                                    counterStyle: lbRegular.copyWith(
                                      fontSize: 14,
                                      color: AppConstants.primaryColor,
                                    ),
                                    suffixIcon: NetworkAwareTap(
                                      onTap: () {},
                                      child: Image.asset(
                                        'assets/icons/Edit.png',
                                        filterQuality: FilterQuality.high,
                                        fit: BoxFit.cover,
                                        height: 38,
                                        width: 38,
                                      ),
                                    ),
                                    filled: true,
                                    fillColor:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    contentPadding: const EdgeInsets.all(10),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(5),
                                      borderSide: const BorderSide(
                                        color: AppConstants.primaryColor,
                                        width: 1.5,
                                      ),
                                    ),
                                  ),
                                  onChanged: (value) {
                                    setState(() {
                                      bioValidation = false;
                                    });
                                  },
                                  validator: (value) {
                                    bioValidation = false;
                                    if (value!.isEmpty) {
                                      setState(() {
                                        bioValidation = true;
                                      });
                                      return null;
                                    } else {
                                      return null;
                                    }
                                  },
                                ),
                                Positioned(
                                  left: 0,
                                  right: 0,
                                  top: 55,
                                  child: bioController.text.isEmpty
                                      ? Text(
                                          '*Enter bio',
                                          overflow: TextOverflow.clip,
                                          style: lbBold.copyWith(
                                            fontSize: 14,
                                            color: AppConstants.redColor,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 25,
                            ),
                            Text(
                              'Open to club invitations ?',
                              overflow: TextOverflow.ellipsis,
                              style: lbRegular.copyWith(
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(
                              height: 3,
                            ),
                            Text(
                              '(Must be set to “Yes” to receive new club invitations)',
                              overflow: TextOverflow.ellipsis,
                              style: lbItalic.copyWith(
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Row(
                              children: [
                                SizedBox(
                                  height: 19,
                                  width: 19,
                                  child: Radio<String>(
                                    fillColor: const WidgetStatePropertyAll(
                                      AppConstants.primaryColor,
                                    ),
                                    value: 'Yes',
                                    groupValue: clubInvitation,
                                    onChanged: (String? value) {
                                      setState(() {
                                        clubInvitation = value!;
                                        _filterData(clubInvitation);
                                      });
                                    },
                                  ),
                                ),
                                const SizedBox(
                                  width: 15,
                                ),
                                Text(
                                  'Yes',
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(
                                  width: 25,
                                ),
                                SizedBox(
                                  height: 19,
                                  width: 19,
                                  child: Radio<String>(
                                    fillColor: const WidgetStatePropertyAll(
                                      AppConstants.primaryColor,
                                    ),
                                    value: 'No',
                                    groupValue: clubInvitation,
                                    onChanged: (String? value) {
                                      setState(() {
                                        clubInvitation = value!;
                                        _filterData(clubInvitation);
                                      });
                                    },
                                  ),
                                ),
                                const SizedBox(
                                  width: 15,
                                ),
                                Text(
                                  'No',
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 25,
                            ),
                            CustomLoaderButton(
                              // loginText: 'Login',
                              buttonWidth: isLoading
                                  ? 45.0
                                  : MediaQuery.of(context).size.width,
                              buttonRadius: 30.0,
                              buttonChild: isLoading
                                  ? const CircularProgressIndicator(
                                      valueColor:
                                          AlwaysStoppedAnimation(Colors.white),
                                      strokeWidth: 3.0,
                                    )
                                  : Text(
                                      'Save',
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                              buttonPressed: () {
                                saveInfo();
                              },
                            ),
                            const SizedBox(
                              height: 25,
                            ),
                          ],
                        ),
                      ),
                    ),
                    // CustomLoaderButton(
                    //   // loginText: 'Login',
                    //   buttonWidth: isLoading
                    //       ? 45.0
                    //       : MediaQuery.of(context).size.width,
                    //   buttonRadius: 30.0,
                    //   buttonChild: isLoading
                    //       ? const CircularProgressIndicator(
                    //           valueColor:
                    //               AlwaysStoppedAnimation(Colors.white),
                    //           strokeWidth: 3.0,
                    //         )
                    //       : Text(
                    //           'Save',
                    //           style: lbBold.copyWith(
                    //             fontSize: 18,
                    //             color: AppConstants.primaryColor,
                    //           ),
                    //         ),
                    //   buttonPressed: () {
                    //     saveInfo();
                    //   },
                    // ),
                    // const SizedBox(
                    //   height: 25,
                    // ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _filterData(String? selectedOutcome) {
    setState(() {
      if (selectedOutcome == 'Yes') {
        isNotification = true;
      } else {
        isNotification = false;
      }
    });
  }

  Future<bool> saveInfo() async {
    FocusScope.of(context).requestFocus(FocusNode());
    bool validation = _formKey.currentState!.validate();
    if (validation &&
        nameController.text.isNotEmpty &&
        locationController.text.isNotEmpty &&
        bioController.text.isNotEmpty) {
      isLoading = true;

      var userData = UserProfileUpdateModel(
        userId: userId,
        userEmailId: logginedMail,
        userProfilePicture: _image.toString(),
        userName: nameController.text,
        userLocation: locationController.text,
        userBio: bioController.text,
        userClubInvitation: isNotification,
      );

      bool success =
          await Provider.of<ProfileController>(context, listen: false)
              .updateProfileFunction(userData, context);

      if (success) {
        await saveLocally().then(
          (value) {
            if (!widget.updateProfile) {
              if (mounted) {
                context.goNamed(
                  'ProfileScreen',
                );
                isLoading = false;

                setState(() {});
              }
            } else {
              if (context.mounted) {
                context.pop(true);
                isLoading = false;
                setState(() {});
              }
            }
          },
        );
        print("Profile updated successfully.");
        return true; // Indicate success
      }
    }
    return false;
  }

  Future<void> saveLocally() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setString('userName', nameController.text);
    pref.setString('userLocation', locationController.text);
    pref.setString('userBio', bioController.text);
    pref.setBool('isUserBioAndLocationAvailable', true);
  }
}
