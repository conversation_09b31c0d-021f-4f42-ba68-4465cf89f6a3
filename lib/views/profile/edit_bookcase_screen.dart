import 'dart:developer';

import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/paginated_book_typeahead.dart';
import 'package:eljunto/reusableWidgets/profile_appbar.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:eljunto/reusable_api_function/books_api_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_switch/flutter_advanced_switch.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:roundcheckbox/roundcheckbox.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/common_helper.dart';
import '../../constants/constants.dart';
import '../../models/profile_model/edit_bookcase/listof_book_model.dart';
import '../../reusableWidgets/no_data_widget.dart';

class EditBookCaseScreen extends StatefulWidget {
  final String? userName;
  final String? userHandler;
  final int? lengthofTopShelf;
  final bool? userClubInvitation;
  final String? userProfilePicture;

  const EditBookCaseScreen({
    super.key,
    this.userName,
    this.userHandler,
    this.lengthofTopShelf,
    this.userClubInvitation,
    this.userProfilePicture,
  });

  @override
  State<EditBookCaseScreen> createState() => _EditBookCaseScreenState();
}

class _EditBookCaseScreenState extends State<EditBookCaseScreen> {
  final List<String> _options = [
    BookCaseSortingOptions.title,
    BookCaseSortingOptions.completionDate,
    BookCaseSortingOptions.author,
    BookCaseSortingOptions.ratings
  ];

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController bookController = TextEditingController();
  List<Books>? bookList = [];
  TextEditingController roleController = TextEditingController();
  TextEditingController reviewController = TextEditingController();
  TextEditingController editReviewController = TextEditingController();
  TextEditingController editCompleteDateController = TextEditingController();
  TextEditingController monthyearController = TextEditingController();
  final unknownController = ValueNotifier<bool>(false);

  // bool bookValidation = false;
  bool monthyearValidation = false;
  bool istopShelfPopUpShow = false;
  bool ratingValidation = false;
  final selectTopShelfController = ValueNotifier<bool>(false);
  int? bookId;
  String? bookName;
  String? bookAuthor;
  bool isLoading = false;
  bool isBookIdNotEmpty = false;
  int? bookCaseId;

  String? readingCompleteDate;

  bool isUnknownChecked = true;
  bool alreadyExists = false;
  String responseMessage = '';
  Future<void>? loadAPIFunction;
  List<BookCaseModel>? bookCaseList = [];
  List<BookCaseModel>? completedBooks = [];
  List<BookCaseModel> currentlyReadingBooks = [];
  bool completeBookLoading = false;
  int offset = 0;
  int completeBookLimit = 10;
  int completeBookcount = 0;
  final ScrollController _bookCaseScrollController = ScrollController();
  SuggestionsController<Books>? suggestionsController;

  int limit = 30;
  int offSet = 0;

  @override
  void initState() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    _bookCaseScrollController.addListener(_bookCaseOnScroll);
    log("TopShelf Length : ${widget.lengthofTopShelf}");
    apiFunction();
    super.initState();

    roleController.text = BookCaseSortingOptions.title;
  }

  Future apiFunction() async {
    isLoading = true;
    setState(() {});
    await _initializeUserId();
    Future.wait([
      getBookCase(false),
    ]).then(
      (value) => setState(() {
        isLoading = false;
      }),
    );
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();
  }

  // // IMPROMPTU CLUB SCROLL
  void _bookCaseOnScroll() {
    if (_bookCaseScrollController.position.pixels >=
            _bookCaseScrollController.position.maxScrollExtent &&
        !completeBookLoading &&
        (completedBooks?.length ?? 0) < (completeBookcount)) {
      CommonHelper.networkClose(getBookCase(true), context);

      // getBookCase(true);
    }
  }

  Future<void> currentReadingAndBookCase(bool isMore) async {
    if ((completedBooks?.length ?? 0) <= completeBookcount || isMore) {
      completeBookLoading = true;

      if (isMore) {
        completeBookLimit += 10;
      }
    }
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .allBooksRead(loggedinUserId ?? 0, completeBookLimit, offset, false,
              true, context)
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookList = [];
          completeBookcount = responseMap['count'];
          setState(() {});
          bookList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
          log("Complete Book Length : ${bookList.length}");

          var result =
              CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
          log("Result List : ${result[2].length}");
          if (result.isNotEmpty) {
            // isLoading = false;
            // bookCaseList = result[0];
            currentlyReadingBooks = result[1];
            // completedBooks = result[2];
            log("Complete Book Length : ${completedBooks?.length}");
            completedBooks = CommonHelper.sortBookCaseList(
                completedBooks!, roleController.text);
          }
          bookCaseController?.notifyListeners();

          // print("Complete Book Length : ${completedBooks?.length}");
          if (result.length >= completeBookcount) {
            completeBookLoading = false;
          }
        }
      }).whenComplete(() {
        completeBookLoading = false;
      });
    } catch (e) {
      log(e.toString());
    }
  }

  BookCaseController? bookCaseController;

  Future<void> getBookCase(bool isMore) async {
    if ((completedBooks?.length ?? 0) <= completeBookcount || isMore) {
      completeBookLoading = true;

      if (isMore) {
        completeBookLimit += 10;
      }
    }
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .allBooksRead(loggedinUserId ?? 0, completeBookLimit, offset, false,
              false, context)
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookList = [];
          completeBookcount = responseMap['count'];
          bookList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
          log("Complete Book Length 11: ${bookList.length}");

          var result =
              CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
          log("Result List : ${result[2].length}");
          if (result.isNotEmpty) {
            // isLoading = false;
            bookCaseList = result[0];
            currentlyReadingBooks = result[1];
            completedBooks = result[2];

            log("Complete Book Length : ${completedBooks?.length}");
            completedBooks = CommonHelper.sortBookCaseList(
                completedBooks!, roleController.text);
          }
          bookCaseController?.notifyListeners();
          if (result.length >= completeBookcount) {
            completeBookLoading = false;
          }
        }
      }).whenComplete(() {
        completeBookLoading = false;
        setState(() {});
      });
    } catch (e) {
      log(e.toString());
    }
  }

  bool isAlreadyExist = false;

  Future<bool> confirmAddBook() async {
    String? date;

    if (selectedDate != null) {
      date = DateFormat("dd-MM-yyyy").format(selectedDate!);
    }
    BookCaseModel newBook = BookCaseModel(
      userId: loggedinUserId,
      bookId: bookId,
      bookAuthor: bookAuthor,
      bookName: bookName,
      is_currently_reading: false,
      ratings: ratingStar,
      review: reviewController.text,
      topShelf: null,
      reading_complete_date_String: date,
      toBeRead: null,
    );

    await Provider.of<BookCaseController>(context, listen: false)
        .addBookInBookCase(newBook, context)
        .then((value) {
      final tempMessage = bookCaseController?.addBookErrorMessage ?? '';

      if (value == 'exist') {
        if (tempMessage.toLowerCase().contains('currently')) {
          isAlreadyExist = true;
        } else {
          isAlreadyExist = false;
        }
        responseMessage = bookCaseController?.addBookErrorMessage ?? '';
        bookCaseId = bookCaseController?.bookCaseId;
        alreadyExists = true;
      } else {
        alreadyExists = false;
      }
      log('alreadyExists : $alreadyExists');
      setState(() {});
    });
    // }
    // }
    return alreadyExists;
  }

  Future<void> addBookInBookCase() async {
    String? date;

    if (selectedDate != null) {
      date = DateFormat("dd-MM-yyyy").format(selectedDate!);
    }
    BookCaseModel newBook = BookCaseModel(
      userId: loggedinUserId,
      bookId: bookId,
      bookCaseId: bookCaseId,
      is_currently_reading: false,
      ratings: ratingStar,
      review: reviewController.text,
      topShelf: selectTopShelfController.value,
      reading_complete_date_String: date,
    );
    await Provider.of<BookCaseController>(context, listen: false)
        .updateBookCase(newBook, context)
        .then((value) {});
  }

  double ratingStar = 0;
  int? loggedinUserId;

  Future<void> sortList(String role) async {
    completedBooks = CommonHelper.sortBookCaseList(completedBooks!, role);
  }

  Future<void> confirmDelete(int? bookId) async {
    await Provider.of<BookCaseController>(context, listen: false)
        .deleteBook(bookId, context)
        .then(
      (value) async {
        if (value) {
          await getBookCase(false);
        }
      },
    );
  }

  Future<void> confirmUpdateBook(int? bookCaseId, int? bookIds) async {
    String? date;
    int reRead =
        completedBooks?.firstWhere((book) => book.bookId == bookIds).reRead ??
            0;
    if (selectedDate != null) {
      date = DateFormat("dd-MM-yyyy").format(selectedDate!);
    } else {
      date = '';
    }

    final obj = BookCaseModel(
      bookId: bookIds,
      //is_currently_reading: true,
      userId: loggedinUserId,
      ratings: ratingStar,
      bookCaseId: bookCaseId,
      review: reviewController.text,
      topShelf: selectTopShelfController.value,
      reading_complete_date_String: date,
      reRead: reRead,
    );

    await Provider.of<BookCaseController>(context, listen: false)
        .updateBookCase(obj, context);
  }

  DateTime? selectedDate;
  String? readBookDate;

  @override
  void dispose() {
    suggestionsController?.dispose();
    bookController.dispose();
    reviewController.dispose();
    roleController.dispose();
    editReviewController.dispose();
    editCompleteDateController.dispose();
    selectTopShelfController.dispose();
    monthyearController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: Skeletonizer(
            effect: const SoldColorEffect(
              color: AppConstants.skeletonforgroundColor,
              lowerBound: 0.1,
              upperBound: 0.5,
            ),
            containersColor: AppConstants.textGreenColor,
            enabled: isLoading,
            child: ProfileAppBar(
              userName: widget.userName,
              userHandle: widget.userHandler,
              isOpenToClubInvitation: widget.userClubInvitation ?? false,
              userOwnProfile: true,
              userProfilePicture: widget.userProfilePicture,
            ),
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.skeletonBackgroundColor,
          enabled: isLoading,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        "All Books Read",
                        style: lbRegular.copyWith(
                          fontSize: 20,
                          // decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                    Consumer<BookCaseController>(
                      builder: (context, value, child) {
                        return Text(
                          "$completeBookcount-Books",
                          overflow: TextOverflow.ellipsis,
                          style: lbItalic.copyWith(
                            fontSize: 20,
                            // decoration: TextDecoration.underline,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      height: 45,
                      width: MediaQuery.of(context).size.width / 2.3,
                      child: DropdownMenu(
                        // initialSelection: 'Title A-Z',
                        inputDecorationTheme: const InputDecorationTheme(
                          alignLabelWithHint: true,
                          contentPadding: EdgeInsets.symmetric(
                            // vertical: BorderSide.strokeAlignCenter,
                            horizontal: 10,
                          ),
                          filled: true,
                          fillColor: AppConstants.backgroundColor,
                          focusColor: AppConstants.primaryColor,
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: AppConstants.primaryColor,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.all(
                              Radius.circular(10),
                            ),
                          ),
                        ),

                        menuStyle: const MenuStyle(
                          surfaceTintColor:
                              WidgetStatePropertyAll(Colors.transparent),
                          shape: WidgetStatePropertyAll(
                            RoundedRectangleBorder(
                              side: BorderSide(
                                color: AppConstants.primaryColor,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.all(
                                Radius.circular(10),
                              ),
                            ),
                          ),
                          padding: WidgetStatePropertyAll(
                            EdgeInsets.zero,
                          ),
                          backgroundColor: WidgetStatePropertyAll(
                            AppConstants.backgroundColor,
                          ),
                        ),

                        expandedInsets: EdgeInsets.zero,
                        controller: roleController,
                        requestFocusOnTap: false,
                        textStyle: lbRegular.copyWith(
                          fontSize: 12,
                        ),
                        onSelected: (newValue) async {
                          roleController.text = newValue.toString();
                          await sortList(roleController.text);
                          setState(() {});
                        },
                        dropdownMenuEntries: _options.map((role) {
                          return DropdownMenuEntry(
                            style: TextButton.styleFrom(
                              visualDensity: VisualDensity.comfortable,
                              side: const BorderSide(
                                width: 0.5,
                                color: AppConstants.primaryColor,
                              ),
                              textStyle: lbRegular.copyWith(
                                overflow: TextOverflow.ellipsis,
                                fontSize: 12,
                              ),
                            ),
                            value: role,
                            label: role,
                          );
                        }).toList(),
                        menuHeight: 200,
                      ),
                    ),
                    NetworkAwareTap(
                      onTap: () async {
                        await showAddEditBookPopUp(() {
                          setState(() {});
                        }, false);
                      },
                      child: Text(
                        "Add new book +",
                        overflow: TextOverflow.ellipsis,
                        style: lbItalic.copyWith(
                          fontSize: 16,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 25,
              ),
              completedBooks?.isNotEmpty ?? false
                  ? Expanded(
                      child: Consumer<BookCaseController>(
                          builder: (context, bookCaseController, child) {
                        return GridView.builder(
                          controller: _bookCaseScrollController,
                          padding: const EdgeInsets.only(
                              left: 20, right: 20, bottom: 25),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 15,
                            mainAxisSpacing: 15,
                            childAspectRatio: 0.80,
                          ),
                          itemCount: completedBooks?.length,
                          itemBuilder: (BuildContext context, int index) {
                            if (index == completedBooks?.length &&
                                completeBookLoading) {
                              return const Padding(
                                padding: EdgeInsets.only(left: 10.0),
                                child: Center(
                                  child: CircularProgressIndicator(
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                              );
                            }

                            final bookData = completedBooks?[index];
                            String completedBookTime = "";
                            if (bookData?.readingCompleteDate != null) {
                              completedBookTime = DateFormat('MMM yyyy').format(
                                DateTime.fromMillisecondsSinceEpoch(
                                    bookData?.readingCompleteDate ?? 0),
                              );
                            } else {
                              completedBookTime = "Unknown";
                            }
                            return Skeleton.replace(
                              replacement: Container(
                                decoration: BoxDecoration(
                                  color: AppConstants.skeletonBackgroundColor,
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: AppConstants.primaryColor,
                                    width: 1.5,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      MarqueeList(
                                        children: [
                                          Text(
                                            completedBooks?[index].bookName ??
                                                '',
                                            textAlign: TextAlign.start,
                                            overflow: TextOverflow.ellipsis,
                                            style: lbBold.copyWith(
                                              fontSize: 18,
                                            ),
                                          ),
                                        ],
                                      ),
                                      // const SizedBox(
                                      //   height: 10,
                                      // ),
                                      Text(
                                        completedBooks?[index].bookAuthor ?? '',
                                        textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                        style: lbRegular.copyWith(
                                          fontSize: 14,
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      Skeleton.replace(
                                        replacement: RatingBar(
                                          itemPadding:
                                              const EdgeInsets.only(left: 2),
                                          itemCount: 5,
                                          itemSize: 22,
                                          allowHalfRating: true,
                                          initialRating:
                                              completedBooks?[index].ratings ??
                                                  0,
                                          maxRating:
                                              completedBooks?[index].ratings ??
                                                  0,
                                          unratedColor: Colors.red,
                                          ratingWidget: RatingWidget(
                                              full: const Icon(
                                                Icons.star,
                                                color:
                                                    AppConstants.textGreenColor,
                                              ),
                                              half: const Icon(
                                                Icons.star_half,
                                                color:
                                                    AppConstants.textGreenColor,
                                              ),
                                              empty: const Icon(
                                                Icons.star_border_outlined,
                                                color:
                                                    AppConstants.textGreenColor,
                                              )),
                                          onRatingUpdate: (double value) {},
                                        ),
                                        child: RatingBar(
                                          itemCount: 5,
                                          itemSize: 22,
                                          allowHalfRating: true,
                                          initialRating:
                                              completedBooks?[index].ratings ??
                                                  0,
                                          maxRating:
                                              completedBooks?[index].ratings ??
                                                  0,
                                          unratedColor: Colors.red,
                                          ratingWidget: RatingWidget(
                                              full: const Icon(
                                                Icons.star,
                                                color:
                                                    AppConstants.textGreenColor,
                                              ),
                                              half: const Icon(
                                                Icons.star_half,
                                                color:
                                                    AppConstants.textGreenColor,
                                              ),
                                              empty: const Icon(
                                                Icons.star_border_outlined,
                                                color:
                                                    AppConstants.textGreenColor,
                                              )),
                                          onRatingUpdate: (double value) {},
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      Row(
                                        children: [
                                          completedBooks?[index].topShelf ==
                                                  true
                                              ? Image.asset(
                                                  AppConstants.topShelfBookIcon,
                                                  height: 20,
                                                  width: 22,
                                                  filterQuality:
                                                      FilterQuality.high,
                                                )
                                              : const SizedBox.shrink(),
                                          const SizedBox(
                                            width: 5,
                                          ),
                                          completedBooks?[index]
                                                      .is_currently_reading ==
                                                  true
                                              ? Image.asset(
                                                  AppConstants
                                                      .currentlyReadingIcon,
                                                  height: 20,
                                                  width: 22,
                                                  filterQuality:
                                                      FilterQuality.high,
                                                )
                                              : const SizedBox.shrink(),
                                        ],
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      NetworkAwareTap(
                                        onTap: () {
                                          final bookName =
                                              completedBooks?[index].bookName ??
                                                  '';
                                          final bookAuthor =
                                              completedBooks?[index]
                                                      .bookAuthor ??
                                                  '';
                                          final review =
                                              completedBooks?[index].review ??
                                                  '';
                                          readReviewFunction(
                                              bookName, bookAuthor, review);
                                        },
                                        child: Text(
                                          "Read Review",
                                          textAlign: TextAlign.center,
                                          style: lbItalic.copyWith(
                                            fontSize: 12,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      NetworkAwareTap(
                                        onTap: () async {
                                          await showAddEditBookPopUp(
                                            () {
                                              setState(() {});
                                            },
                                            true,
                                            index,
                                            completedBooks?[index],
                                          );
                                          // .then((value) => setState(() {}));
                                        },
                                        child: Text(
                                          "Edit",
                                          textAlign: TextAlign.center,
                                          style: lbItalic.copyWith(
                                            fontSize: 12,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      Text(
                                        "Completed: $completedBookTime",
                                        // textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                        style: lbRegular.copyWith(
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: AppConstants.primaryColor,
                                    width: 1.5,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      MarqueeList(
                                        children: [
                                          Text(
                                            completedBooks?[index].bookName ??
                                                '',
                                            textAlign: TextAlign.start,
                                            overflow: TextOverflow.ellipsis,
                                            style: lbBold.copyWith(
                                              fontSize: 18,
                                            ),
                                          ),
                                        ],
                                      ),
                                      // const SizedBox(height: 5),
                                      MarqueeList(
                                        children: [
                                          Text(
                                            completedBooks?[index].bookAuthor ??
                                                '',
                                            textAlign: TextAlign.center,
                                            overflow: TextOverflow.ellipsis,
                                            style: lbRegular.copyWith(
                                              fontSize: 14,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      RatingBar(
                                        ignoreGestures: true,
                                        itemCount: 5,
                                        itemSize: 22,
                                        allowHalfRating: true,
                                        initialRating:
                                            completedBooks?[index].ratings ?? 0,
                                        maxRating:
                                            completedBooks?[index].ratings ?? 0,
                                        unratedColor: Colors.red,
                                        ratingWidget: RatingWidget(
                                            full: const Icon(
                                              Icons.star,
                                              color:
                                                  AppConstants.textGreenColor,
                                            ),
                                            half: const Icon(
                                              Icons.star_half,
                                              color:
                                                  AppConstants.textGreenColor,
                                            ),
                                            empty: const Icon(
                                              Icons.star_border_outlined,
                                              color:
                                                  AppConstants.textGreenColor,
                                            )),
                                        onRatingUpdate: (double value) {},
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      Row(
                                        children: [
                                          completedBooks?[index].topShelf ==
                                                  true
                                              ? Image.asset(
                                                  AppConstants.topShelfBookIcon,
                                                  height: 20,
                                                  width: 22,
                                                  filterQuality:
                                                      FilterQuality.high,
                                                )
                                              : const SizedBox.shrink(),
                                          completedBooks?[index].topShelf ==
                                                  true
                                              ? const SizedBox(
                                                  width: 10,
                                                )
                                              : const SizedBox.shrink(),
                                          completedBooks?[index]
                                                      .is_currently_reading ==
                                                  true
                                              ? Image.asset(
                                                  AppConstants
                                                      .currentlyReadingIcon,
                                                  height: 20,
                                                  width: 22,
                                                  filterQuality:
                                                      FilterQuality.high,
                                                )
                                              : const SizedBox(
                                                  height: 20,
                                                ),
                                        ],
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      NetworkAwareTap(
                                        onTap: () {
                                          final bookName =
                                              completedBooks?[index].bookName ??
                                                  '';
                                          final bookAuthor =
                                              completedBooks?[index]
                                                      .bookAuthor ??
                                                  '';
                                          final review =
                                              completedBooks?[index].review ??
                                                  '';
                                          readReviewFunction(
                                              bookName, bookAuthor, review);
                                        },
                                        child: Text(
                                          "Read Review",
                                          textAlign: TextAlign.center,
                                          style: lbItalic.copyWith(
                                            fontSize: 12,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      NetworkAwareTap(
                                        onTap: () async {
                                          await showAddEditBookPopUp(
                                            () {
                                              setState(() {});
                                            },
                                            true,
                                            index,
                                            completedBooks?[index],
                                          );
                                          // .then((value) => setState(() {}));
                                        },
                                        child: Text(
                                          "Edit",
                                          textAlign: TextAlign.center,
                                          style: lbItalic.copyWith(
                                            fontSize: 12,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 5,
                                      ),
                                      Text(
                                        "Completed: $completedBookTime",
                                        // textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                        style: lbRegular.copyWith(
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      }),
                    )
                  : Skeleton.replace(
                      replacement: Container(
                        padding: const EdgeInsets.only(left: 20),
                        margin: const EdgeInsets.symmetric(horizontal: 20),
                        height: 50,
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: AppConstants.skeletonBackgroundColor,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "No books read",
                            textAlign: TextAlign.start,
                          ),
                        ),
                      ),
                      child: const NoDataWidget(
                        message: "No books read",
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> showAddEditBookPopUp(
    Function updateUI, [
    bool? isEditMode,
    int? index,
    //ProfileBookCase? object,
    BookCaseModel? object,
  ]) async {
    suggestionsController?.dispose();
    suggestionsController = null;
    suggestionsController = SuggestionsController();

    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        ratingValidation = false;
        bookController.clear();
        monthyearController.clear();
        reviewController.clear();
        bookCaseController?.updateTypeAheadFlag(false);
        monthyearValidation = false;
        unknownController.value = false;
        isBookIdNotEmpty = false;

        bookId = 0;
        if (isEditMode!) {
          isBookIdNotEmpty = true;
          bookController.text = object?.bookName ?? '';
          if (object?.readingCompleteDate != null) {
            monthyearController.text = DateFormat("MMM yyyy").format(
              DateTime.fromMillisecondsSinceEpoch(
                  object?.readingCompleteDate ?? 0),
            );
          } else {
            // monthyearController.text = 'Unknown';
            unknownController.value = true;
          }

          readingCompleteDate = DateFormat("dd-MM-yyyy").format(
            DateTime.fromMillisecondsSinceEpoch(
                object?.readingCompleteDate ?? 0),
          );
          // print("Date : $readingCompleteDate");
          ratingStar = object?.ratings ?? 0;
          selectedDate = DateTime.fromMillisecondsSinceEpoch(
              object?.readingCompleteDate ?? 0);
          // print("Like : $ratingStar");
          reviewController.text = object?.review ?? '';
        } else {
          ratingStar = 0;
          unknownController.value = true;
        }
        selectTopShelfController.value = object?.topShelf ?? false;
        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Center(
            child: SingleChildScrollView(
              child: StatefulBuilder(
                builder: (context, setState) {
                  return AlertDialog(
                    actionsPadding: const EdgeInsets.only(right: 10),
                    insetPadding: const EdgeInsets.all(25),
                    contentPadding: EdgeInsets.zero,
                    backgroundColor: AppConstants.backgroundColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                      side: const BorderSide(
                        color: AppConstants.popUpBorderColor,
                        width: 1.5,
                      ),
                    ),
                    surfaceTintColor: Colors.white,
                    actions: [
                      Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            NetworkAwareTap(
                              onTap: () {
                                bookController.clear(); // Clear when closing
                                suggestionsController?.dispose();
                                suggestionsController = null;
                                context.pop();
                              },
                              child: Container(
                                alignment: Alignment.centerRight,
                                padding: const EdgeInsets.only(top: 10),
                                child: Image.asset(
                                  AppConstants.closePopupImagePath,
                                  height: 30,
                                  width: 30,
                                ),
                              ),
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.only(left: 22.0, right: 12),
                              child: Column(
                                children: [
                                  SizedBox(
                                    width: MediaQuery.of(context).size.width,
                                    child: Text(
                                      isEditMode ? "Edit Book" : "Add New Book",
                                      textAlign: TextAlign.center,
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 25,
                                  ),
                                  !isEditMode
                                      ? Stack(
                                          clipBehavior: Clip.none,
                                          children: [
                                            typeAheadField(),
                                            positioned(),
                                            booknotSelect()
                                          ],
                                        )
                                      : const SizedBox.shrink(),
                                  isEditMode
                                      ? Text(
                                          "Book: ${object?.bookName}, ${object?.bookAuthor}",
                                          textAlign: TextAlign.center,
                                          style: lbRegular.copyWith(
                                            fontSize: 12,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                  Consumer<BookCaseController>(builder:
                                      (context, bookCaseController, child) {
                                    return bookCaseController.isTypeAheadEmpty
                                        ? const SizedBox(
                                            height: 25,
                                          )
                                        : const SizedBox.shrink();
                                  }),
                                  isBookIdNotEmpty
                                      ? const SizedBox(
                                          height: 25,
                                        )
                                      : const SizedBox.shrink(),
                                  const SizedBox(
                                    height: 25,
                                  ),
                                  Text(
                                    isEditMode ? "Completed:" : "Last Read:",
                                    textAlign: TextAlign.center,
                                    style: lbRegular.copyWith(
                                      fontSize: 12,
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Select completion date:",
                                        textAlign: TextAlign.center,
                                        style: lbRegular.copyWith(
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Stack(
                                    clipBehavior: Clip.none,
                                    children: [
                                      datePickerFunction(),
                                      Positioned(
                                        top: 55,
                                        left: 0,
                                        right: 0,
                                        child: monthyearValidation
                                            ? Text(
                                                "*Select a date or check 'Unknown'",
                                                style: lbRegular.copyWith(
                                                  fontSize: 14,
                                                  color: AppConstants.redColor,
                                                ),
                                              )
                                            : const SizedBox.shrink(),
                                      ),
                                    ],
                                  ),
                                  monthyearValidation
                                      ? const SizedBox(
                                          height: 25,
                                        )
                                      : const SizedBox.shrink(),
                                  const SizedBox(
                                    height: 15,
                                  ),
                                  ratingWidget(),
                                  const SizedBox(
                                    height: 15,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Up to 2000 Character Review (Optional): ",
                                        textAlign: TextAlign.center,
                                        style: lbRegular.copyWith(
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  TextFormField(
                                    controller: reviewController,
                                    textCapitalization:
                                        TextCapitalization.sentences,
                                    maxLines: 4,
                                    maxLength: 2000,
                                    style: lbRegular.copyWith(
                                      fontSize: 12,
                                    ),
                                    decoration: InputDecoration(
                                      contentPadding: const EdgeInsets.all(10),
                                      fillColor: const Color.fromRGBO(
                                          255, 255, 255, 1),
                                      filled: true,
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(5),
                                        borderSide: const BorderSide(
                                          color: AppConstants.primaryColor,
                                          width: 1.5,
                                        ),
                                      ),
                                      counterStyle: lbRegular.copyWith(
                                        fontSize: 14,
                                      ),
                                    ),
                                    // keyboardType: TextInputType.text,
                                  ),
                                  const SizedBox(
                                    height: 25,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      NetworkAwareTap(
                                        onTap: () async {
                                          bool validation =
                                              _formKey.currentState!.validate();
                                          if (bookController.text.isEmpty) {
                                            setState(
                                              () {
                                                bookCaseController
                                                    ?.updateTypeAheadFlag(true);
                                              },
                                            );
                                            return;
                                          }

                                          if (isBookIdNotEmpty && validation) {
                                            if (monthyearController
                                                    .text.isEmpty &&
                                                unknownController.value ==
                                                    false) {
                                              setState(
                                                () {
                                                  monthyearValidation = true;
                                                },
                                              );
                                            } else if (ratingStar == 0) {
                                              setState(
                                                () {
                                                  ratingValidation = true;
                                                },
                                              );
                                            } else {
                                              if (isEditMode) {
                                                await confirmUpdateBook(
                                                  object?.bookCaseId,
                                                  object?.bookId,
                                                ).then((value) async {
                                                  await getBookCase(false);
                                                  if (context.mounted) {
                                                    context.pop();
                                                    // updateUI();
                                                  }
                                                });
                                              } else {
                                                await confirmAddBook().then(
                                                  (value) async {
                                                    if (value) {
                                                      if (context.mounted) {
                                                        context.pop();
                                                      }
                                                      await addNewBook();

                                                      // await showDialog(
                                                      //   barrierColor:
                                                      //       Colors.white60,
                                                      //   context: context,
                                                      //   barrierDismissible:
                                                      //       false,

                                                      //   builder: (BuildContext
                                                      //       context) {
                                                      //     return CustomDialog(
                                                      //       title:
                                                      //           "Add New Book",
                                                      //       message:
                                                      //           responseMessage,
                                                      //       showDoneImage:
                                                      //           false,
                                                      //     );
                                                      //   },
                                                      // );
                                                    }
                                                    await getBookCase(false);
                                                    if (context.mounted) {
                                                      context.pop();
                                                      // updateUI();
                                                    }
                                                  },
                                                ).then((_) {});
                                              }
                                            }
                                          } else {
                                            setState(
                                              () {
                                                isBookIdNotEmpty = true;
                                              },
                                            );
                                          }
                                        },
                                        child: Container(
                                          height: 45,
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width /
                                              3,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(49),
                                            color: AppConstants.textGreenColor,
                                          ),
                                          child: Center(
                                            child: Text(
                                              isEditMode ? "Save" : "Add",
                                              textAlign: TextAlign.center,
                                              style: lbBold.copyWith(
                                                fontSize: 18,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      NetworkAwareTap(
                                        onTap: () {
                                          context.pop();
                                          // notSentInvitationFunction();
                                        },
                                        child: Container(
                                          height: 45,
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width /
                                              3,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(49),
                                            color: AppConstants.backgroundColor,
                                            border: Border.all(
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                          child: Center(
                                            child: Text(
                                              "Cancel",
                                              textAlign: TextAlign.center,
                                              style: lbBold.copyWith(
                                                fontSize: 18,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  isEditMode
                                      ? const SizedBox(
                                          height: 25,
                                        )
                                      : const SizedBox.shrink(),
                                  isEditMode
                                      ? NetworkAwareTap(
                                          onTap: () async {
                                            await showDeletePopUp(index ?? 0);
                                            if (context.mounted) {
                                              context.pop();
                                            }
                                          },
                                          child: Container(
                                            height: 45,
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(49),
                                              color:
                                                  AppConstants.textGreenColor,
                                            ),
                                            child: Center(
                                              child: Text(
                                                "Delete Book",
                                                textAlign: TextAlign.center,
                                                style: lbBold.copyWith(
                                                  fontSize: 18,
                                                ),
                                              ),
                                            ),
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                  const SizedBox(
                                    height: 30,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> addNewBook() async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            NetworkAwareTap(
              onTap: () {
                context.pop();
              },
              child: Container(
                alignment: Alignment.centerRight,
                padding: const EdgeInsets.only(top: 10),
                child: Image.asset(
                  AppConstants.closePopupImagePath,
                  height: 30,
                  width: 30,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 30.0, right: 20),
              child: Column(
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Add to Bookcase",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          responseMessage,
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  alreadyExists && isAlreadyExist
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            NetworkAwareTap(
                              onTap: () async {
                                // await getBookCaseId(bookId).then((value) {
                                // bookCaseId = value;
                                await addBookInBookCase();
                                // });
                                if (context.mounted) {
                                  context.pop();
                                }
                              },
                              child: Container(
                                height: 45,
                                width: MediaQuery.of(context).size.width / 3,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(49),
                                  color: AppConstants.textGreenColor,
                                ),
                                child: Center(
                                  child: Text(
                                    "Move",
                                    textAlign: TextAlign.center,
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            NetworkAwareTap(
                              onTap: () {
                                context.pop();
                                // notSentInvitationFunction();
                              },
                              child: Container(
                                height: 45,
                                width: MediaQuery.of(context).size.width / 3,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(49),
                                  color: AppConstants.backgroundColor,
                                  border: Border.all(
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    "Cancel",
                                    textAlign: TextAlign.center,
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )
                      : NetworkAwareTap(
                          onTap: () {
                            context.pop();
                          },
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width / 3,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.textGreenColor,
                            ),
                            child: Center(
                              child: Text(
                                "Ok",
                                textAlign: TextAlign.center,
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              ),
            )
          ],
        );
      },
    );
  }

  Future<void> topShelfFullFunction() async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Add to Topshelf",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          "You already have 20 books in your topshelf",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          "Remove a book from your topshelf first to add this book.",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Future<void> readReviewFunction(
      String? bookName, String? bookAuthor, String? review) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        reviewController.clear();
        reviewController.text = review ?? '';
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Review:",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          "$bookName, $bookAuthor",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: TextFormField(
                    controller: reviewController,
                    textCapitalization: TextCapitalization.sentences,
                    readOnly: true,
                    maxLines: 4,
                    maxLength: 2000,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.all(10),
                      fillColor: AppConstants.backgroundColor,
                      filled: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(
                          color: AppConstants.primaryColor,
                          width: 1.5,
                        ),
                      ),
                      counterStyle: lbRegular.copyWith(
                        fontSize: 14,
                      ),
                      hintText: "No review",
                      hintStyle: lbRegular.copyWith(
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () async {
                    if (context.mounted) {
                      context.pop();
                    }
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Future<void> showDeletePopUp(int index) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Delete Book:",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          "Are you sure you want to delete this book?",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () async {
                          final bookId = completedBooks?[index].bookId;
                          await confirmDelete(bookId);
                          // setState(() {});
                          if (context.mounted) {
                            context.pop();
                          }
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Delete",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          // notSentInvitationFunction();
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.backgroundColor,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Widget typeAheadField() {
    return PaginatedBookTypeahead(
      suggestionsController: suggestionsController,
      controller: bookController,
      fetchBooksCallback: (query, offset, limit) =>
          BooksApiFunctions.fetchBooks(
        query,
        offset,
        limit,
        context,
        loggedinUserId,
      ),
      onSelected: (book) {
        if (book.bookName == "Can't Find Your Book? Click Here") {
          questionFeedBox();
        } else {
          _handleBookSelection(book);
        }
      },
    );
  }

  void _handleBookSelection(Books book) {
    setState(() {
      bookController.text = book.bookName.toString();
      bookId = book.bookId;
      bookName = book.bookName ?? '';
      bookAuthor = book.bookAuthor ?? '';
      isBookIdNotEmpty = true;
    });
  }

  void questionFeedBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const QuestionFeedbackDialog(isBookQuery: true);
      },
    );
  }

  Widget positioned() {
    log("Validation Flag : ${bookCaseController?.isTypeAheadEmpty}");
    return Consumer<BookCaseController>(
        builder: (context, bookCaseController, child) {
      return Positioned(
        top: 55,
        left: 0,
        right: 0,
        child: bookCaseController.isTypeAheadEmpty
            ? Text(
                '*Select a book',
                style: lbRegular.copyWith(
                  fontSize: 14,
                  color: AppConstants.redColor,
                ),
              )
            : const SizedBox.shrink(),
      );
    });
  }

  Widget booknotSelect() {
    return Positioned(
      top: 55,
      left: 0,
      right: 0,
      child: isBookIdNotEmpty && bookId == 0
          ? Text(
              '*Invalid book',
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            )
          : const SizedBox.shrink(),
    );
  }

  Widget datePickerFunction() {
    return StatefulBuilder(builder: (context, setState) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            // height: 30,
            width: MediaQuery.of(context).size.width / 2.8,
            child: TextFormField(
              controller: monthyearController,
              style: lbRegular.copyWith(
                fontSize: 12,
              ),
              decoration: InputDecoration(
                suffixIcon: const Icon(
                  Icons.calendar_month_outlined,
                  size: 20,
                ),
                contentPadding: const EdgeInsets.all(10),
                fillColor: const Color.fromRGBO(255, 255, 255, 1),
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5),
                  borderSide: const BorderSide(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
              ),
              onChanged: (value) {
                setState(
                  () {
                    monthyearValidation = false;
                  },
                );
              },
              onTap: () async {
                selectedDate = await CommonHelper.getMonthYear(context);
                setState(() {
                  if (selectedDate != null) {
                    monthyearValidation = false;
                    unknownController.value = false;
                    monthyearController.text =
                        DateFormat('MMM yyyy').format(selectedDate!);
                    // print("Date : ${monthyearController.text}");
                    readingCompleteDate = monthyearController.text;
                  } else {
                    monthyearController.clear();
                    readingCompleteDate = '';
                  }
                });
              },
              readOnly: true,
            ),
          ),
          const SizedBox(
            width: 25,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "Unknown",
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                  fontSize: 12,
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              RoundCheckBox(
                isChecked: unknownController.value,
                border: Border.all(color: Colors.transparent),
                onTap: (p0) {
                  setState(
                    () {
                      unknownController.value = p0!;
                      if (unknownController.value) {
                        monthyearValidation = false;
                        selectedDate = null;
                        monthyearController.clear();
                      }

                      print("UnKnown value : ${unknownController.value}");
                      monthyearValidation = false;
                    },
                  );
                },
                checkedWidget: const Icon(
                  Icons.check_circle_outline_rounded,
                  color: AppConstants.primaryColor,
                ),
                checkedColor: AppConstants.backgroundColor,
                uncheckedColor: AppConstants.backgroundColor,
                uncheckedWidget: const Icon(
                  Icons.circle_outlined,
                  color: AppConstants.primaryColor,
                ),
              ),
            ],
          ),
        ],
      );
    });
  }

  Widget ratingWidget() {
    return StatefulBuilder(builder: (context, setState) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            "Ratings:",
            textAlign: TextAlign.center,
            style: lbRegular.copyWith(
              fontSize: 12,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          RatingBar(
            glow: false,
            itemCount: 5,
            itemSize: 25,
            allowHalfRating: true,
            initialRating: ratingStar,
            minRating: 0,
            ratingWidget: RatingWidget(
                full: const Icon(
                  Icons.star,
                  color: AppConstants.textGreenColor,
                ),
                half: const Icon(
                  Icons.star_half,
                  color: AppConstants.textGreenColor,
                ),
                empty: const Icon(
                  Icons.star_border_outlined,
                  color: AppConstants.textGreenColor,
                )),
            onRatingUpdate: (double value) {
              setState(() {
                ratingValidation = false;
                ratingStar = value;
                print("Rating : $ratingStar");
              });
            },
          ),
          const SizedBox(
            width: 5,
          ),
          ratingValidation
              ? Text(
                  '*',
                  style: lbRegular.copyWith(
                    color: AppConstants.redColor,
                    fontSize: 20,
                  ),
                )
              : const SizedBox.shrink(),
        ],
      );
    });
  }

  Widget topShelfWidget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "Top Shelf (Upto 20):",
          textAlign: TextAlign.center,
          style: lbRegular.copyWith(
            fontSize: 12,
          ),
        ),
        const SizedBox(
          width: 10,
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            border: Border.all(color: AppConstants.primaryColor),
          ),
          child: AdvancedSwitch(
            controller: selectTopShelfController,
            width: 40,
            height: 21,
            initialValue: selectTopShelfController.value,
            thumb: ValueListenableBuilder(
              valueListenable: selectTopShelfController,
              builder: (context, val, _) {
                return Container(
                  height: 10,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: AppConstants.primaryColor),
                    borderRadius: BorderRadius.circular(50),
                  ),
                );
              },
            ),
            borderRadius: BorderRadius.circular(20),
            activeColor: AppConstants.textGreenColor,
            inactiveColor: Colors.transparent,
            onChanged: (value) async {
              if (istopShelfPopUpShow) return;
              if (value) {
                if (completedBooks!.length < 20) {
                  selectTopShelfController.value = value;
                } else {
                  istopShelfPopUpShow = true;
                  await topShelfFullFunction();
                  selectTopShelfController.value = false;
                  istopShelfPopUpShow = false;
                }
              } else {
                selectTopShelfController.value = false;
              }
              // print(selectTopShelfController.value);
            },
          ),
        ),
      ],
    );
  }
}
