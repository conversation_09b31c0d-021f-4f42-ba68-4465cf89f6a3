import 'package:flutter/material.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/text_style.dart';
import '../../../../models/book_case_model.dart';
import '../../../../reusableWidgets/connection_error/network_aware_tap.dart';
import '../../../../reusableWidgets/marquee_text.dart';

/// Reusable book list item widget for bookcase screens
class BookListItem extends StatelessWidget {
  final BookCaseModel book;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onMove;
  final String? editText;
  final String? moveText;
  final bool showSkeleton;
  final BookListItemType type;

  const BookListItem({
    super.key,
    required this.book,
    this.onEdit,
    this.onDelete,
    this.onMove,
    this.editText,
    this.moveText,
    this.showSkeleton = false,
    this.type = BookListItemType.currentReading,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        minHeight: 100,
        maxHeight: MediaQuery.of(context).size.height * 0.15,
      ),
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.only(top: 25, left: 20, right: 20),
      decoration: BoxDecoration(
        color: showSkeleton
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: showSkeleton ? Colors.transparent : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: IntrinsicHeight(
        child: Padding(
          padding: const EdgeInsets.all(14.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBookTitle(),
              const SizedBox(height: 10),
              _buildBookAuthor(),
              const SizedBox(height: 10),
              _buildActionRow(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookTitle() {
    if (type == BookListItemType.currentReading) {
      return MarqueeList(
        children: [
          Text(
            book.bookName ?? '',
            overflow: TextOverflow.ellipsis,
            style: lbBold.copyWith(fontSize: 18),
          ),
        ],
      );
    } else {
      return Text(
        book.bookName ?? '',
        overflow: TextOverflow.ellipsis,
        style: lbBold.copyWith(
          fontSize: 18,
          color: AppConstants.primaryColor,
        ),
      );
    }
  }

  Widget _buildBookAuthor() {
    return Text(
      book.bookAuthor ?? '',
      overflow: TextOverflow.ellipsis,
      style: lbRegular.copyWith(
        fontSize: 14,
        color: type == BookListItemType.toBeRead
            ? AppConstants.primaryColor
            : null,
      ),
    );
  }

  Widget _buildActionRow() {
    if (type == BookListItemType.currentReading) {
      return Row(
        children: [
          if (editText != null && editText!.isNotEmpty && onEdit != null)
            NetworkAwareTap(
              onTap: onEdit!,
              child: Text(
                editText!,
                overflow: TextOverflow.ellipsis,
                style: lbItalic.copyWith(
                  decoration: TextDecoration.underline,
                  fontSize: 14,
                ),
              ),
            ),
          const Spacer(),
          if (onDelete != null)
            NetworkAwareTap(
              onTap: onDelete!,
              child: Text(
                'Delete',
                overflow: TextOverflow.ellipsis,
                style: lbItalic.copyWith(
                  decorationColor: AppConstants.redColor,
                  decoration: TextDecoration.underline,
                  fontSize: 14,
                  color: AppConstants.redColor,
                ),
              ),
            ),
        ],
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (moveText != null && onMove != null)
            NetworkAwareTap(
              onTap: onMove!,
              child: Text(
                moveText!,
                overflow: TextOverflow.ellipsis,
                style: lbItalic.copyWith(
                  fontSize: 14,
                  color: AppConstants.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          if (onDelete != null)
            NetworkAwareTap(
              onTap: onDelete!,
              child: Text(
                "Delete",
                overflow: TextOverflow.ellipsis,
                style: lbItalic.copyWith(
                  fontSize: 14,
                  color: AppConstants.redColor,
                  decoration: TextDecoration.underline,
                  decorationColor: AppConstants.redColor,
                ),
              ),
            ),
        ],
      );
    }
  }
}

/// Empty state skeleton widget
class EmptyBookListSkeleton extends StatelessWidget {
  final String message;

  const EmptyBookListSkeleton({
    Key? key,
    required this.message,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.only(left: 20, right: 20, top: 25),
      height: 50,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          message,
          textAlign: TextAlign.start,
        ),
      ),
    );
  }
}

enum BookListItemType {
  currentReading,
  toBeRead,
}
