import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/text_style.dart';
import '../../../../reusableWidgets/connection_error/network_aware_tap.dart';

class ConfirmationDialogs {
  /// Shows a delete confirmation dialog
  static Future<void> showDeleteConfirmation({
    required BuildContext context,
    required String title,
    required String message,
    required VoidCallback onConfirm,
    String confirmText = "Delete",
    String cancelText = "Cancel",
  }) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () => context.pop(),
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(fontSize: 18),
                    ),
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          message,
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () async {
                          onConfirm();
                          if (context.mounted) {
                            context.pop();
                          }
                        },
                        child: _buildActionButton(
                          context: context,
                          text: confirmText,
                          backgroundColor: AppConstants.textGreenColor,
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () => context.pop(),
                        child: _buildActionButton(
                          context: context,
                          text: cancelText,
                          backgroundColor: AppConstants.backgroundColor,
                          borderColor: AppConstants.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),
              ],
            )
          ],
        );
      },
    );
  }

  /// Shows a move/action confirmation dialog
  static Future<void> showActionConfirmation({
    required BuildContext context,
    required String message,
    required VoidCallback onConfirm,
    required String confirmText,
    String cancelText = "Cancel",
  }) async {
    await showCupertinoModalPopup(
      context: context,
      barrierColor: Colors.white60,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () => context.pop(),
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Column(
                    children: [
                      const SizedBox(height: 30),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Text(
                          message,
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      ),
                      const SizedBox(height: 30),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NetworkAwareTap(
                            onTap: () async {
                              onConfirm();
                              if (context.mounted) {
                                context.pop();
                              }
                            },
                            child: _buildActionButton(
                              context: context,
                              text: confirmText,
                              backgroundColor: AppConstants.textGreenColor,
                            ),
                          ),
                          NetworkAwareTap(
                            onTap: () => context.pop(),
                            child: _buildActionButton(
                              context: context,
                              text: cancelText,
                              backgroundColor: AppConstants.backgroundColor,
                              borderColor: AppConstants.primaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 30),
                    ],
                  ),
                ),
              ],
            )
          ],
        );
      },
    );
  }

  static Widget _buildActionButton({
    required BuildContext context,
    required String text,
    required Color backgroundColor,
    Color? borderColor,
  }) {
    return Container(
      height: 45,
      width: MediaQuery.of(context).size.width / 3,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(49),
        color: backgroundColor,
        border: borderColor != null ? Border.all(color: borderColor) : null,
      ),
      child: Center(
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: lbBold.copyWith(fontSize: 18),
        ),
      ),
    );
  }
}
