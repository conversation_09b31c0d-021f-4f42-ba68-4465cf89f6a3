/// Constants and helper functions for bookcase screens
class BookcaseConstants {
  // Pagination
  static const int defaultLimit = 10;
  static const int defaultOffset = 0;
  static const int paginationIncrement = 10;
  
  // Error messages
  static const String networkError = 'Network error. Please try again.';
  static const String loadBooksError = 'Failed to load books. Please try again.';
  static const String addBookError = 'Failed to add book. Please try again.';
  static const String deleteBookError = 'Failed to delete book. Please try again.';
  static const String updateBookError = 'Failed to update book. Please try again.';
  
  // UI messages
  static const String noCurrentlyReading = "No books in currently reading";
  static const String noToBeRead = "No books in To-Be-Read";
  static const String addNewBook = 'Add new book +';
  static const String markAsComplete = "Mark as Complete";
  static const String updateCompletionDate = "Update Completion Date";
  static const String moveToCurrentlyReading = "Move to currently reading";
  static const String deleteBook = "Delete";
  static const String cancel = "Cancel";
  
  // Dialog titles
  static const String deleteBookTitle = "Delete Book:";
  static const String addNewBookTitle = "Add New Book";
  static const String markAsCompleteTitle = "Mark as Complete";
  static const String updateCompletionDateTitle = "Update Completion Date";
  
  // Validation messages
  static const String selectBookValidation = "*Select book";
  static const String invalidBookValidation = "*Invalid book";
  static const String selectDateValidation = "*Select a date or check 'Unknown'";
  static const String ratingValidation = "*";
  
  // Form labels
  static const String selectCompletionDate = "Select completion date:";
  static const String unknown = "Unknown";
  static const String ratings = "Ratings:";
  static const String reviewLabel = "Up to 2000 Character Review (Optional): ";
  static const String lastRead = "Last Read:";
  
  // Button texts
  static const String add = "Add";
  static const String update = "Update";
  static const String move = "Move";
  static const String ok = "OK";
  
  // Confirmation messages
  static const String deleteConfirmation = "Are you sure you want to delete this book?";
  static const String moveConfirmation = "Are you sure you want to move this book to your currently reading list?";
  
  // Info messages
  static const String markAsCompleteInfo = '(Removes book from "Currently Reading" and Adds to "Books Read")';
  
  // Limits
  static const int reviewMaxLength = 2000;
  static const int reviewMaxLines = 4;
  static const double minRating = 0.5;
  static const int maxRating = 5;
  static const int ratingItemSize = 25;
  
  // UI dimensions
  static const double cardHeight = 117;
  static const double cardMarginTop = 25;
  static const double cardMarginHorizontal = 20;
  static const double cardPadding = 14;
  static const double buttonHeight = 45;
  static const double buttonBorderRadius = 49;
  static const double dialogBorderRadius = 10;
  static const double dialogBorderWidth = 1.5;
  static const double appBarHeight = 80;
  
  // Cache settings
  static const double listCacheExtent = 500;
  
  // Date formats
  static const String monthYearFormat = 'MMM yyyy';
  static const String completeDateFormat = 'dd-MM-yyyy';
}
