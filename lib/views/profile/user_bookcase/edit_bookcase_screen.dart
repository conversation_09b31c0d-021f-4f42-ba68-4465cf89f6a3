import 'dart:developer';

import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/customDialouge_with_message.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/reusableWidgets/paginated_book_typeahead.dart';
import 'package:eljunto/reusableWidgets/profile_appbar.dart';
import 'package:eljunto/reusable_api_function/books_api_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:roundcheckbox/roundcheckbox.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../constants/common_helper.dart';
import '../../../constants/constants.dart';
import '../../../models/profile_model/edit_bookcase/listof_book_model.dart';
import 'constants/bookcase_constants.dart' as constants;
import 'mixins/book_selection_mixin.dart';
import 'models/bookcase_screen_state.dart';
import 'widgets/confirmation_dialogs.dart';

/// Edit Bookcase Screen - Manages completed books with sorting and filtering
class EditBookCaseScreen extends StatefulWidget {
  final String? userName;
  final String? userHandler;
  final int? lengthofTopShelf;
  final bool? userClubInvitation;
  final String? userProfilePicture;

  const EditBookCaseScreen({
    super.key,
    this.userName,
    this.userHandler,
    this.lengthofTopShelf,
    this.userClubInvitation,
    this.userProfilePicture,
  });

  @override
  State<EditBookCaseScreen> createState() => _EditBookCaseScreenState();
}

class _EditBookCaseScreenState extends State<EditBookCaseScreen>
    with BookSelectionMixin {
  // Screen state management
  final BookcaseScreenState _screenState = BookcaseScreenState();

  // Controllers
  final roleController = TextEditingController();
  final reviewController = TextEditingController();
  final editReviewController = TextEditingController();
  final editCompleteDateController = TextEditingController();
  final monthyearController = TextEditingController();
  final unknownController = ValueNotifier<bool>(false);
  final selectTopShelfController = ValueNotifier<bool>(false);
  final _bookCaseScrollController = ScrollController();
  SuggestionsController<Books>? suggestionsController;

  // State variables
  bool monthyearValidation = false;
  bool istopShelfPopUpShow = false;
  bool ratingValidation = false;
  bool isInitialLoading = false;
  bool isPaginationLoading = false;
  bool alreadyExists = false;
  String responseMessage = '';

  // Data
  List<BookCaseModel>? completedBooks = [];
  List<BookCaseModel> currentlyReadingBooks = [];
  int completeBookcount = 0;
  int completeBookLimit = constants.BookcaseConstants.defaultLimit;
  int offset = constants.BookcaseConstants.defaultOffset;

  // Book operation state
  double ratingStar = 0;
  DateTime? selectedDate;
  String? readingCompleteDate;
  int? bookCaseId;

  // Add book popup state
  String? bookName;
  String? bookAuthor;
  int? bookId;
  List<Books> bookList = [];
  final bookController = TextEditingController();

  // Provider reference
  BookCaseController? bookCaseController;

  // Sorting options
  final List<String> _sortingOptions = [
    BookCaseSortingOptions.title,
    BookCaseSortingOptions.completionDate,
    BookCaseSortingOptions.author,
    BookCaseSortingOptions.ratings
  ];

  @override
  void initState() {
    super.initState();
    _initializeScreen();
  }

  void _initializeScreen() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    _bookCaseScrollController.addListener(_onScroll);
    roleController.text = BookCaseSortingOptions.title;
    log("TopShelf Length : ${widget.lengthofTopShelf}");
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    setState(() => isInitialLoading = true);

    try {
      await _initializeUserId();
      await _loadBookCase(false);
    } catch (e) {
      log('Error loading initial data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text(constants.BookcaseConstants.loadBooksError)),
        );
      }
    } finally {
      if (mounted) {
        setState(() => isInitialLoading = false);
      }
    }
  }

  Future<void> _initializeUserId() async {
    _screenState.loggedInUserId = await CommonHelper.getLoggedInUserId();
  }

  void _onScroll() {
    if (_bookCaseScrollController.position.pixels >=
            _bookCaseScrollController.position.maxScrollExtent &&
        !isPaginationLoading &&
        (completedBooks?.length ?? 0) < completeBookcount) {
      _loadBookCase(true);
    }
  }

  Future<void> _loadBookCase(bool isLoadMore) async {
    if (isPaginationLoading) return;

    if (isLoadMore) {
      setState(() => isPaginationLoading = true);
      completeBookLimit += constants.BookcaseConstants.paginationIncrement;
    }

    try {
      final responseMap =
          await Provider.of<BookCaseController>(context, listen: false)
              .allBooksRead(
        _screenState.loggedInUserId ?? 0,
        completeBookLimit,
        offset,
        false,
        false,
        context,
      );

      if (responseMap["statusCode"] == 200) {
        _processBookCaseResponse(responseMap);
      }
    } catch (e) {
      log('Error loading bookcase: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text(constants.BookcaseConstants.loadBooksError)),
        );
      }
    } finally {
      if (mounted) {
        setState(() => isPaginationLoading = false);
      }
    }
  }

  void _processBookCaseResponse(Map<String, dynamic> responseMap) {
    final List<BookCaseModel> bookList = (responseMap["data"] as List)
        .map((item) => BookCaseModel.fromJson(item))
        .toList();

    completeBookcount = responseMap['count'];

    final result = CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
    if (result.isNotEmpty) {
      currentlyReadingBooks = result[1];
      completedBooks = result[2];

      // Apply current sorting
      completedBooks = CommonHelper.sortBookCaseList(
        completedBooks!,
        roleController.text,
      );
    }

    if (mounted) setState(() {});
  }

  Future<void> _sortBookCase(String sortOption) async {
    if (completedBooks != null) {
      setState(() {
        completedBooks =
            CommonHelper.sortBookCaseList(completedBooks!, sortOption);
      });
    }
  }

  @override
  void dispose() {
    _screenState.dispose();
    roleController.dispose();
    reviewController.dispose();
    editReviewController.dispose();
    editCompleteDateController.dispose();
    monthyearController.dispose();
    unknownController.dispose();
    selectTopShelfController.dispose();
    _bookCaseScrollController.dispose();
    bookController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return PreferredSize(
      preferredSize:
          const Size.fromHeight(constants.BookcaseConstants.appBarHeight),
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 1.5,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
        child: Skeletonizer(
          effect: const SoldColorEffect(
            color: AppConstants.skeletonforgroundColor,
            lowerBound: 0.1,
            upperBound: 0.5,
          ),
          containersColor: AppConstants.textGreenColor,
          enabled: isInitialLoading,
          child: ProfileAppBar(
            userName: widget.userName,
            userHandle: widget.userHandler,
            isOpenToClubInvitation: widget.userClubInvitation ?? false,
            userOwnProfile: true,
            userProfilePicture: widget.userProfilePicture,
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppConstants.bgImagePath),
          filterQuality: FilterQuality.high,
          fit: BoxFit.fitWidth,
        ),
      ),
      child: Skeletonizer(
        effect: const SoldColorEffect(
          color: AppConstants.skeletonforgroundColor,
          lowerBound: 0.1,
          upperBound: 0.5,
        ),
        containersColor: AppConstants.skeletonBackgroundColor,
        enabled: isInitialLoading,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 25),
            _buildHeader(),
            const SizedBox(height: 25),
            _buildSortingAndAddSection(),
            _buildBooksList(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: constants.BookcaseConstants.cardMarginHorizontal),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              "All Books Read",
              style: lbRegular.copyWith(fontSize: 20),
            ),
          ),
          Consumer<BookCaseController>(
            builder: (context, value, child) {
              return Text(
                "$completeBookcount-Books",
                overflow: TextOverflow.ellipsis,
                style: lbItalic.copyWith(fontSize: 20),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSortingAndAddSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: constants.BookcaseConstants.cardMarginHorizontal),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildSortingDropdown(),
          _buildAddBookButton(),
        ],
      ),
    );
  }

  Widget _buildSortingDropdown() {
    return SizedBox(
      height: constants.BookcaseConstants.buttonHeight,
      width: MediaQuery.of(context).size.width / 2.3,
      child: DropdownMenu(
        inputDecorationTheme: const InputDecorationTheme(
          alignLabelWithHint: true,
          contentPadding: EdgeInsets.symmetric(
            // vertical: BorderSide.strokeAlignCenter,
            horizontal: 10,
          ),
          filled: true,
          fillColor: AppConstants.backgroundColor,
          focusColor: AppConstants.primaryColor,
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
            borderRadius: BorderRadius.all(
              Radius.circular(10),
            ),
          ),
        ),
        menuStyle: const MenuStyle(
          surfaceTintColor: WidgetStatePropertyAll(Colors.transparent),
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(
              side: BorderSide(
                color: AppConstants.primaryColor,
                width: 1,
              ),
              borderRadius: BorderRadius.all(
                Radius.circular(10),
              ),
            ),
          ),
          padding: WidgetStatePropertyAll(
            EdgeInsets.zero,
          ),
          backgroundColor: WidgetStatePropertyAll(
            AppConstants.backgroundColor,
          ),
        ),
        expandedInsets: EdgeInsets.zero,
        controller: roleController,
        requestFocusOnTap: false,
        textStyle: lbRegular.copyWith(fontSize: 12),
        onSelected: (String? value) {
          if (value != null) {
            _sortBookCase(value);
          }
        },
        dropdownMenuEntries: _sortingOptions.map((role) {
          return DropdownMenuEntry(
            style: TextButton.styleFrom(
              visualDensity: VisualDensity.comfortable,
              side: const BorderSide(
                width: 0.5,
                color: AppConstants.primaryColor,
              ),
              textStyle: lbRegular.copyWith(
                overflow: TextOverflow.ellipsis,
                fontSize: 12,
              ),
            ),
            value: role,
            label: role,
          );
        }).toList(),
        menuHeight: 200,
      ),
    );
  }

  Widget _buildAddBookButton() {
    return NetworkAwareTap(
      onTap: _showAddBookPopup,
      child: Text(
        constants.BookcaseConstants.addNewBook,
        style: lbItalic.copyWith(
          fontSize: 16,
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }

  Widget _buildBooksList() {
    // Show skeleton during initial loading
    if (isInitialLoading) {
      return Expanded(
        child: GridView.builder(
          padding: const EdgeInsets.only(
            top: 25,
            bottom: 25,
            left: 20,
            right: 20,
          ),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 15,
            mainAxisSpacing: 15,
            childAspectRatio: 0.75,
          ),
          itemCount: 6, // Show 6 skeleton items
          itemBuilder: (context, index) => _buildOriginalSkeletonCard(),
        ),
      );
    }

    // Show actual content or empty state
    if (completedBooks?.isNotEmpty ?? false) {
      return Expanded(
        child: GridView.builder(
          controller: _bookCaseScrollController,
          padding: const EdgeInsets.only(
            bottom: 25,
            left: 20,
            right: 20,
          ),
          cacheExtent: constants.BookcaseConstants.listCacheExtent,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 15,
            mainAxisSpacing: 15,
            childAspectRatio: 0.75,
          ),
          itemCount: isPaginationLoading
              ? (completedBooks?.length ?? 0) + 2 // Add 2 loading items
              : (completedBooks?.length ?? 0),
          itemBuilder: (context, index) {
            // Show pagination loader at the end
            if (index >= (completedBooks?.length ?? 0) && isPaginationLoading) {
              return _buildPaginationLoader();
            }

            final book = completedBooks?[index];
            if (book == null) return const SizedBox.shrink();

            return _buildOriginalBookCard(book, index);
          },
        ),
      );
    } else {
      // Show empty state
      return const Expanded(
        child: Padding(
          padding: EdgeInsets.only(top: 25.0),
          child: NoDataWidget(
            message: "No completed books found",
          ),
        ),
      );
    }
  }

  Widget _buildOriginalSkeletonCard() {
    return Container(
      height: constants.BookcaseConstants.cardHeight,
      margin: const EdgeInsets.only(top: 25),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Loading...',
              style: lbBold.copyWith(fontSize: 14),
            ),
            const SizedBox(height: 4),
            Text(
              'Loading...',
              style: lbRegular.copyWith(fontSize: 12),
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Edit',
                  style: lbItalic.copyWith(fontSize: 12),
                ),
                Text(
                  'Delete',
                  style: lbItalic.copyWith(fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaginationLoader() {
    return Container(
      height: constants.BookcaseConstants.cardHeight,
      margin: const EdgeInsets.only(top: 25),
      child: const Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }

  Widget _buildOriginalBookCard(BookCaseModel book, int index) {
    return Container(
      // height: constants.BookcaseConstants.cardHeight,
      margin: const EdgeInsets.only(top: 25),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildOriginalBookInfo(book),
            _buildOriginalBookActions(book, index),
          ],
        ),
      ),
    );
  }

  Widget _buildOriginalBookInfo(BookCaseModel book) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          book.bookName ?? '',
          style: lbBold.copyWith(fontSize: 18),
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          book.bookAuthor ?? '',
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        if (book.ratings != null && book.ratings! > 0) ...[
          RatingBar(
            ignoreGestures: true,
            itemCount: 5,
            itemSize: 22,
            allowHalfRating: true,
            initialRating: book.ratings ?? 0,
            minRating: 0.5,
            unratedColor: Colors.red,
            ratingWidget: RatingWidget(
              full: const Icon(
                Icons.star,
                color: AppConstants.textGreenColor,
              ),
              half: const Icon(
                Icons.star_half,
                color: AppConstants.textGreenColor,
              ),
              empty: const Icon(
                Icons.star_border_outlined,
                color: AppConstants.textGreenColor,
              ),
            ),
            onRatingUpdate: (double value) {},
          ),
        ],
        Row(
          children: [
            if (book.topShelf == true)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppConstants.textGreenColor,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Top Shelf',
                  style: lbBold.copyWith(fontSize: 10, color: Colors.white),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildOriginalBookActions(BookCaseModel book, int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        NetworkAwareTap(
          onTap: () {},
          child: Text(
            'Read Review',
            style: lbItalic.copyWith(
              fontSize: 12,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        const SizedBox(height: 5),
        NetworkAwareTap(
          onTap: () => _showEditBookPopup(book, index),
          child: Text(
            'Edit',
            style: lbItalic.copyWith(
              fontSize: 12,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        const SizedBox(height: 5),
        if (book.readingCompleteDate != null) ...[
          const SizedBox(height: 4),
          Text(
            'Completed: ${DateFormat(constants.BookcaseConstants.monthYearFormat).format(DateTime.fromMillisecondsSinceEpoch(book.readingCompleteDate!))}',
            style: lbRegular.copyWith(fontSize: 12),
          ),
        ] else ...[
          const SizedBox(height: 4),
          Text(
            'Completed: Unknown',
            style: lbRegular.copyWith(fontSize: 12),
          ),
        ],
      ],
    );
  }

  void _showAddBookPopup() {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return _buildOriginalAddBookPopup(setState);
          },
        );
      },
    );
  }

  void _showDeleteConfirmation(BookCaseModel book) {
    ConfirmationDialogs.showDeleteConfirmation(
      context: context,
      title: constants.BookcaseConstants.deleteBookTitle,
      message: constants.BookcaseConstants.deleteConfirmation,
      onConfirm: () => _confirmDelete(book.bookId),
    );
  }

  void _showEditBookPopup(BookCaseModel book, int index) {
    // Reset form state
    reviewController.text = book.review ?? '';
    ratingStar = book.ratings ?? 0.5;
    selectTopShelfController.value = book.topShelf ?? false;

    if (book.readingCompleteDate != null) {
      selectedDate =
          DateTime.fromMillisecondsSinceEpoch(book.readingCompleteDate!);
      monthyearController.text =
          DateFormat(constants.BookcaseConstants.monthYearFormat)
              .format(selectedDate!);
      unknownController.value = false;
    } else {
      selectedDate = null;
      monthyearController.clear();
      unknownController.value = true;
    }

    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return _buildEditBookDialog(book, setState);
          },
        );
      },
    );
  }

  Future<bool> _confirmAddBook(bool isMove) async {
    final addBook = BookCaseModel(
      userId: _screenState.loggedInUserId,
      bookId: bookId,
      bookAuthor: bookAuthor,
      bookName: bookName,
      is_currently_reading: false,
      ratings: ratingStar,
      review: reviewController.text,
      topShelf: selectTopShelfController.value,
      reading_complete_date_String: selectedDate != null
          ? DateFormat(constants.BookcaseConstants.completeDateFormat)
              .format(selectedDate!)
          : null,
      toBeRead: false,
    );

    try {
      final result =
          await Provider.of<BookCaseController>(context, listen: false)
              .addBookInBookCase(addBook, context);

      final tempMessage = bookCaseController?.addBookErrorMessage ?? '';
      if (result == 'exist') {
        responseMessage = tempMessage;
        alreadyExists = true;
        return true;
      } else {
        alreadyExists = false;
        return false;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error adding book: $e')),
        );
      }
      return false;
    }
  }

  Future<void> _confirmDelete(int? bookId) async {
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .deleteBook(bookId, context);
      await _loadBookCase(false);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting book: $e')),
        );
      }
    }
  }

  Widget _buildEditBookDialog(BookCaseModel book, StateSetter setState) {
    return _buildUnifiedBookPopup(
      setState: setState,
      isEditMode: true,
      title: "Edit Book",
      book: book,
    );
  }

  bool _validateEditForm() {
    bool isValid = true;

    if (monthyearController.text.isEmpty && unknownController.value == false) {
      monthyearValidation = true;
      isValid = false;
    }

    if (ratingStar == 0) {
      ratingValidation = true;
      isValid = false;
    }

    return isValid;
  }

  Future<void> _confirmUpdateBook(int? bookCaseId, int? bookIds) async {
    String? date;
    final book = completedBooks?.firstWhere((book) => book.bookId == bookIds);
    int reRead = book?.reRead ?? 0;

    if (selectedDate != null) {
      date = DateFormat(constants.BookcaseConstants.completeDateFormat)
          .format(selectedDate!);
    } else {
      date = '';
    }

    final obj = BookCaseModel(
      bookId: bookIds,
      userId: _screenState.loggedInUserId,
      ratings: ratingStar,
      bookCaseId: bookCaseId,
      review: reviewController.text,
      topShelf: selectTopShelfController.value,
      reading_complete_date_String: date,
      reRead: reRead,
    );

    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .updateBookCase(obj, context);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating book: $e')),
        );
      }
    }
  }

  Widget _buildOriginalAddBookPopup(StateSetter setState) {
    return _buildUnifiedBookPopup(
      setState: setState,
      isEditMode: false,
      title: constants.BookcaseConstants.addNewBookTitle,
      book: null,
    );
  }

  /// Unified popup structure for both add and edit book modes
  Widget _buildUnifiedBookPopup({
    required StateSetter setState,
    required bool isEditMode,
    required String title,
    BookCaseModel? book,
  }) {
    return AlertDialog(
      actionsPadding: const EdgeInsets.only(right: 10),
      insetPadding: const EdgeInsets.all(25),
      contentPadding: EdgeInsets.zero,
      backgroundColor: AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
            constants.BookcaseConstants.dialogBorderRadius),
        side: const BorderSide(
          color: AppConstants.popUpBorderColor,
          width: constants.BookcaseConstants.dialogBorderWidth,
        ),
      ),
      actions: [
        Column(
          children: [
            _buildSharedCloseButton(),
            _buildSharedTitle(title),
            _buildSharedContent(setState, isEditMode, book),
            _buildSharedActions(setState, isEditMode, book),
            const SizedBox(height: 30),
          ],
        )
      ],
    );
  }

  /// Shared close button for both popups
  Widget _buildSharedCloseButton() {
    return NetworkAwareTap(
      onTap: () => context.pop(),
      child: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(top: 10),
        child: Image.asset(
          AppConstants.closePopupImagePath,
          height: 30,
          width: 30,
        ),
      ),
    );
  }

  /// Shared title section for both popups
  Widget _buildSharedTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 22.0, right: 12),
      child: Column(
        children: [
          SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: lbRegular.copyWith(fontSize: 18),
            ),
          ),
          const SizedBox(height: 25),
        ],
      ),
    );
  }

  /// Shared content section for both popups
  Widget _buildSharedContent(
      StateSetter setState, bool isEditMode, BookCaseModel? book) {
    return Padding(
      padding: const EdgeInsets.only(left: 22.0, right: 12),
      child: Column(
        children: [
          // Book selection (add mode) or book info display (edit mode)
          if (isEditMode)
            _buildBookInfoDisplay(book!)
          else
            _buildBookTypeahead(setState),
          const SizedBox(height: 25),
          if (isEditMode) ...[
            Text(
              'Completed: ',
              style: lbRegular.copyWith(fontSize: 12),
            ),
            const SizedBox(height: 15),
          ],
          // Shared form sections
          _buildSharedDateSelection(setState),
          const SizedBox(height: 15),
          _buildSharedRatingSection(setState),
          const SizedBox(height: 15),
          // Read review section (only for edit mode)
          if (isEditMode) ...[
            _buildReadReviewSection(book!),
            const SizedBox(height: 15),
          ],
          _buildSharedReviewSection(),
          const SizedBox(height: 25),
        ],
      ),
    );
  }

  /// Book info display for edit mode
  Widget _buildBookInfoDisplay(BookCaseModel book) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Book: ${book.bookName}, ${book.bookAuthor}",
          textAlign: TextAlign.center,
          style: lbRegular.copyWith(fontSize: 12),
        ),
      ],
    );
  }

  /// Shared date selection component for both popups
  Widget _buildSharedDateSelection(StateSetter setState) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              constants.BookcaseConstants.selectCompletionDate,
              style: lbRegular.copyWith(fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width / 2.8,
              child: TextFormField(
                controller: monthyearController,
                style: lbRegular.copyWith(fontSize: 12),
                decoration: InputDecoration(
                  suffixIcon:
                      const Icon(Icons.calendar_month_outlined, size: 20),
                  contentPadding: const EdgeInsets.all(10),
                  fillColor: const Color.fromRGBO(255, 255, 255, 1),
                  filled: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: const BorderSide(
                      color: AppConstants.primaryColor,
                      width: 1.5,
                    ),
                  ),
                ),
                onChanged: (value) =>
                    setState(() => monthyearValidation = false),
                onTap: () async {
                  selectedDate = await CommonHelper.getMonthYear(context);
                  setState(() {
                    if (selectedDate != null) {
                      monthyearValidation = false;
                      unknownController.value = false;
                      monthyearController.text = DateFormat(
                              constants.BookcaseConstants.monthYearFormat)
                          .format(selectedDate!);
                      readingCompleteDate = monthyearController.text;
                    } else {
                      monthyearController.clear();
                      readingCompleteDate = '';
                    }
                  });
                },
                readOnly: true,
              ),
            ),
            const SizedBox(width: 25),
            _buildSharedUnknownCheckbox(setState),
          ],
        ),
        if (monthyearValidation)
          Positioned(
            top: 55,
            left: 0,
            right: 0,
            child: Text(
              constants.BookcaseConstants.selectDateValidation,
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            ),
          ),
      ],
    );
  }

  /// Shared unknown checkbox component for both popups
  Widget _buildSharedUnknownCheckbox(StateSetter setState) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          constants.BookcaseConstants.unknown,
          style: lbRegular.copyWith(fontSize: 12),
        ),
        const SizedBox(width: 10),
        RoundCheckBox(
          isChecked: unknownController.value,
          border: Border.all(color: Colors.transparent),
          onTap: (value) {
            setState(() {
              unknownController.value = value!;
              if (unknownController.value) {
                monthyearValidation = false;
                monthyearController.clear();
              }
              monthyearValidation = false;
            });
          },
          checkedWidget: const Icon(
            Icons.check_circle_outline_rounded,
            color: AppConstants.primaryColor,
          ),
          checkedColor: AppConstants.backgroundColor,
          uncheckedColor: AppConstants.backgroundColor,
          uncheckedWidget: const Icon(
            Icons.circle_outlined,
            color: AppConstants.primaryColor,
          ),
        ),
      ],
    );
  }

  /// Shared rating section component for both popups
  Widget _buildSharedRatingSection(StateSetter setState) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          constants.BookcaseConstants.ratings,
          style: lbRegular.copyWith(fontSize: 12),
        ),
        const SizedBox(width: 10),
        RatingBar(
          glow: false,
          itemCount: constants.BookcaseConstants.maxRating,
          itemSize: constants.BookcaseConstants.ratingItemSize.toDouble(),
          allowHalfRating: true,
          initialRating: ratingStar,
          minRating: constants.BookcaseConstants.minRating,
          unratedColor: Colors.red,
          ratingWidget: RatingWidget(
            full: const Icon(
              Icons.star,
              color: AppConstants.textGreenColor,
            ),
            half: const Icon(
              Icons.star_half,
              color: AppConstants.textGreenColor,
            ),
            empty: const Icon(
              Icons.star_border_outlined,
              color: AppConstants.textGreenColor,
            ),
          ),
          onRatingUpdate: (double value) {
            setState(() {
              ratingValidation = false;
              ratingStar = value;
            });
          },
        ),
        if (ratingValidation)
          Text(
            constants.BookcaseConstants.ratingValidation,
            style: lbRegular.copyWith(
              fontSize: 14,
              color: AppConstants.redColor,
            ),
          ),
      ],
    );
  }

  /// Shared review section component for both popups
  Widget _buildSharedReviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              constants.BookcaseConstants.reviewLabel,
              style: lbRegular.copyWith(fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 10),
        TextFormField(
          controller: reviewController,
          textCapitalization: TextCapitalization.sentences,
          maxLines: constants.BookcaseConstants.reviewMaxLines,
          maxLength: constants.BookcaseConstants.reviewMaxLength,
          style: lbRegular.copyWith(fontSize: 12),
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.all(10),
            fillColor: const Color.fromRGBO(255, 255, 255, 1),
            filled: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5),
              borderSide: const BorderSide(
                color: AppConstants.primaryColor,
                width: 1.5,
              ),
            ),
            counterStyle: lbRegular.copyWith(fontSize: 14),
          ),
        ),
      ],
    );
  }

  Widget _buildSharedActions(
      StateSetter setState, bool isEditMode, BookCaseModel? book) {
    return Padding(
      padding: const EdgeInsets.only(left: 22.0, right: 12),
      child: Column(
        children: [
          // Primary action row (Add/Update and Cancel)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Primary action button (Add Book / Update)
              NetworkAwareTap(
                onTap: () async {
                  if (isEditMode) {
                    await _handleUpdateBook(setState, book!);
                  } else {
                    await _handleAddBook(setState);
                  }
                },
                child: Container(
                  height: constants.BookcaseConstants.buttonHeight,
                  width: MediaQuery.of(context).size.width / 3,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                        constants.BookcaseConstants.buttonBorderRadius),
                    color: AppConstants.textGreenColor,
                  ),
                  child: Center(
                    child: Text(
                      isEditMode ? "Save" : "Add Book",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(fontSize: 18),
                    ),
                  ),
                ),
              ),
              // Cancel button
              NetworkAwareTap(
                onTap: () => context.pop(),
                child: Container(
                  height: constants.BookcaseConstants.buttonHeight,
                  width: MediaQuery.of(context).size.width / 3,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                        constants.BookcaseConstants.buttonBorderRadius),
                    color: AppConstants.backgroundColor,
                    border: Border.all(color: AppConstants.primaryColor),
                  ),
                  child: Center(
                    child: Text(
                      constants.BookcaseConstants.cancel,
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(fontSize: 18),
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Delete button (only for edit mode)
          if (isEditMode) ...[
            const SizedBox(height: 25),
            NetworkAwareTap(
              onTap: () => _showDeleteConfirmation(book!),
              child: Container(
                height: constants.BookcaseConstants.buttonHeight,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                      constants.BookcaseConstants.buttonBorderRadius),
                  color: AppConstants.textGreenColor,
                ),
                child: Center(
                  child: Text(
                    constants.BookcaseConstants.deleteBook,
                    textAlign: TextAlign.center,
                    style: lbBold.copyWith(fontSize: 18),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Handle add book action
  Future<void> _handleAddBook(StateSetter setState) async {
    if (_validateAddBookForm()) {
      final success = await _confirmAddBook(false);
      if (!success) {
        if (mounted) {
          context.pop();
          await _loadBookCase(false);
        }
      } else {
        // Show error dialog if book already exists
        if (alreadyExists && mounted) {
          showDialog(
            context: context,
            builder: (context) => CustomDialog(
              title: "Book Already Exists",
              message: responseMessage,
            ),
          );
        }
      }
    } else {
      setState(() {});
    }
  }

  /// Handle update book action
  Future<void> _handleUpdateBook(
      StateSetter setState, BookCaseModel book) async {
    if (_validateEditForm()) {
      await _confirmUpdateBook(book.bookCaseId, book.bookId);
      if (mounted) {
        context.pop();
        await _loadBookCase(false);
      }
    } else {
      setState(() {});
    }
  }

  Widget _buildBookTypeahead(StateSetter setState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 10),
        PaginatedBookTypeahead(
          suggestionsController: suggestionsController,
          controller: bookController,
          fetchBooksCallback: (query, offset, limit) =>
              BooksApiFunctions.fetchBooks(
            query,
            offset,
            limit,
            context,
            _screenState.loggedInUserId,
          ),
          onSelected: (book) => setState(
            () {
              bookController.text = book.bookName.toString();
              bookId = book.bookId;
              bookName = book.bookName ?? '';
              bookAuthor = book.bookAuthor ?? '';
            },
          ),
        ),
      ],
    );
  }

  bool _validateAddBookForm() {
    bool isValid = true;

    if (bookController.text.isEmpty) {
      // Show book selection error
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please select a book")),
      );
      isValid = false;
    }

    if (monthyearController.text.isEmpty && unknownController.value == false) {
      monthyearValidation = true;
      isValid = false;
    }

    if (ratingStar == 0) {
      ratingValidation = true;
      isValid = false;
    }

    return isValid;
  }
}
