import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';

import '../../../../models/book_case_model.dart';
import '../../../../models/profile_model/edit_bookcase/listof_book_model.dart';

/// Shared state management for bookcase screens
class BookcaseScreenState {
  // Book selection state
  int? bookId;
  String? bookName;
  String? bookAuthor;
  bool isBookIdNotEmpty = false;
  bool alreadyExists = false;
  String responseMessage = '';

  // Pagination state
  int limit = 10;
  int offset = 0;
  int count = 0;
  bool isLoading = false;

  // Controllers
  SuggestionsController<Books>? suggestionsController;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // User state
  int? loggedInUserId;

  BookcaseScreenState() {
    suggestionsController = SuggestionsController<Books>();
  }

  void dispose() {
    suggestionsController?.dispose();
  }

  void resetBookSelection() {
    bookId = 0;
    bookName = null;
    bookAuthor = null;
    isBookIdNotEmpty = false;
    alreadyExists = false;
    responseMessage = '';
  }

  void resetPagination() {
    limit = 10;
    offset = 0;
    count = 0;
    isLoading = false;
  }

  void updateBookSelection({
    required int? id,
    required String? name,
    required String? author,
    required bool idNotEmpty,
  }) {
    bookId = id;
    bookName = name;
    bookAuthor = author;
    isBookIdNotEmpty = idNotEmpty;
  }

  void updatePagination({
    int? newLimit,
    int? newOffset,
    int? newCount,
    bool? loading,
  }) {
    if (newLimit != null) limit = newLimit;
    if (newOffset != null) offset = newOffset;
    if (newCount != null) count = newCount;
    if (loading != null) isLoading = loading;
  }
}

/// Constants for bookcase screens
class BookcaseConstants {
  static const int defaultLimit = 10;
  static const int defaultOffset = 0;
  static const int paginationIncrement = 10;
  
  // Error messages
  static const String networkError = 'Network error. Please try again.';
  static const String loadBooksError = 'Failed to load books. Please try again.';
  static const String addBookError = 'Failed to add book. Please try again.';
  static const String deleteBookError = 'Failed to delete book. Please try again.';
  static const String updateBookError = 'Failed to update book. Please try again.';
  
  // UI messages
  static const String noCurrentlyReading = "No books in currently reading";
  static const String noToBeRead = "No books in To-Be-Read";
  static const String addNewBook = 'Add new book +';
  static const String markAsComplete = "Mark as Complete";
  static const String updateCompletionDate = "Update Completion Date";
  static const String moveToCurrentlyReading = "Move to currently reading";
  static const String deleteBook = "Delete";
  static const String cancel = "Cancel";
}

/// Helper class for common bookcase operations
class BookcaseHelper {
  /// Validates if more items can be loaded for pagination
  static bool canLoadMore({
    required int currentLength,
    required int totalCount,
    required bool isLoading,
  }) {
    return !isLoading && currentLength < totalCount;
  }

  /// Calculates if scroll position is at bottom
  static bool isAtBottom(ScrollController controller) {
    return controller.position.pixels == controller.position.maxScrollExtent;
  }

  /// Creates a safe error message for user display
  static String getSafeErrorMessage(dynamic error) {
    if (error == null) return BookcaseConstants.networkError;
    
    final errorString = error.toString();
    if (errorString.toLowerCase().contains('network') ||
        errorString.toLowerCase().contains('connection')) {
      return BookcaseConstants.networkError;
    }
    
    return errorString.length > 100 
        ? BookcaseConstants.networkError 
        : errorString;
  }

  /// Determines edit link text based on book state
  static String getEditLinkText(BookCaseModel? book) {
    if (book?.readingCompleteDate != null) {
      return "";
    } else if (book?.reRead != null && (book?.reRead ?? 0) >= 1) {
      return BookcaseConstants.updateCompletionDate;
    } else {
      return BookcaseConstants.markAsComplete;
    }
  }
}
