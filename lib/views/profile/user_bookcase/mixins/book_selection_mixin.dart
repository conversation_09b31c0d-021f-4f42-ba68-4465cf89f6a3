import 'package:flutter/material.dart';
import '../../../../models/profile_model/edit_bookcase/listof_book_model.dart';

/// Mixin to handle common book selection logic across bookcase screens
mixin BookSelectionMixin<T extends StatefulWidget> on State<T> {
  /// Handles book selection from typeahead suggestions
  void handleBookSelection(
    Books book, {
    required TextEditingController controller,
    required Function(int?) setBookId,
    required Function(String?) setBookName,
    required Function(String?) setBookAuthor,
    required Function(bool) setBookIdNotEmpty,
  }) {
    setState(() {
      controller.text = book.bookName.toString();
      setBookId(book.bookId);
      setBookName(book.bookName ?? '');
      setBookAuthor(book.bookAuthor ?? '');
      setBookIdNotEmpty(true);
    });
  }

  /// Clears book selection state
  void clearBookSelection({
    required TextEditingController controller,
    required Function(int?) setBookId,
    required Function(String?) setBookName,
    required Function(String?) setBookAuthor,
    required Function(bool) setBookIdNotEmpty,
  }) {
    setState(() {
      controller.clear();
      setBookId(0);
      setBookName(null);
      setBookAuthor(null);
      setBookIdNotEmpty(false);
    });
  }

  /// Validates book selection
  bool validateBookSelection({
    required TextEditingController controller,
    required int? bookId,
    required bool isBookIdNotEmpty,
    required Function(bool) setTypeAheadEmpty,
    required Function(bool) setBookIdNotEmpty,
  }) {
    if (controller.text.isEmpty) {
      setState(() {
        setTypeAheadEmpty(true);
      });
      return false;
    }

    if (!isBookIdNotEmpty || bookId == 0) {
      setState(() {
        setBookIdNotEmpty(true);
      });
      return false;
    }

    return true;
  }
}
