import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../constants/common_helper.dart';
import '../../../constants/constants.dart';
import '../../../constants/text_style.dart';
import '../../../controller/book_case_controller.dart';
import '../../../controller/profile_controller.dart';
import '../../../models/book_case_model.dart';
import '../../../reusableWidgets/connection_error/network_aware_tap.dart';
import '../../../reusableWidgets/no_data_widget.dart';
import '../../../reusableWidgets/previous_screen_appbar.dart';
// Import shared components
import 'mixins/book_selection_mixin.dart';
import 'models/bookcase_screen_state.dart';
import 'widgets/add_book_popup.dart';
import 'widgets/book_list_item.dart';
import 'widgets/confirmation_dialogs.dart';

class ToBeReadScreen extends StatefulWidget {
  const ToBeReadScreen({super.key});

  @override
  State<ToBeReadScreen> createState() => _ToBeReadScreenState();
}

class _ToBeReadScreenState extends State<ToBeReadScreen>
    with BookSelectionMixin<ToBeReadScreen> {
  // Core state
  late BookcaseScreenState _screenState;
  List<BookCaseModel>? toBeReadList = [];
  bool toBeReadLoading = false;
  int toBeReadLimit = 10;
  int toBeReadcount = 0;

  // Controllers
  final ScrollController _scrollController = ScrollController();

  // Provider controllers
  ProfileController? profileController;
  BookCaseController? bookCaseController;

  @override
  void initState() {
    super.initState();
    _screenState = BookcaseScreenState();

    // Use WidgetsBinding to ensure context is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeControllers();
      _initializeUserId();
    });

    _scrollController.addListener(_onScrollToBeRead);
  }

  void _initializeControllers() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    profileController = Provider.of<ProfileController>(context, listen: false);
  }

  @override
  void dispose() {
    _screenState.dispose();
    _scrollController.removeListener(_onScrollToBeRead);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeUserId() async {
    _screenState.loggedInUserId = await CommonHelper.getLoggedInUserId();
    await getToBeReadBook(false);
  }

  void _onScrollToBeRead() {
    if (BookcaseHelper.isAtBottom(_scrollController) &&
        BookcaseHelper.canLoadMore(
          currentLength: toBeReadList?.length ?? 0,
          totalCount: toBeReadcount,
          isLoading: toBeReadLoading,
        )) {
      CommonHelper.networkClose(getToBeReadBook(true), context);
    }
  }

  Future<void> getToBeReadBook(bool isMore) async {
    if (!BookcaseHelper.canLoadMore(
          currentLength: toBeReadList?.length ?? 0,
          totalCount: toBeReadcount,
          isLoading: toBeReadLoading,
        ) &&
        isMore) {
      return;
    }

    try {
      toBeReadLoading = true;
      if (mounted) setState(() {});

      if (isMore) {
        toBeReadLimit += BookcaseConstants.paginationIncrement;
      }

      final responseMap =
          await Provider.of<BookCaseController>(context, listen: false)
              .getToBeReadBook(
                  _screenState.loggedInUserId ?? 0, toBeReadLimit, 0, context);

      if (!mounted) return;

      if (responseMap["statusCode"] == 200) {
        toBeReadcount = responseMap['count'] ?? 0;

        if (responseMap["data"] != null) {
          final bookCaseList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
          toBeReadList = bookCaseList;

          if (mounted) {
            setState(() {});
            bookCaseController?.notifyListeners();
          }
        } else {
          toBeReadList?.clear();
          toBeReadList = [];
        }
      } else if (responseMap['statusCode'] == 404) {
        toBeReadList?.clear();
        toBeReadList = [];
        toBeReadcount = 0;
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Failed to load books: ${responseMap["message"] ?? "Unknown error"}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
    } finally {
      toBeReadLoading = false;
      if (mounted) setState(() {});
    }
  }

  Future<bool> confirmAddBook(bool isMove) async {
    try {
      if (isMove) {
        final updatedBook = BookCaseModel(
          bookCaseId: _screenState.bookId,
          userId: _screenState.loggedInUserId,
          bookId: _screenState.bookId,
          is_currently_reading: true,
          toBeRead: false,
          reRead: 0,
        );

        await Provider.of<BookCaseController>(context, listen: false)
            .updateBookCase(updatedBook, context);
        return false;
      } else {
        final addBook = BookCaseModel(
          bookId: _screenState.bookId,
          toBeRead: true,
          bookAuthor: _screenState.bookAuthor,
          bookName: _screenState.bookName,
        );

        final result =
            await Provider.of<BookCaseController>(context, listen: false)
                .addBookInBookCase(addBook, context);

        if (result == 'exist') {
          _screenState.alreadyExists = true;
          _screenState.responseMessage =
              bookCaseController?.addBookErrorMessage ?? "";
          return true;
        } else {
          _screenState.alreadyExists = false;
          return false;
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
      return false;
    }
  }

  Future<void> deleteBook(int? bookId) async {
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .deleteBook(bookId, context);
      await getToBeReadBook(false);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(BookcaseHelper.getSafeErrorMessage(e))),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: "Edit To-Be-Read",
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppConstants.bgImagePath),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 25),
            _buildAddBookButton(),
            _buildBooksList(),
          ],
        ),
      ),
    );
  }

  Widget _buildAddBookButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          NetworkAwareTap(
            onTap: () => _showAddBookPopup(),
            child: Text(
              BookcaseConstants.addNewBook,
              style: lbItalic.copyWith(
                fontSize: 16,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBooksList() {
    if (toBeReadList?.isNotEmpty ?? false) {
      return Expanded(
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.only(bottom: 25),
          cacheExtent: 500, // Preload items for smoother scrolling
          itemCount: toBeReadList?.length ?? 0,
          itemBuilder: (context, index) {
            final book = toBeReadList?[index];
            if (book == null) return const SizedBox.shrink();

            return BookListItem(
              book: book,
              type: BookListItemType.toBeRead,
              moveText: BookcaseConstants.moveToCurrentlyReading,
              onMove: () => _showMoveConfirmation(index, book),
              onDelete: () => _showDeleteConfirmation(index, book),
            );
          },
        ),
      );
    } else {
      return Skeleton.replace(
        replacement: const EmptyBookListSkeleton(
          message: BookcaseConstants.noToBeRead,
        ),
        child: const Padding(
          padding: EdgeInsets.only(top: 25.0),
          child: NoDataWidget(
            message: BookcaseConstants.noToBeRead,
          ),
        ),
      );
    }
  }

  void _showAddBookPopup() {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AddBookPopup(
              title: "Add New Book",
              isCurrentlyReading: false,
              loggedInUserId: _screenState.loggedInUserId,
              updateUI: () => setState(() {}),
              onAddBook: confirmAddBook,
              onBookAdded: () => getToBeReadBook(false),
            );
          },
        );
      },
    );
  }

  void _showDeleteConfirmation(int index, BookCaseModel book) {
    ConfirmationDialogs.showDeleteConfirmation(
      context: context,
      title: "Delete Book:",
      message: "Are you sure you want to delete this book?",
      onConfirm: () => deleteBook(book.bookId),
    );
  }

  void _showMoveConfirmation(int index, BookCaseModel book) {
    ConfirmationDialogs.showActionConfirmation(
      context: context,
      message:
          "Are you sure you want to move this book to your currently reading list?",
      confirmText: "Move",
      onConfirm: () async {
        _screenState.bookId = book.bookId;
        await confirmAddBook(true);
        await getToBeReadBook(false);
      },
    );
  }
}
