import 'dart:developer';

import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/paginated_book_typeahead.dart';
import 'package:eljunto/reusable_api_function/books_api_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/common_helper.dart';
import '../../constants/constants.dart';
import '../../constants/text_style.dart';
import '../../controller/book_case_controller.dart';
import '../../controller/profile_controller.dart';
import '../../models/book_case_model.dart';
import '../../models/profile_model/edit_bookcase/listof_book_model.dart';
import '../../reusableWidgets/customDialouge_with_message.dart';
import '../../reusableWidgets/no_data_widget.dart';
import '../../reusableWidgets/previous_screen_appbar.dart';
import '../../reusableWidgets/question_feedback_dialog.dart';

class ToBeReadScreen extends StatefulWidget {
  const ToBeReadScreen({super.key});

  @override
  State<ToBeReadScreen> createState() => _ToBeReadScreenState();
}

class _ToBeReadScreenState extends State<ToBeReadScreen> {
  List<BookCaseModel> completedBooks = [];
  List<BookCaseModel>? toBeReadList = [];
  List<Books>? bookList = [];
  int? bookId;
  String? bookName;
  String? bookAuthor;
  int? bookCaseId;
  String responseMessage = '';
  String searchValue = '';
  bool alreadyExists = false;
  int? loggedinUserId;
  int bookListCount = 0;
  int bookListLimit = 30;
  int offSet = 0;
  int toBeReadcount = 0;
  int toBeReadLimit = 10;
  bool toBeReadLoading = false;
  bool isBookLoading = false;
  bool isBookIdNotEmpty = false;
  TextEditingController bookSearchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  ProfileController? profileController;
  BookCaseController? bookCaseController;
  SuggestionsController<Books>? suggestionsController;

  @override
  void initState() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    profileController = Provider.of<ProfileController>(context, listen: false);
    _scrollController.addListener(_onScrollToBeRead);
    _initializeUserId();
    super.initState();
  }

  @override
  void dispose() {
    suggestionsController?.dispose();
    _scrollController.dispose();
    bookSearchController.dispose();
    super.dispose();
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();
    await getToBeReadBook(false);
  }

  void _onScrollToBeRead() {
    if (_scrollController.position.pixels ==
            _scrollController.position.maxScrollExtent &&
        !toBeReadLoading &&
        (toBeReadList?.length ?? 0) < toBeReadcount) {
      CommonHelper.networkClose(getToBeReadBook(true), context);
      // getToBeReadBook(true);
    }
  }

  Future<void> getToBeReadBook(bool isMore) async {
    if ((toBeReadList?.length ?? 0) <= toBeReadcount || !isMore) {
      toBeReadLoading = true;

      if (isMore) {
        toBeReadLimit += 10; // Increment the limit by 10 for the next load
      }
    }
    log("Current Read Limit : $toBeReadLimit");
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .getToBeReadBook(loggedinUserId ?? 0, toBeReadLimit, offSet, context)
          .then((responseMap) async {
        log("Response Map : ${responseMap['statusCode']}");
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookCaseList = [];
          if (responseMap["count"] != null) {
            toBeReadcount = responseMap['count'];
          } else {
            toBeReadcount = 0;
          }
          log("ToBeRead Count : $toBeReadcount");

          if (responseMap["data"] != null) {
            bookCaseList = (responseMap["data"] as List)
                .map((item) => BookCaseModel.fromJson(item))
                .toList();
            toBeReadList = bookCaseList;
            setState(() {});
            bookCaseController?.notifyListeners();
          } else {
            toBeReadList?.clear();
            toBeReadList = [];
          }
          log("BookCase List : ${bookCaseList.length}");
        } else if (responseMap['statusCode'] == 404) {
          log("ToBeRead Read : ${toBeReadList?.length}");

          toBeReadcount = 0;
        }
      }).whenComplete(() {
        toBeReadLoading = false;
      });
      log("ToBeRead Read : ${toBeReadList?.length}");
    } catch (e) {
      log(e.toString());
    }
  }

  Future<bool> confirmAddBook(bool isMove) async {
    if (isMove) {
      BookCaseModel updatedBook = BookCaseModel(
        bookCaseId: bookCaseId,
        userId: loggedinUserId,
        bookId: bookId,
        // bookName: bookToReRead.bookName,
        // bookAuthor: bookToReRead.bookAuthor,
        is_currently_reading: true,
        toBeRead: false,
        // readingCompleteDate: bookToReRead.readingCompleteDate,
        // ratings: bookToReRead.ratings,
        // topShelf: bookToReRead.topShelf,
        // review: bookToReRead.review,
        reRead: 0,
      );

      await Provider.of<BookCaseController>(context, listen: false)
          .updateBookCase(updatedBook, context)
          .then((value) async {
        return value;
      });
    } else {
      BookCaseModel addBook = BookCaseModel(
        // userId: loggedinUserId,
        bookId: bookId,
        toBeRead: true,
        bookAuthor: bookAuthor,
        bookName: bookName,
      );
      await Provider.of<BookCaseController>(context, listen: false)
          .addBookInBookCase(addBook, context)
          .then((value) {
        if (value == 'exist') {
          alreadyExists = true;
          responseMessage = bookCaseController?.addBookErrorMessage ?? "";
          // responseMessage = 'This book is already present in your bookcase.';
        } else {
          alreadyExists = false;
        }
      });
      // }
    }
    return alreadyExists;
  }

  Future<void> deleteBook() async {
    await Provider.of<BookCaseController>(context, listen: false)
        .deleteBook(bookId, context)
        .then((value) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: "Edit To-Be-Read",
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Column(
          children: [
            const SizedBox(
              height: 25,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  NetworkAwareTap(
                    onTap: () {
                      showAddBookPopUp(() {
                        setState(() {});
                      });
                      // showAddBookPopUp().then((value) => setState(() {}));
                    },
                    child: Text(
                      'Add new book +',
                      style: lbItalic.copyWith(
                        fontSize: 16,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            toBeReadList?.isNotEmpty ?? false
                ? Expanded(
                    child: Consumer<BookCaseController>(
                        builder: (context, bookCaseController, child) {
                      return ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.only(bottom: 25),
                        itemCount: toBeReadList?.length ?? 0,
                        itemBuilder: (context, index) {
                          return Container(
                            height: 117,
                            width: MediaQuery.of(context).size.width,
                            padding: const EdgeInsets.all(14),
                            margin: const EdgeInsets.only(
                              top: 25,
                              left: 20,
                              right: 20,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: AppConstants.primaryColor,
                                width: 1.5,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  toBeReadList?[index].bookName ?? '',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Text(
                                  toBeReadList?[index].bookAuthor ?? '',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbRegular.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    NetworkAwareTap(
                                      onTap: () async {
                                        // responseMessage =
                                        //     "Are you sure you want to move this book to currently reading?";
                                        bookId =
                                            toBeReadList?[index].bookId ?? 0;
                                        bookCaseId =
                                            toBeReadList?[index].bookCaseId;
                                        await moveToCurrentReadingPopUp(
                                            index, true);
                                      },
                                      child: Text(
                                        "Move to currently reading",
                                        overflow: TextOverflow.ellipsis,
                                        style: lbItalic.copyWith(
                                            fontSize: 14,
                                            color: AppConstants.primaryColor,
                                            decoration:
                                                TextDecoration.underline),
                                      ),
                                    ),
                                    NetworkAwareTap(
                                      onTap: () async {
                                        await moveToCurrentReadingPopUp(
                                            index, false);
                                      },
                                      child: Text(
                                        "Delete",
                                        overflow: TextOverflow.ellipsis,
                                        style: lbItalic.copyWith(
                                          fontSize: 14,
                                          color: AppConstants.redColor,
                                          decoration: TextDecoration.underline,
                                          decorationColor:
                                              AppConstants.redColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    }),
                  )
                : Skeleton.replace(
                    replacement: Container(
                      padding: const EdgeInsets.only(left: 20),
                      margin: const EdgeInsets.only(top: 25),
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                        color: AppConstants.skeletonBackgroundColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          "No books in To-Be-Read",
                          textAlign: TextAlign.start,
                        ),
                      ),
                    ),
                    child: const Padding(
                      padding: EdgeInsets.only(top: 25.0),
                      child: NoDataWidget(
                        message: "No books in To-Be-Read",
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  Future<void> showAddBookPopUp(Function updateUI) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        bookSearchController.clear();
        bookCaseController?.updateTypeAheadFlag(false);
        isBookIdNotEmpty = false;
        bookListCount = 0;
        bookListLimit = 10;
        bookId = 0;

        suggestionsController?.dispose();
        suggestionsController = null;
        suggestionsController = SuggestionsController();

        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.all(20),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        bookSearchController.clear(); // Clear when closing
                        suggestionsController?.dispose();
                        suggestionsController = null;
                        context.pop();
                      },
                      child: Container(
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(top: 10),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Text(
                          "Add New Book",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 20.0, left: 30),
                          child: Consumer<ProfileController>(
                            builder: (context, profileController, child) {
                              return PaginatedBookTypeahead(
                                suggestionsController: suggestionsController,
                                controller: bookSearchController,
                                fetchBooksCallback: (query, offset, limit) =>
                                    BooksApiFunctions.fetchBooks(
                                  query,
                                  offset,
                                  limit,
                                  context,
                                  loggedinUserId,
                                ),
                                onSelected: (book) {
                                  if (book.bookName ==
                                      "Can't Find Your Book? Click Here") {
                                    questionFeedBox();
                                  } else {
                                    _handleBookSelection(book);
                                  }
                                },
                              );
                            },
                          ),
                        ),
                        Consumer<BookCaseController>(
                            builder: (context, bookCaseController, child) {
                          return Positioned(
                            top: 55,
                            left: 30,
                            right: 0,
                            child: bookCaseController.isTypeAheadEmpty
                                ? Text(
                                    "*Select book",
                                    style: lbRegular.copyWith(
                                      fontSize: 14,
                                      color: AppConstants.redColor,
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          );
                        }),
                        Positioned(
                          top: 55,
                          left: 30,
                          right: 0,
                          child: isBookIdNotEmpty && bookId == 0
                              ? Text(
                                  "*Invalid book",
                                  style: lbRegular.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ),
                      ],
                    ),
                    Consumer<BookCaseController>(
                        builder: (context, bookCaseController, child) {
                      return bookCaseController.isTypeAheadEmpty
                          ? const SizedBox(
                              height: 15,
                            )
                          : const SizedBox.shrink();
                    }),
                    isBookIdNotEmpty
                        ? const SizedBox(
                            height: 15,
                          )
                        : const SizedBox.shrink(),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NetworkAwareTap(
                            onTap: () async {
                              bool validation =
                                  _formKey.currentState!.validate();
                              if (bookSearchController.text.isEmpty) {
                                setState(() {
                                  bookCaseController?.updateTypeAheadFlag(true);
                                });
                                return;
                              }

                              if (isBookIdNotEmpty && validation) {
                                await confirmAddBook(false).then(
                                  (value) async {
                                    getToBeReadBook(true);
                                    updateUI();
                                    if (context.mounted) {
                                      context.pop();
                                    }

                                    if (value) {
                                      // context.pop();

                                      await showDialog(
                                        barrierColor: Colors.white60,
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (BuildContext context) {
                                          return CustomDialog(
                                            title: "Add New Book",
                                            message: responseMessage,
                                            showDoneImage: false,
                                            incomingClubFont: true,
                                          );
                                        },
                                      );
                                    }
                                    // context.pop();
                                    // setState(() {});

                                    // setState(() {});
                                  },
                                );
                              } else {
                                setState(() {
                                  isBookIdNotEmpty = true;
                                });
                              }
                              // context.pop();
                              // setState(() {});
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text(
                                  "Add",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          NetworkAwareTap(
                            onTap: () {
                              context.pop();
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.backgroundColor,
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  "Cancel",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              )
            ],
          );
        });
      },
    );
  }

  Widget _buildCantFindBookTile() {
    return ListTile(
      title: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: "Can't Find Your Book? ",
              style: lbRegular.copyWith(
                fontSize: 15,
                color: AppConstants.primaryColor,
              ), // Black color for the first part
            ),
            TextSpan(
              text: "Click Here",
              style: lbBold.copyWith(
                fontSize: 15,
                color: AppConstants.blueColor,
              ), // Blue color for "Click Here"
            ),
          ],
        ),
      ),
      shape: const Border(
        bottom: BorderSide(
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }

  Widget _buildBookTile(Books book) {
    return ListTile(
      title: Text(
        book.bookName ?? '',
        style: lbRegular.copyWith(fontSize: 15),
      ),
      subtitle: Text(
        book.bookAuthor ?? '',
        style: lbBold.copyWith(
          fontSize: 14,
          fontWeight: FontWeight.w900,
        ),
      ),
      shape: const Border(
        bottom: BorderSide(color: AppConstants.primaryColor),
      ),
    );
  }

  void _handleBookSelection(Books book) {
    setState(() {
      bookSearchController.text = book.bookName.toString();
      bookId = book.bookId;
      bookName = book.bookName ?? '';
      bookAuthor = book.bookAuthor ?? '';
      isBookIdNotEmpty = true;
    });
  }

  void questionFeedBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const QuestionFeedbackDialog(isBookQuery: true);
      },
    );
  }

  Future<void> moveToCurrentReadingPopUp(int index, bool isMoveOrDelete) async {
    await showCupertinoModalPopup(
      context: context,
      barrierColor: Colors.white60,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 30,
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Text(
                          isMoveOrDelete
                              ? "Are you sure you want to move this book to your currently reading list?"
                              : "Are you sure you want to delete this book?",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NetworkAwareTap(
                            onTap: () async {
                              if (isMoveOrDelete) {
                                await confirmAddBook(true).then((value) {});
                              } else {
                                bookId = toBeReadList?[index].bookId;
                                await deleteBook();
                              }
                              await getToBeReadBook(false);
                              setState(() {});
                              if (context.mounted) {
                                context.pop();
                              }
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text(
                                  isMoveOrDelete ? "Move" : "Delete",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          NetworkAwareTap(
                            onTap: () {
                              context.pop();
                              // notSentInvitationFunction();
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.backgroundColor,
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  "Cancel",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                    ],
                  ),
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
