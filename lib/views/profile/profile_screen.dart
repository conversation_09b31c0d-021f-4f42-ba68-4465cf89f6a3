import 'dart:developer';

import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/reusableWidgets/profile_home_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/common_helper.dart';
import '../../constants/constants.dart';
import '../../controller/user_controller.dart';
import '../../models/user_model.dart';
import '../../reusableWidgets/marquee_text.dart';
import '../../reusable_api_function/club/club_function.dart';

class ProfileHomeScreen extends StatefulWidget {
  const ProfileHomeScreen({super.key});

  @override
  State<ProfileHomeScreen> createState() => _ProfileHomeScreenState();
}

class _ProfileHomeScreenState extends State<ProfileHomeScreen> {
  bool? isUpdateProfile;
  bool isLoading = false;
  int? loggedinUserId;
  bool currentReadLoading = false;
  bool topShelfLoading = false;
  int offset = 0;
  int currentReadLimit = 10;
  int topShelfLimit = 10;
  int currentReadcount = 0;
  int topShelfCount = 0;
  int standingClubCount = 0;
  bool standingClubLoading = false;

  final ScrollController _currentReadScrollController = ScrollController();
  final ScrollController _topShelfScrollController = ScrollController();
  final ScrollController _standingClubscrollController = ScrollController();
  final ScrollController _impromClubScrollController = ScrollController();
  final ScrollController _toBeReadScrollController = ScrollController();
  UserController? userController;

  @override
  void initState() {
    clubController = Provider.of(context, listen: false);
    userController = Provider.of(context, listen: false);
    _currentReadScrollController.addListener(_currentReadOnScroll);
    _topShelfScrollController.addListener(_topShelfOnScroll);
    _standingClubscrollController.addListener(_standingOnScroll);
    _impromClubScrollController.addListener(_impromptuOnScroll);
    _toBeReadScrollController.addListener(_toBeReadOnScroll);
    super.initState();
  }

  // CURRENT READING SCROLL
  void _currentReadOnScroll() {
    if (_currentReadScrollController.position.pixels >=
            _currentReadScrollController.position.maxScrollExtent &&
        !currentReadLoading &&
        (currentbookCaseList?.length ?? 0) <= currentReadcount) {
      CommonHelper.networkClose(getCurrentReadBookCase(true), context);
    }
  }

// getCurrentReadBookCase(true);
  // TOPSHELF LIST SCROLL
  void _topShelfOnScroll() {
    if (_topShelfScrollController.position.pixels >=
            _topShelfScrollController.position.maxScrollExtent &&
        !topShelfLoading &&
        (topShelfList?.length ?? 0) < topShelfCount) {
      CommonHelper.networkClose(getTopShelfBookCase(true), context);
      // getFellowReaders(true); // Fetch more data
    }
  }

  // STANDING CLUB SCROLL
  void _standingOnScroll() {
    if (_standingClubscrollController.position.pixels >=
            _standingClubscrollController.position.maxScrollExtent &&
        (clubController?.standingLoading == false) &&
        (standingBookClubList?.length ?? 0) <
            (clubController?.standingClubCount ?? 0)) {
      CommonHelper.networkClose(getStandingBookClubsByUserId(true), context);
    }
  }

  // // IMPROMPTU CLUB SCROLL
  void _impromptuOnScroll() {
    if (_impromClubScrollController.position.pixels >=
            _impromClubScrollController.position.maxScrollExtent &&
        (clubController?.impromptuLoading == false) &&
        (impromptuBookClubList?.length ?? 0) <
            (clubController?.impromptuClubCount ?? 0)) {
      CommonHelper.networkClose(getImpromptuBookClubsByUserId(true), context);
    }
  }

  void _toBeReadOnScroll() {
    if (_toBeReadScrollController.position.pixels >=
            _toBeReadScrollController.position.maxScrollExtent &&
        !toBeReadLoading &&
        (toBeReadList?.length ?? 0) < (toBeReadcount)) {
      CommonHelper.networkClose(
        getToBeReadBook(true),
        context,
      );
    }
  }

  Future _initializeUserIdAndFetchData() async {
    await _initializeUserId();
    await getUserDetails();
    await Future.wait([
      getCurrentReadBookCase(false),
      getToBeReadBook(false),
      getTopShelfBookCase(false),
      getStandingBookClubsByUserId(false),
      getImpromptuBookClubsByUserId(false),
    ]);
  }

  String? userName;
  String? userLocation;
  String? userHandler;
  String? userBio;

  List<BookCaseModel>? currentbookCaseList = [];
  List<BookCaseModel>? topShelfList = [];
  List<BookCaseModel>? completedBooks = [];
  List<BookCaseModel>? bookCase;

  List<BookClubModel>? standingBookClubList = [];
  List<BookClubModel>? impromptuBookClubList = [];

  Future<void> _initializeUserId() async {
    userName = await CommonHelper.getLoggedinUserName();
    userLocation = await CommonHelper.getLoggedinUserLocation();
    userHandler = await CommonHelper.getLoggedinUserHandler();
    loggedinUserId = await CommonHelper.getLoggedInUserId();
    userBio = await CommonHelper.getLoggedinUserBio();
  }

  ClubController? clubController;
  BookCaseController? bookCaseController;
  BookClubController? bookClubController;

  Future<void> getCurrentReadBookCase(bool isMore) async {
    if ((currentbookCaseList?.length ?? 0) <= currentReadcount || !isMore) {
      currentReadLoading = true;
      bookCaseController?.notifyListeners();

      if (isMore) {
        currentReadLimit += 10;
      }
      // Increment the limit by 10 for the next load
    }
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .getCurrentReadBookCase(
              loggedinUserId ?? 0, currentReadLimit, offset, context)
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookList = [];
          currentReadcount = responseMap['count'];
          bookList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();

          var result =
              CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
          if (result.isNotEmpty) {
            // isLoading = false;
            currentbookCaseList = result[0];
          }

          if ((currentbookCaseList?.length ?? 0) >= currentReadcount) {
            currentReadLoading = false;
          }
          // isLoading = false;
        } else {}
      }).whenComplete(() {
        currentReadLoading = false;
        bookCaseController?.notifyListeners();
      });
    } catch (e) {
      log(e.toString());
    }
  }

  Future<void> getTopShelfBookCase(bool isMore) async {
    if ((topShelfList?.length ?? 0) <= topShelfCount || !isMore) {
      topShelfLoading = true;

      if (isMore) {
        topShelfLimit += 10;
      }
    }

    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .allBooksRead(
              loggedinUserId ?? 0, topShelfLimit, offset, false, false, context)
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookList = [];
          topShelfCount = responseMap['count'];
          bookList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
          Provider.of<BookCaseController>(context, listen: false)
              .notifyListeners();
          var result =
              CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
          if (result.isNotEmpty) {
            // isLoading = false;
            topShelfList = result[1];
            completedBooks = result[2];
            log("Completed Book Length: ${completedBooks?.length}");
          }

          // print("Length : ${topShelfList?.length}");
          if ((topShelfList?.length ?? 0) >= topShelfCount) {
            topShelfLoading = false;
          }
          // isLoading = false;
        } else {}
      }).whenComplete(() {
        topShelfLoading = false;
      });
    } catch (e) {
      log(e.toString());
    }
  }

  int toBeReadcount = 0;
  int toBeReadLimit = 10;
  bool toBeReadLoading = false;
  List<BookCaseModel>? toBeReadList = [];

  Future<void> getToBeReadBook(bool isMore) async {
    if ((toBeReadList?.length ?? 0) <= toBeReadcount || !isMore) {
      toBeReadLoading = true;

      if (isMore) {
        toBeReadLimit += 10; // Increment the limit by 10 for the next load
      }
    }
    log("Current Read Limit : $toBeReadLimit");
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .getToBeReadBook(loggedinUserId ?? 0, toBeReadLimit, offset, context)
          .then((responseMap) async {
        log("Response Map : ${responseMap['statusCode']}");
        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookCaseList = [];
          if (responseMap["count"] != null) {
            toBeReadcount = responseMap['count'];
          } else {
            toBeReadcount = 0;
          }
          log("ToBeRead Count : $toBeReadcount");

          if (responseMap["data"] != null) {
            bookCaseList = (responseMap["data"] as List)
                .map((item) => BookCaseModel.fromJson(item))
                .toList();
            toBeReadList = bookCaseList;
            // setState(() {});
            bookCaseController?.notifyListeners();
          } else {
            toBeReadList?.clear();
            toBeReadList = [];
          }
          log("BookCase List : ${bookCaseList.length}");
        } else if (responseMap['statusCode'] == 404) {
          log("ToBeRead Read : ${toBeReadList?.length}");

          toBeReadcount = 0;
        }
      }).whenComplete(() {
        toBeReadLoading = false;
      });
      log("ToBeRead Read : ${toBeReadList?.length}");
    } catch (e) {
      log(e.toString());
    }
  }

  Future<void> getStandingBookClubsByUserId(bool isMore) async {
    // print("API CALL 1");
    await Provider.of<ClubController>(context, listen: false)
        .getBookClubsByUserId(
            context, loggedinUserId ?? 0, ClubType.standing, isMore)
        .then((value) {
      if (mounted) {
        standingBookClubList =
            Provider.of<ClubController>(context, listen: false)
                .standingBookClubList;
      }
      clubController?.notifyListeners();
      // print("Standing : ${standingBookClubList?.length}");
    });
  }

  Future<void> getImpromptuBookClubsByUserId(bool isMore) async {
    await Provider.of<ClubController>(context, listen: false)
        .getBookClubsByUserId(
            context, loggedinUserId ?? 0, ClubType.impromptu, isMore)
        .then((value) {
      // standingBookClubList = Provider.of<ClubController>(context, listen: false)
      //     .standingBookClubList;
      if (mounted) {
        impromptuBookClubList =
            Provider.of<ClubController>(context, listen: false)
                .impromptuBookClubList;
      }
      clubController?.notifyListeners();
      // print("Impromptu : ${impromptuBookClubList?.length}");
    });
  }

  UserModel userModel = UserModel();

  Future<void> getUserDetails() async {
    try {
      // final responseMap =
      isLoading = true;
      await Provider.of<UserController>(context, listen: false)
          .getUserDetailsByUserId(loggedinUserId ?? 0, context)
          .then((responseMap) {
        userModel = userController?.userModel ?? UserModel();
      });
      isLoading = false;
    } catch (e) {
      log('An error occurred: $e');
    }
  }

  @override
  void dispose() {
    _standingClubscrollController.dispose();
    _impromClubScrollController.dispose();
    _currentReadScrollController.dispose();
    _topShelfScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: Consumer<UserController>(
            builder: (context, userController, child) {
              return Skeletonizer(
                effect: const SoldColorEffect(
                  color: AppConstants.skeletonforgroundColor,
                  lowerBound: 0.1,
                  upperBound: 0.5,
                ),
                containersColor: AppConstants.textGreenColor,
                enabled: userController.isUserLoading,
                child: Skeleton.replace(
                  replacement: ProfileHomeAppBar(
                    userName: userModel.data?.userName,
                    userHandler: userModel.data?.userHandle,
                    isClubInvitation:
                        userModel.data?.userClubInvitation ?? false,
                    userOwnProfile: true,
                    userProfilePicture: userModel.data?.userProfilePicture,
                  ),
                  child: ProfileHomeAppBar(
                    userName: userModel.data?.userName,
                    userHandler: userModel.data?.userHandle,
                    isClubInvitation:
                        userModel.data?.userClubInvitation ?? false,
                    userOwnProfile: true,
                    userProfilePicture: userModel.data?.userProfilePicture,
                  ),
                ),
              );
            },
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: FutureBuilder(
            future: _initializeUserIdAndFetchData(),
            builder: (context, snapShot) {
              final joiningDate = CommonHelper.getMonthYearDateFormat(
                  userModel.data?.userCreatedDate);

              return Skeletonizer(
                effect: const SoldColorEffect(
                  color: AppConstants.skeletonforgroundColor,
                  lowerBound: 0.1,
                  upperBound: 0.5,
                ),
                containersColor: AppConstants.skeletonBackgroundColor,
                enabled: snapShot.connectionState == ConnectionState.waiting,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          // mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 25.0),
                              child: Image.asset(
                                AppConstants.locationImagePath,
                                height: 25,
                                width: 25,
                                filterQuality: FilterQuality.high,
                                fit: BoxFit.cover,
                              ),
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 35.0),
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  maxWidth:
                                      MediaQuery.of(context).size.width / 2,
                                ),
                                child: Text(
                                  userModel.data?.userLocation ?? '',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbRegular.copyWith(
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(
                              width: 5,
                            ),
                            const Spacer(),
                            Padding(
                              padding: const EdgeInsets.only(top: 35.0),
                              child: Text(
                                "Joined: $joiningDate",
                                overflow: TextOverflow.ellipsis,
                                style: lbRegular.copyWith(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Text(
                          userModel.data?.userBio ?? '',
                          style: lbRegular.copyWith(
                            fontSize: 14,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: NetworkAwareTap(
                          onTap: () {
                            context.pushNamed(
                              'EditProfileScreen',
                              extra: {
                                'buttonName': 'Edit Profile',
                                "userModel": userModel,
                                'isCompleteProfile': true,
                              },
                            );
                          },
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                              color: AppConstants.textGreenColor,
                              borderRadius: BorderRadius.circular(90),
                            ),
                            child: Center(
                              child: Text(
                                "Edit Profile",
                                textAlign: TextAlign.center,
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),

                      const Divider(
                        thickness: 1.5,
                        color: AppConstants.primaryColor,
                      ),
                      const SizedBox(
                        height: 18,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          children: [
                            Text(
                              "Bookcase",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 24,
                              ),
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Image.asset(
                              "assets/icons/Bookcase_2.png",
                              height: 25,
                              width: 25,
                              filterQuality: FilterQuality.high,
                              fit: BoxFit.contain,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 18,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          children: [
                            Text(
                              "Currently Reading",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 20,
                              ),
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Image.asset(
                              AppConstants.currentlyReadingIcon,
                              height: 20,
                              width: 22,
                              filterQuality: FilterQuality.high,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      currentbookCaseList?.isNotEmpty ?? false
                          ? SizedBox(
                              height: 80,
                              child: Consumer<BookCaseController>(
                                builder: (context, bookCaseController, child) {
                                  return ListView.builder(
                                    controller: _currentReadScrollController,
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 20),
                                    itemCount: currentReadLoading
                                        ? (currentbookCaseList?.length ?? 0) + 1
                                        : currentbookCaseList?.length,
                                    itemBuilder: (context, index) {
                                      if (index ==
                                              currentbookCaseList?.length &&
                                          currentReadLoading) {
                                        return const Padding(
                                          padding: EdgeInsets.only(left: 10.0),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        );
                                      }
                                      return Skeleton.replace(
                                        replacement:
                                            currentReadSkeleton(true, index),
                                        child:
                                            currentReadSkeleton(false, index),
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                          : Skeleton.replace(
                              replacement: Container(
                                padding: const EdgeInsets.only(left: 20),
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                  color: AppConstants.skeletonBackgroundColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    "Add books to currently reading",
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                              ),
                              child: const NoDataWidget(
                                message: "Add books to currently reading",
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: NetworkAwareTap(
                          onTap: () {
                            // blockFunction();

                            context.pushNamed(
                              'EditCurrentReadBook',
                              extra: {
                                'buttonName': "Edit Currently Reading",
                                'topShelfList': topShelfList,
                                'completedBookList': completedBooks,
                                'userId': userModel.data?.userId,
                              },
                            );
                          },
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                              color: AppConstants.textGreenColor,
                              borderRadius: BorderRadius.circular(90),
                            ),
                            child: Center(
                              child: Text(
                                "Edit Currently Reading",
                                textAlign: TextAlign.center,
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(
                        height: 25,
                      ),

                      /// To-Be-Read (31 Dec 2024)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          children: [
                            Text(
                              "To-Be-Read",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 20,
                              ),
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Image.asset(
                              AppConstants.toBeReadIcon,
                              height: 20,
                              width: 26,
                              filterQuality: FilterQuality.high,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      toBeReadList?.isNotEmpty ?? false
                          ? SizedBox(
                              height: 80,
                              child: Consumer<BookCaseController>(
                                builder: (context, bookCaseController, child) {
                                  return ListView.builder(
                                    controller: _toBeReadScrollController,
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 20),
                                    itemCount: toBeReadLoading
                                        ? (toBeReadList?.length ?? 0) + 1
                                        : toBeReadList?.length,
                                    itemBuilder: (context, index) {
                                      if (index == toBeReadList?.length &&
                                          toBeReadLoading) {
                                        return const Padding(
                                          padding: EdgeInsets.only(left: 10.0),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        );
                                      }
                                      return Skeleton.replace(
                                        replacement:
                                            toBeReadSkeleton(true, index),
                                        child: toBeReadSkeleton(false, index),
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                          : Skeleton.replace(
                              replacement: Container(
                                padding: const EdgeInsets.only(left: 20),
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                  color: AppConstants.skeletonBackgroundColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    "No books in To-Be-Read",
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                              ),
                              child: const NoDataWidget(
                                message: "No books in To-Be-Read",
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: NetworkAwareTap(
                          onTap: () {
                            context.pushNamed(
                              'To-Be-Read',
                              // extra: {
                              //   'userProfilePicture':
                              //       userModel.data?.userProfilePicture,
                              //   'handler': userModel.data?.userHandle,
                              //   'userName': userModel.data?.userName,
                              //   'bookCaseList': bookCase,
                              //   'topShelfLength': topShelfList?.length,
                              //   'userClubInvitation':
                              //       userModel.data?.userClubInvitation,
                              // },
                            );
                          },
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.textGreenColor,
                            ),
                            child: Center(
                              child: Text(
                                "Edit To-Be-Read",
                                textAlign: TextAlign.center,
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),

                      /// All Books Read Section
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          children: [
                            Text(
                              "All Books Read",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 20,
                              ),
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Image.asset(
                              AppConstants.bookReadIcon,
                              height: 20,
                              width: 22,
                              filterQuality: FilterQuality.high,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      completedBooks?.isNotEmpty ?? false
                          ? SizedBox(
                              height: 117,
                              child: Consumer<BookCaseController>(
                                builder: (context, bookCaseController, child) {
                                  return ListView.builder(
                                    controller: _topShelfScrollController,
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 20),
                                    itemCount: topShelfLoading
                                        ? (completedBooks?.length ?? 0) + 1
                                        : completedBooks?.length,
                                    itemBuilder: (context, index) {
                                      if (index == completedBooks?.length &&
                                          topShelfLoading) {
                                        return const Padding(
                                          padding: EdgeInsets.only(left: 10.0),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        );
                                      }
                                      return Skeleton.replace(
                                        replacement:
                                            completeReadSkeleton(true, index),
                                        child:
                                            completeReadSkeleton(false, index),
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                          : Skeleton.replace(
                              replacement: Container(
                                padding: const EdgeInsets.only(left: 20),
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                  color: AppConstants.skeletonBackgroundColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    "No books read",
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                              ),
                              child: const NoDataWidget(
                                message: "No books read",
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: NetworkAwareTap(
                          onTap: () {
                            context.pushNamed(
                              'EditBookCase',
                              extra: {
                                'userProfilePicture':
                                    userModel.data?.userProfilePicture,
                                'handler': userModel.data?.userHandle,
                                'userName': userModel.data?.userName,
                                'bookCaseList': bookCase,
                                'topShelfLength': topShelfList?.length,
                                'userClubInvitation':
                                    userModel.data?.userClubInvitation,
                              },
                            );
                          },
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.textGreenColor,
                            ),
                            child: Center(
                              child: Text(
                                "View/Edit All Books Read",
                                textAlign: TextAlign.center,
                                style: lbBold.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      const Divider(
                        thickness: 1.5,
                        color: AppConstants.primaryColor,
                      ),
                      const SizedBox(
                        height: 18,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          children: [
                            Text(
                              "Book Clubs",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 24,
                              ),
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Image.asset(
                              "assets/icons/Clubs_2.png",
                              height: 35,
                              width: 35,
                              filterQuality: FilterQuality.high,
                              fit: BoxFit.contain,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 18,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Text(
                          "Standing Book Clubs",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 20,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      standingBookClubList?.isNotEmpty ?? false
                          ? SizedBox(
                              height: 132,
                              child: Consumer<ClubController>(
                                builder: (context, clubController, child) {
                                  return ListView.builder(
                                    controller: _standingClubscrollController,
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 20),
                                    itemCount: (clubController.standingLoading)
                                        ? (standingBookClubList?.length ?? 0) +
                                            1
                                        : standingBookClubList?.length,
                                    itemBuilder: (context, index) {
                                      // print(
                                      //     "Standing list length : ${standingBookClubList?.length}");
                                      if (index ==
                                              standingBookClubList?.length &&
                                          (clubController.standingLoading)) {
                                        return const Padding(
                                          padding: EdgeInsets.only(left: 10.0),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        );
                                      }
                                      return NetworkAwareTap(
                                        onTap: () {
                                          Provider.of<BookClubController>(
                                                  context,
                                                  listen: false)
                                              .updateData(
                                            standingBookClubList?[index] ??
                                                BookClubModel(),
                                          );
                                          navigateToBookClubDetails(
                                              standingBookClubList?[index] ??
                                                  BookClubModel());
                                        },
                                        child: Skeleton.replace(
                                          replacement:
                                              standingClubSkeleton(true, index),
                                          child: standingClubSkeleton(
                                              false, index),
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                          : Skeleton.replace(
                              replacement: Container(
                                padding: const EdgeInsets.only(left: 20),
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                  color: AppConstants.skeletonBackgroundColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    "Join or create a standing club",
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                              ),
                              child: const NoDataWidget(
                                message: "Join or create a standing club",
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),

                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Text(
                          "Impromptu Book Clubs",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 20,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      impromptuBookClubList?.isNotEmpty ?? false
                          ? SizedBox(
                              height: 181,
                              child: Consumer<ClubController>(
                                builder: (context, clubController, child) {
                                  return ListView.builder(
                                    controller: _impromClubScrollController,
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 20),
                                    itemCount: (clubController.impromptuLoading)
                                        ? (impromptuBookClubList?.length ?? 0) +
                                            1
                                        : impromptuBookClubList?.length,
                                    itemBuilder: (context, index) {
                                      if (index ==
                                              impromptuBookClubList?.length &&
                                          (clubController.impromptuLoading)) {
                                        return const Padding(
                                          padding: EdgeInsets.only(left: 10.0),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        );
                                      }
                                      return NetworkAwareTap(
                                        onTap: () {
                                          Provider.of<BookClubController>(
                                                  context,
                                                  listen: false)
                                              .updateData(
                                            impromptuBookClubList?[index] ??
                                                BookClubModel(),
                                          );
                                          navigateToBookClubDetails(
                                              impromptuBookClubList?[index] ??
                                                  BookClubModel());
                                        },
                                        child: Skeleton.replace(
                                          replacement: impromptuClubSkeleton(
                                              true, index),
                                          child: impromptuClubSkeleton(
                                              false, index),
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                          : Skeleton.replace(
                              replacement: Container(
                                padding: const EdgeInsets.only(left: 20),
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                  color: AppConstants.skeletonBackgroundColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    "Join or create an impromptu club",
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                              ),
                              child: const NoDataWidget(
                                message: 'Join or create an impromptu club',
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),
                    ],
                  ),
                ),
              );
            }),
      ),
    );
  }

  Widget currentReadSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  currentbookCaseList?[index].bookName ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 3,
            ),
            MarqueeList(
              children: [
                Text(
                  currentbookCaseList?[index].bookAuthor ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget toBeReadSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  toBeReadList?[index].bookName ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 3,
            ),
            MarqueeList(
              children: [
                Text(
                  toBeReadList?[index].bookAuthor ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget completeReadSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  completedBooks?[index].bookName ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            MarqueeList(
              children: [
                Text(
                  completedBooks?[index].bookAuthor ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(),
                ),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                (completedBooks?[index].review?.isNotEmpty ?? false)
                    ? NetworkAwareTap(
                        onTap: () {
                          context.pushNamed(
                            "book-review",
                            extra: {
                              // 'index': index,
                              'userName': userName,
                              'bookName': completedBooks?[index].bookName,
                              'bookAuthor': completedBooks?[index].bookAuthor,
                              'userHandle': userHandler,
                              'ratings': completedBooks?[index].ratings,
                              'review': completedBooks?[index].review,
                              'userProfile': userModel.data?.userProfilePicture,
                              'userClubInvitation':
                                  userModel.data?.userClubInvitation ?? false,
                              'userOwnProfile': true,
                            },
                          );
                        },
                        child: Text(
                          "Review",
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          style: lbItalic.copyWith(
                            fontSize: 12,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      )
                    : RatingBar(
                        ignoreGestures: true,
                        itemCount: 5,
                        itemSize: 20,
                        allowHalfRating: true,
                        initialRating: completedBooks?[index].ratings ?? 0,
                        minRating: 0,
                        ratingWidget: RatingWidget(
                          full: const Icon(
                            Icons.star,
                            color: AppConstants.textGreenColor,
                          ),
                          half: const Icon(
                            Icons.star_half,
                            color: AppConstants.textGreenColor,
                          ),
                          empty: const Icon(
                            Icons.star_border_outlined,
                            color: AppConstants.textGreenColor,
                          ),
                        ),
                        onRatingUpdate: (double value) {},
                      ),
                const SizedBox(
                  width: 10,
                ),
                (completedBooks?[index].review?.isNotEmpty ?? false)
                    ? RatingBar(
                        ignoreGestures: true,
                        itemCount: 5,
                        itemSize: 20,
                        allowHalfRating: true,
                        initialRating: completedBooks?[index].ratings ?? 0,
                        minRating: 0,
                        ratingWidget: RatingWidget(
                          full: const Icon(
                            Icons.star,
                            color: AppConstants.textGreenColor,
                          ),
                          half: const Icon(
                            Icons.star_half,
                            color: AppConstants.textGreenColor,
                          ),
                          empty: const Icon(
                            Icons.star_border_outlined,
                            color: AppConstants.textGreenColor,
                          ),
                        ),
                        onRatingUpdate: (double value) {},
                      )
                    : const SizedBox.shrink(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget standingClubSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 228,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          children: [
            Stack(
              alignment: Alignment.center,
              // mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Visibility(
                    replacement: const SizedBox.shrink(),
                    visible:
                        standingBookClubList?[index].userId == loggedinUserId,
                    child: Image.asset(
                      AppConstants.leaderStar,
                      height: 43,
                      width: 43,
                      fit: BoxFit.cover,
                      filterQuality: FilterQuality.high,
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: Image.asset(
                    (standingBookClubList?[index].totalVacancies ?? 0) > 0
                        ? AppConstants.clubOpeningLogoImagePath
                        : AppConstants.clubOpeningZero,
                    height: 50,
                    width: 50,
                    fit: BoxFit.cover,
                    filterQuality: FilterQuality.high,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 25,
            ),
            MarqueeList(
              children: [
                Text(
                  standingBookClubList?[index].bookClubName ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget impromptuClubSkeleton(bool isBorder, int index) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 228,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          children: [
            Stack(
              alignment: Alignment.center,
              // mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Visibility(
                    replacement: const SizedBox.shrink(),
                    visible:
                        impromptuBookClubList?[index].userId == loggedinUserId,
                    child: Image.asset(
                      AppConstants.leaderStar,
                      height: 43,
                      width: 43,
                      fit: BoxFit.contain,
                      filterQuality: FilterQuality.high,
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: Image.asset(
                    (impromptuBookClubList?[index].totalVacancies ?? 0) > 0
                        ? AppConstants.clubOpeningLogoImagePath
                        : AppConstants.clubOpeningZero,
                    height: 50,
                    width: 50,
                    fit: BoxFit.contain,
                    filterQuality: FilterQuality.high,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 25,
            ),
            MarqueeList(
              children: [
                Text(
                  impromptuBookClubList?[index].bookClubName ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 3,
            ),
            Text(
              impromptuBookClubList?[index].bookAuthor ?? '',
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: lbRegular.copyWith(
                fontSize: 14,
              ),
            ),
            const Spacer(),
            Text(
              impromptuBookClubList?[index].clubCount ?? '',
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: lbItalic.copyWith(
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  navigateToBookClubDetails(BookClubModel bookClubItem) {
    context.pushNamed(
      'user-club-details',
      queryParameters: {
        'bookClubId': bookClubItem.bookClubId.toString(),
        'userId': loggedinUserId.toString(),
      },
    );
  }
}
