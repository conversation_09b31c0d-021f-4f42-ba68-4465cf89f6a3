// ignore_for_file: invalid_use_of_protected_member

import 'dart:async';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/connectivity_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/services/overlay_service.dart';
import 'package:eljunto/services/video_call_manager.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../constants/config.dart';
import '../controller/book_club_controller.dart';
import '../reusableWidgets/imageBuilder.dart';

class UIScreen extends StatefulWidget {
  final String? bookName;
  final String? token;
  final int? userId;
  final String? discussionQue;
  final String? channelName;
  final String? userHandle;
  final String? profilePictureUrl;
  final bool isRejoin;

  const UIScreen({
    super.key,
    this.bookName,
    this.token,
    this.userId,
    this.discussionQue,
    this.channelName,
    this.userHandle,
    this.profilePictureUrl,
    this.isRejoin = false,
  });

  @override
  State<UIScreen> createState() => _UIScreenState();
}

class _UIScreenState extends State<UIScreen> {
  final _videoCallManager = VideoCallManager();
  RtcEngine? _engine;

  bool _isLoading = true;
  bool _callJoinedSuccessfully = false;
  bool _buttonsVisible = true;
  Timer? _autoHideTimer;
  bool _isExitingForPipMode = false;

  final _userHandlesMap = <int, String>{};
  final _userProfilePictureMap = <int, String>{};
  final Map<int, VideoViewController> _screenRemoteControllers = {};

  DateTime _lastQualityAdjustment = DateTime.now();
  final _debounceInterval = const Duration(seconds: 3);
  int _poorQualityCounter = 0;
  int _goodQualityCounter = 0;

  final double _controlPanelHeight = 80.0;
  final double _bottomSheetPeekHeight = 80.0;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    WakelockPlus.enable();
    _CallManagerListenerMixin.addManagerListeners(this);
    _CallInitializationMixin.initializeCallAndPermissions(this);
    _startAutoHideTimer();
  }

  @override
  void dispose() {
    _CallManagerListenerMixin.removeManagerListeners(this);
    WakelockPlus.disable();
    _screenRemoteControllers.forEach((_, controller) => controller.dispose());
    _screenRemoteControllers.clear();
    _autoHideTimer?.cancel();
    super.dispose();
  }

  void _loadInitialData() {
    final bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    for (final map in bookClubController.userProfilePicture) {
      map.forEach((key, value) {
        if (key != null && value != null) _userProfilePictureMap[key] = value;
      });
    }
    for (final map in bookClubController.userHandles) {
      map.forEach((key, value) {
        if (key != null && value != null) _userHandlesMap[key] = value;
      });
    }
    if (widget.userId != null && widget.userHandle != null) {
      _userHandlesMap[widget.userId!] ??= widget.userHandle!;
    }
    if (widget.userId != null && widget.profilePictureUrl != null) {
      _userProfilePictureMap[widget.userId!] ??= widget.profilePictureUrl!;
    }
  }

  void _syncScreenControllers(Set<int> currentRemoteUids) {
    if (!mounted || _engine == null) {
      _screenRemoteControllers.forEach((_, controller) => controller.dispose());
      _screenRemoteControllers.clear();
      return;
    }

    final currentScreenUids = _screenRemoteControllers.keys.toSet();
    final uidsToAdd = currentRemoteUids.difference(currentScreenUids);
    final uidsToRemove = currentScreenUids.difference(currentRemoteUids);

    for (int uid in uidsToRemove) {
      _screenRemoteControllers.remove(uid)?.dispose();
    }

    final channelName = _videoCallManager.getCallMetadata()?['channelName'] ??
        widget.channelName ??
        "";

    for (int uid in uidsToAdd) {
      _screenRemoteControllers[uid] = VideoViewController.remote(
        rtcEngine: _engine!,
        canvas: VideoCanvas(
          uid: uid,
          renderMode: RenderModeType.renderModeHidden,
          mirrorMode: VideoMirrorModeType.videoMirrorModeDisabled,
        ),
        connection: RtcConnection(channelId: channelName),
        useFlutterTexture: false,
        useAndroidSurfaceView: false,
      );
    }
  }

  void _startAutoHideTimer() {
    _autoHideTimer?.cancel();
    _autoHideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _buttonsVisible) {
        setState(() {
          _buttonsVisible = false;
        });
      }
    });
  }

  void _onScreenTap() {
    if (!mounted) return;
    setState(() {
      _buttonsVisible = !_buttonsVisible;
    });
    if (_buttonsVisible) {
      _startAutoHideTimer();
    } else {
      _autoHideTimer?.cancel();
    }
  }

  Future<void> _toggleAudio() async {
    if (_videoCallManager.localAudioMutedNotifier.value) {
      final hasPermission = await _PermissionHandlerMixin.checkPermission(
          this, Permission.microphone);
      if (!hasPermission) return;
    }
    _videoCallManager.toggleAudio();
    _startAutoHideTimer();
  }

  Future<void> _toggleVideo() async {
    if (_videoCallManager.localVideoMutedNotifier.value) {
      final hasPermission = await _PermissionHandlerMixin.checkPermission(
          this, Permission.camera);
      if (!hasPermission) return;
    }
    _videoCallManager.toggleVideo();
    _startAutoHideTimer();
  }

  void _toggleCamera() {
    _videoCallManager.switchCamera();
    _startAutoHideTimer();
  }

  void _togglePipMode({bool isInitiatedByDialog = false}) {
    if (!_videoCallManager.isCallActive) return;
    OverlayService().showVideoCallOverlay();
    if (mounted) {
      _isExitingForPipMode = true;
      context.pop();
    }
  }

  Future<void> _onCallEnd(
      {bool userInitiated = false, bool remoteInitiated = false}) async {
    if (_isExitingForPipMode) return;
    if (!_videoCallManager.isCallActive && !userInitiated && !remoteInitiated) {
      return;
    }

    await _videoCallManager.endCall();
    if ((userInitiated || remoteInitiated) && mounted && context.canPop()) {
      try {
        context.pop();
      } catch (e) {
        // Ignore
      }
    }
  }

  void _showErrorAndPop(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 2)),
    );
    Future.delayed(const Duration(milliseconds: 2100), () {
      if (mounted && context.canPop()) {
        try {
          context.pop();
        } catch (e) {
          // Ignore
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;

        if (_isExitingForPipMode) {
          _isExitingForPipMode = false;
          if (mounted && context.canPop()) {
            context.pop();
          } else if (mounted) {
            context.go('/');
          }
          return;
        }

        final bool? shouldLeave =
            await _UIScreenWidgets.showLeaveCallConfirmationDialog(
                context, _videoCallManager.isCallActive, _togglePipMode);
        if (shouldLeave == true) {
          await _onCallEnd(userInitiated: true);
          if (context.mounted && context.canPop()) {
            context.pop();
          }
        } else if (shouldLeave == null) {
          // null indicates "Background" / PiP
          _togglePipMode(isInitiatedByDialog: true);
        }
      },
      child: Scaffold(
        appBar: _UIScreenWidgets.buildAppBar(context, widget.bookName ?? ''),
        body: Container(
          color: Colors.black,
          child: _buildScreenContent(),
        ),
      ),
    );
  }

  Widget _buildScreenContent() {
    if (_isLoading) {
      return _UIScreenWidgets.buildLoadingIndicator();
    } else if (!_callJoinedSuccessfully) {
      return _UIScreenWidgets.buildCallFailedView();
    } else {
      return Column(
        children: [
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: _onScreenTap,
              child: Stack(
                children: [
                  AnimatedPadding(
                    padding: EdgeInsets.only(
                        bottom: _buttonsVisible ? _controlPanelHeight : 0.0),
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    child: Container(
                        color: Colors.black,
                        child: _UIScreenWidgets.buildGridLayout(this)),
                  ),
                  AnimatedPositioned(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    bottom: _buttonsVisible ? 0 : -_controlPanelHeight,
                    left: 0,
                    right: 0,
                    child: _UIScreenWidgets.buildSlidingControlPanel(this),
                  ),
                  Consumer<ConnectivityProvider>(
                      builder: (context, provider, _) {
                    bool showNoInternetBanner =
                        provider.status == InternetStatus.disconnected;
                    return AnimatedPositioned(
                      duration: const Duration(milliseconds: 300),
                      bottom: showNoInternetBanner ? _controlPanelHeight : -150,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            color: AppConstants.backgroundColor,
                            borderRadius: BorderRadius.circular(5),
                            border:
                                Border.all(color: AppConstants.primaryColor),
                          ),
                          child: Text(
                            'No Internet Connection',
                            style: lbRegular.copyWith(
                              fontSize: 10,
                              color: AppConstants.primaryColor,
                              decoration: TextDecoration.none,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
          _UIScreenWidgets.buildDiscussionSheetPeekView(
            this,
            context,
            widget.discussionQue,
          ),
        ],
      );
    }
  }
}

// --- Mixin for Call Initialization Logic ---
class _CallInitializationMixin {
  static Future<void> initializeCallAndPermissions(
      _UIScreenState screenState) async {
    if (screenState.mounted) {
      screenState.setState(() {
        screenState._isLoading = true;
        screenState._callJoinedSuccessfully = false;
      });
    }

    final micStatus = await Permission.microphone.request();
    final camStatus = await Permission.camera.request();
    bool initialMicMute = !(micStatus.isGranted || micStatus.isLimited);
    bool initialCamMute = !(camStatus.isGranted || camStatus.isLimited);

    if (!screenState.mounted) return;

    if (micStatus.isPermanentlyDenied || camStatus.isPermanentlyDenied) {
      if (!screenState.mounted) return;
      _PermissionHandlerMixin.showPermissionInfoDialog(
        screenState,
        isPermanentlyDeniedMic: micStatus.isPermanentlyDenied,
        isPermanentlyDeniedCam: camStatus.isPermanentlyDenied,
      );
    }

    if (screenState._videoCallManager.isCallActive &&
        screenState._videoCallManager.engine != null) {
      screenState._engine = screenState._videoCallManager.engine;
      _CallManagerListenerMixin.updateStateFromManager(screenState);
      screenState._callJoinedSuccessfully = true;
      _VideoQualityMixin.addNetworkQualityListener(screenState);
      if (!screenState.mounted) return;
      screenState.setState(() {
        screenState._isLoading = false;
      });
    } else {
      if (screenState.widget.channelName == null ||
          screenState.widget.token == null ||
          screenState.widget.userHandle == null) {
        screenState._showErrorAndPop("Missing call information.");
        if (!screenState.mounted) return;
        screenState.setState(() {
          screenState._isLoading = false;
        });
        return;
      }

      bool success = await screenState._videoCallManager.startCall(
        channelName: screenState.widget.channelName!,
        token: screenState.widget.token!,
        preferredLocalUid: screenState.widget.userId ?? 0,
        userHandle: screenState.widget.userHandle!,
        profilePictureUrl: screenState.widget.profilePictureUrl,
        discussionQue: screenState.widget.discussionQue,
        bookName: screenState.widget.bookName,
        initialAudioMute: initialMicMute,
        initialVideoMute: initialCamMute,
      );

      if (!screenState.mounted) return;

      if (success) {
        screenState._engine = screenState._videoCallManager.engine;
        if (screenState._engine != null) {
          _VideoQualityMixin.addNetworkQualityListener(screenState);
          screenState.setState(() {
            screenState._callJoinedSuccessfully = true;
            screenState._isLoading = false;
          });
        } else {
          screenState._showErrorAndPop("Failed to obtain video engine.");
          screenState.setState(() {
            screenState._isLoading = false;
          });
        }
      } else {
        screenState._showErrorAndPop("Failed to start video call.");
        screenState.setState(() {
          screenState._isLoading = false;
        });
      }
    }
  }
}

// --- Mixin for VideoCallManager Listeners ---
class _CallManagerListenerMixin {
  static void addManagerListeners(_UIScreenState screenState) {
    screenState._videoCallManager.callStateNotifier
        .addListener(() => _handleCallStateChange(screenState));

    screenState._videoCallManager.localAudioMutedNotifier
        .addListener(() => _updateLocalMuteStates(screenState));

    screenState._videoCallManager.localVideoMutedNotifier
        .addListener(() => _updateLocalMuteStates(screenState));

    screenState._videoCallManager.remoteUsersNotifier
        .addListener(() => _updateRemoteUsers(screenState));

    screenState._videoCallManager.remoteVideoMutedNotifier
        .addListener(() => _updateRemoteUsers(screenState));

    screenState._videoCallManager.remoteAudioMutedNotifier
        .addListener(() => _updateRemoteAudioStates(screenState));
    updateStateFromManager(screenState);
  }

  static void removeManagerListeners(_UIScreenState screenState) {
    screenState._videoCallManager.callStateNotifier
        .removeListener(() => _handleCallStateChange(screenState));

    screenState._videoCallManager.localAudioMutedNotifier
        .removeListener(() => _updateLocalMuteStates(screenState));

    screenState._videoCallManager.localVideoMutedNotifier
        .removeListener(() => _updateLocalMuteStates(screenState));

    screenState._videoCallManager.remoteUsersNotifier
        .removeListener(() => _updateRemoteUsers(screenState));

    screenState._videoCallManager.remoteVideoMutedNotifier
        .removeListener(() => _updateRemoteUsers(screenState));

    screenState._videoCallManager.remoteAudioMutedNotifier
        .removeListener(() => _updateRemoteAudioStates(screenState));
  }

  static void _handleCallStateChange(_UIScreenState screenState) {
    if (!screenState.mounted) return;
    final isActive = screenState._videoCallManager.callStateNotifier.value;
    screenState.setState(() {
      screenState._callJoinedSuccessfully = isActive;
      screenState._isLoading = !isActive;
      if (!isActive && screenState._videoCallManager.engine == null) {
        screenState._onCallEnd(remoteInitiated: true);
      }
    });
  }

  static void _updateLocalMuteStates(_UIScreenState screenState) {
    if (!screenState.mounted) return;
    screenState.setState(() {});
  }

  static void _updateRemoteUsers(_UIScreenState screenState) {
    if (!screenState.mounted) return;
    final newUids = screenState._videoCallManager.remoteUsersNotifier.value;
    screenState._syncScreenControllers(newUids);
    screenState.setState(() {});
  }

  static void _updateRemoteAudioStates(_UIScreenState screenState) {
    if (!screenState.mounted) return;
    screenState.setState(() {});
  }

  static void updateStateFromManager(_UIScreenState screenState) {
    if (!screenState.mounted) return;
    screenState._engine = screenState._videoCallManager.engine;
    screenState._callJoinedSuccessfully =
        screenState._videoCallManager.isCallActive;
    screenState._isLoading = !screenState._callJoinedSuccessfully &&
        screenState._engine != null &&
        !screenState._videoCallManager.isCallActive;
    screenState._syncScreenControllers(
        screenState._videoCallManager.remoteUsersNotifier.value);
    screenState.setState(() {});
  }
}

// --- Mixin for Permission Handling ---
class _PermissionHandlerMixin {
  static Future<bool> checkPermission(
      _UIScreenState screenState, Permission permission) async {
    final status = await permission.status;
    if (status.isDenied) {
      final result = await permission.request();
      return result.isGranted;
    }
    if (status.isPermanentlyDenied) {
      final permissionType =
          permission == Permission.camera ? 'Camera' : 'Microphone';
      if (screenState.mounted) {
        await showFeaturePermissionDialog(
          screenState,
          permissionType,
        );
      }
      return false;
    }
    return status.isGranted;
  }

  static void showPermissionInfoDialog(
    _UIScreenState screenState, {
    required bool isPermanentlyDeniedMic,
    required bool isPermanentlyDeniedCam,
  }) {
    if (!screenState.mounted) return;
    String message = 'Some features are limited:\n\n';
    if (isPermanentlyDeniedMic) message += '• Microphone access is disabled.\n';
    if (isPermanentlyDeniedCam) message += '• Camera access is disabled.\n';
    message += 'You can enable them in Settings.';

    showDialog(
      context: screenState.context,
      builder: (ctx) => _UIScreenWidgets.buildPermissionDialog(
        ctx,
        title: 'Limited Permissions',
        message: message,
        primaryActionText: 'Open Settings',
        onPrimaryAction: () {
          ctx.pop();
          openAppSettings();
        },
        secondaryActionText: 'Continue Anyway',
        onSecondaryAction: () => ctx.pop(),
      ),
    );
  }

  static Future<void> showFeaturePermissionDialog(
      _UIScreenState screenState, String permissionType) async {
    if (!screenState.mounted) return;
    await showDialog(
      context: screenState.context,
      barrierColor: Colors.white38,
      builder: (ctx) => _UIScreenWidgets.buildPermissionDialog(
        ctx,
        title: 'Enable $permissionType',
        message:
            'To use the $permissionType, please enable permission in Settings.',
        primaryActionText: 'Open Settings',
        onPrimaryAction: () {
          ctx.pop();
          openAppSettings();
        },
        secondaryActionText: 'Not now',
        onSecondaryAction: () => ctx.pop(),
        isFeatureSpecific: true,
      ),
    );
  }
}

// --- Mixin for Video Quality Adjustment ---
class _VideoQualityMixin {
  static const _lowQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 320, height: 240),
      frameRate: 15,
      bitrate: 300,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _wifilowQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 320, height: 240),
      frameRate: 15,
      bitrate: 300,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _mediumQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 640, height: 480),
      frameRate: 20,
      bitrate: 500,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _wifimediumQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 640, height: 480),
      frameRate: 20,
      bitrate: 500,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _highQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 1280, height: 720),
      frameRate: 30,
      bitrate: 800,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _vHighQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 1920, height: 1080),
      frameRate: 30,
      bitrate: 1000,
      orientationMode: OrientationMode.orientationModeAdaptive);

  static void addNetworkQualityListener(_UIScreenState screenState) {
    if (screenState._engine == null) return;
    screenState._engine!.registerEventHandler(
      RtcEngineEventHandler(
        onNetworkQuality: (connection, remoteUid, txQuality, rxQuality) async {
          if (!screenState.mounted) return;
          final List<ConnectivityResult> connectivityResult =
              await (Connectivity().checkConnectivity());
          _adjustVideoQuality(
              screenState, txQuality, rxQuality, connectivityResult);
        },
      ),
    );
  }

  static Future<void> _adjustVideoQuality(
      _UIScreenState screenState,
      QualityType txQuality,
      QualityType rxQuality,
      List<ConnectivityResult> connectivityResult) async {
    if (!screenState.mounted ||
        DateTime.now().difference(screenState._lastQualityAdjustment) <
            screenState._debounceInterval) {
      return;
    }

    bool isPoorNet = _isLowQuality(txQuality) || _isLowQuality(rxQuality);
    bool isGoodNet = _isGoodQuality(txQuality) && _isGoodQuality(rxQuality);

    if (isPoorNet) {
      screenState._poorQualityCounter++;
      screenState._goodQualityCounter = 0;
    } else if (isGoodNet) {
      screenState._goodQualityCounter++;
      screenState._poorQualityCounter = 0;
    }

    VideoEncoderConfiguration? configToSet;

    if (_isVeryPoorQuality(txQuality) ||
        _isVeryPoorQuality(rxQuality) ||
        screenState._poorQualityCounter >= 2) {
      configToSet = connectivityResult.contains(ConnectivityResult.wifi)
          ? _wifilowQualityConfig
          : _lowQualityConfig;
      screenState._poorQualityCounter = 0;
    } else if ((_isPoorQuality(txQuality) || _isPoorQuality(rxQuality)) &&
        screenState._goodQualityCounter < 3) {
      configToSet = connectivityResult.contains(ConnectivityResult.wifi)
          ? _wifimediumQualityConfig
          : _mediumQualityConfig;
    } else if (screenState._goodQualityCounter >= 3) {
      configToSet = connectivityResult.contains(ConnectivityResult.wifi)
          ? _vHighQualityConfig
          : _highQualityConfig;
      screenState._goodQualityCounter = 0;
    }

    if (configToSet != null) {
      await screenState._videoCallManager
          .setVideoEncoderConfiguration(configToSet);
      screenState._lastQualityAdjustment = DateTime.now();
    }
  }

  static bool _isVeryPoorQuality(QualityType q) =>
      q == QualityType.qualityBad ||
      q == QualityType.qualityVbad ||
      q == QualityType.qualityDown ||
      q == QualityType.qualityUnknown;
  static bool _isPoorQuality(QualityType q) => q == QualityType.qualityPoor;
  static bool _isGoodQuality(QualityType q) =>
      q == QualityType.qualityGood || q == QualityType.qualityExcellent;
  static bool _isLowQuality(QualityType q) =>
      _isVeryPoorQuality(q) || _isPoorQuality(q);
}

// --- Static class for UI Builder Methods ---
class _UIScreenWidgets {
  static AppBar buildAppBar(BuildContext context, String bookName) {
    return AppBar(
      backgroundColor: AppConstants.videoConferenceAppBarColor,
      centerTitle: true,
      leading: IconButton(
        icon: const Icon(
          Icons.arrow_back,
          color: AppConstants.backgroundColor,
        ),
        onPressed: () => Navigator.of(context).maybePop(),
      ),
      title: MarqueeList(
        children: [
          Text(
            bookName,
            style: lbBold.copyWith(
              fontSize: 18,
              color: AppConstants.backgroundColor,
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildLoadingIndicator() {
    return Container(
      color: AppConstants.backgroundColor,
      child: const Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }

  static Widget buildCallFailedView() {
    return Container(
      color: AppConstants.backgroundColor,
      child: Center(
        child: Text(
          "Failed to join the call. Please try again.",
          style: lbBold.copyWith(
            color: AppConstants.redColor,
            fontSize: 18,
          ),
        ),
      ),
    );
  }

  static Future<bool?> showLeaveCallConfirmationDialog(
    BuildContext context,
    bool isCallActive,
    Function({bool isInitiatedByDialog}) togglePipMode,
  ) async {
    if (!isCallActive) return true;
    return await showDialog<bool?>(
      context: context,
      builder: (ctx) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: const BorderSide(color: AppConstants.primaryColor),
        ),
        backgroundColor: AppConstants.backgroundColor,
        buttonPadding: EdgeInsets.zero,
        title: Text(
          'Leave Call?',
          style: lbBold.copyWith(
            fontSize: 18,
            color: AppConstants.primaryColor,
          ),
        ),
        content: Text(
          'End the call or keep it running in the background (PiP)?',
          style: lbRegular.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => ctx.pop(false),
            style: TextButton.styleFrom(
                splashFactory: NoSplash.splashFactory,
                shadowColor: Colors.transparent,
                surfaceTintColor: Colors.transparent),
            child: Text(
              'Cancel',
              style: lbBold.copyWith(
                fontSize: 14,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () => ctx.pop(null),
            child: Text(
              'Background',
              style: lbBold.copyWith(
                fontSize: 14,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () => ctx.pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(
              'End Call',
              style: lbBold.copyWith(
                fontSize: 14,
                color: AppConstants.redColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildSlidingControlPanel(_UIScreenState screenState) {
    return Container(
      height: screenState._controlPanelHeight,
      color: Colors.black.withOpacity(0.5),
      child: _buildControlButtons(screenState),
    );
  }

  static Widget _buildControlButtons(_UIScreenState screenState) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 6),
      margin: const EdgeInsets.symmetric(vertical: 6),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: ValueListenableBuilder<bool>(
              valueListenable:
                  screenState._videoCallManager.localAudioMutedNotifier,
              builder: (ctx, isMuted, _) => RawMaterialButton(
                onPressed: screenState._toggleAudio,
                shape: const CircleBorder(),
                padding: const EdgeInsets.all(20),
                visualDensity: VisualDensity.compact,
                fillColor:
                    isMuted ? AppConstants.redColor : AppConstants.primaryColor,
                child: Icon(
                  isMuted ? Icons.mic_off : Icons.mic,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
          Expanded(
            child: RawMaterialButton(
              onPressed: () => screenState._onCallEnd(userInitiated: true),
              shape: const CircleBorder(),
              padding: const EdgeInsets.all(15),
              fillColor: AppConstants.redColor,
              child: const Icon(
                Icons.call_end,
                color: Colors.white,
                size: 25,
              ),
            ),
          ),
          Expanded(
            child: ValueListenableBuilder<bool>(
              valueListenable:
                  screenState._videoCallManager.localVideoMutedNotifier,
              builder: (ctx, isMuted, _) => RawMaterialButton(
                onPressed: screenState._toggleVideo,
                shape: const CircleBorder(),
                padding: const EdgeInsets.all(20),
                visualDensity: VisualDensity.compact,
                fillColor:
                    isMuted ? AppConstants.redColor : AppConstants.primaryColor,
                child: Icon(
                  isMuted ? Icons.videocam_off : Icons.videocam,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
          Expanded(
            child: RawMaterialButton(
              onPressed: screenState._toggleCamera,
              shape: const CircleBorder(),
              visualDensity: VisualDensity.compact,
              padding: const EdgeInsets.all(20),
              fillColor: AppConstants.primaryColor,
              child: const Icon(
                Icons.cameraswitch_outlined,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          Expanded(
            child: RawMaterialButton(
              onPressed: screenState._togglePipMode,
              shape: const CircleBorder(),
              padding: const EdgeInsets.all(20),
              visualDensity: VisualDensity.compact,
              fillColor: AppConstants.primaryColor,
              child: const Icon(
                Icons.picture_in_picture_alt,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildGridLayout(_UIScreenState screenState) {
    return ValueListenableBuilder<Set<int>>(
      valueListenable: screenState._videoCallManager.remoteUsersNotifier,
      builder: (context, remoteUids, _) {
        final List<Widget> allParticipantViews = [];
        final localUid = screenState._videoCallManager.localUid;

        if (screenState._callJoinedSuccessfully &&
            screenState._engine != null &&
            localUid != null) {
          allParticipantViews.add(
            _buildVideoContainer(screenState, isLocal: true, uid: localUid),
          );
        }

        for (int uid in remoteUids) {
          final controller = screenState._screenRemoteControllers[uid];
          if (controller != null) {
            allParticipantViews.add(
              _buildVideoContainer(screenState,
                  isLocal: false, uid: uid, controller: controller),
            );
          }
        }

        if (allParticipantViews.isEmpty) {
          return const Center(
              child: Text('Waiting for participants...',
                  style: TextStyle(color: Colors.white)));
        }

        return _AdaptiveVideoLayout(videoViews: allParticipantViews);
      },
    );
  }

  static Widget _buildVideoContainer(
    _UIScreenState screenState, {
    required bool isLocal,
    required int uid,
    VideoViewController? controller,
  }) {
    return Container(
      key: ValueKey(isLocal ? 'container_local_$uid' : 'container_remote_$uid'),
      color: Colors.black,
      child: Stack(
        children: [
          _buildVideoView(screenState,
              isLocal: isLocal, uid: uid, controller: controller),
          _buildAudioIconOverlay(screenState, isLocal: isLocal, uid: uid),
          _buildUserNameTag(screenState, isLocal: isLocal, uid: uid),
        ],
      ),
    );
  }

  static Widget _buildVideoView(
    _UIScreenState screenState, {
    required bool isLocal,
    required int uid,
    VideoViewController? controller,
  }) {
    return ValueListenableBuilder<Map<int, bool>>(
      valueListenable: screenState._videoCallManager.remoteVideoMutedNotifier,
      builder: (context, remoteVideoStates, _) {
        return ValueListenableBuilder<bool>(
          valueListenable:
              screenState._videoCallManager.localVideoMutedNotifier,
          builder: (context, localIsMuted, _) {
            final bool videoIsMuted =
                isLocal ? localIsMuted : (remoteVideoStates[uid] ?? true);
            if (videoIsMuted) {
              final imageUrl = isLocal
                  ? screenState.widget.profilePictureUrl
                  : Config.imageBaseUrl +
                      (screenState._userProfilePictureMap[uid] ?? '');
              return Center(
                child: ClipOval(
                  child: CustomCachedNetworkImage(
                    imageUrl: imageUrl,
                    width: 100,
                    height: 100,
                    errorImage: AppConstants.profileLogoImagePath,
                  ),
                ),
              );
            } else {
              if (isLocal && screenState._engine != null) {
                return AgoraVideoView(
                  key: ValueKey('local_view_0'), // UID for local canvas is 0
                  controller: VideoViewController(
                    rtcEngine: screenState._engine!,
                    canvas: const VideoCanvas(uid: 0),
                    useFlutterTexture: false,
                    useAndroidSurfaceView: false,
                  ),
                  onAgoraVideoViewCreated: (viewId) {
                    if (!screenState
                        ._videoCallManager.localVideoMutedNotifier.value) {
                      screenState._engine?.startPreview();
                    }
                  },
                );
              } else if (!isLocal && controller != null) {
                return AgoraVideoView(
                    key: ValueKey('remote_view_$uid'), controller: controller);
              } else {
                return Container(color: Colors.grey[900]);
              }
            }
          },
        );
      },
    );
  }

  static Widget _buildAudioIconOverlay(_UIScreenState screenState,
      {required bool isLocal, required int uid}) {
    return ValueListenableBuilder<Map<int, bool>>(
      valueListenable: screenState._videoCallManager.remoteAudioMutedNotifier,
      builder: (context, remoteAudioStates, _) {
        return ValueListenableBuilder<bool>(
          valueListenable:
              screenState._videoCallManager.localAudioMutedNotifier,
          builder: (context, localIsMuted, _) {
            final bool audioIsMuted =
                isLocal ? localIsMuted : (remoteAudioStates[uid] ?? false);
            return Positioned(
              top: 5,
              right: 5,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  audioIsMuted ? Icons.mic_off : Icons.mic,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            );
          },
        );
      },
    );
  }

  static Widget _buildUserNameTag(_UIScreenState screenState,
      {required bool isLocal, required int uid}) {
    String displayName =
        isLocal ? 'You' : (screenState._userHandlesMap[uid] ?? 'User $uid');
    return Positioned(
      top: 5,
      left: 5,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
        decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            borderRadius: BorderRadius.circular(4)),
        child: Text(displayName,
            style: lbRegular.copyWith(fontSize: 10, color: Colors.white)),
      ),
    );
  }

  static Widget buildPermissionDialog(
    BuildContext dialogContext, {
    required String title,
    required String message,
    required String primaryActionText,
    required VoidCallback onPrimaryAction,
    required String secondaryActionText,
    required VoidCallback onSecondaryAction,
    bool isFeatureSpecific = false,
  }) {
    return AlertDialog(
      actionsPadding: const EdgeInsets.only(right: 10),
      insetPadding: const EdgeInsets.all(25),
      contentPadding: EdgeInsets.zero,
      backgroundColor: AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side:
            const BorderSide(color: AppConstants.popUpBorderColor, width: 1.5),
      ),
      surfaceTintColor: Colors.white,
      actions: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NetworkAwareTap(
              onTap: () => Navigator.of(dialogContext).pop(),
              child: Container(
                alignment: Alignment.centerRight,
                padding: const EdgeInsets.only(top: 10),
                child: Image.asset(AppConstants.closePopupImagePath,
                    height: 30, width: 30),
              ),
            ),
            Text(title,
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                    fontSize: 18, color: AppConstants.primaryColor)),
            const SizedBox(height: 25),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 25.0),
              child: Text(message,
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                      fontSize: 12, color: AppConstants.primaryColor)),
            ),
            const SizedBox(height: 20),
            Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: isFeatureSpecific ? 20 : 10),
              child: isFeatureSpecific
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildDialogButton(
                            dialogContext, primaryActionText, onPrimaryAction,
                            isPrimary: true, fullWidth: false),
                        _buildDialogButton(dialogContext, secondaryActionText,
                            onSecondaryAction,
                            isPrimary: false, fullWidth: false),
                      ],
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildDialogButton(
                            dialogContext, primaryActionText, onPrimaryAction,
                            isPrimary: true),
                        const SizedBox(height: 20),
                        _buildDialogButton(dialogContext, secondaryActionText,
                            onSecondaryAction,
                            isPrimary: false),
                      ],
                    ),
            ),
            const SizedBox(height: 30),
          ],
        )
      ],
    );
  }

  static Widget _buildDialogButton(
      BuildContext context, String text, VoidCallback onPressed,
      {required bool isPrimary, bool fullWidth = true}) {
    return NetworkAwareTap(
      onTap: onPressed,
      child: Container(
        width: fullWidth ? null : MediaQuery.of(context).size.width / 3,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          color: isPrimary
              ? AppConstants.textGreenColor
              : AppConstants.backgroundColor,
          border:
              isPrimary ? null : Border.all(color: AppConstants.primaryColor),
        ),
        child: Center(
            child: Text(text,
                textAlign: TextAlign.center,
                style: lbBold.copyWith(fontSize: 16))),
      ),
    );
  }

  static Widget buildDiscussionSheetPeekView(
      _UIScreenState screenState, BuildContext context, String? discussionQue) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    return GestureDetector(
      onTap: () => showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        isDismissible: true,
        showDragHandle: false,
        constraints: BoxConstraints(
            minWidth: MediaQuery.of(context).size.width,
            maxWidth: MediaQuery.of(context).size.width),
        builder: (ctx) =>
            _buildDiscussionBottomSheetContent(ctx, discussionQue, isLandscape),
      ),
      child: Container(
        height: screenState._bottomSheetPeekHeight,
        padding: const EdgeInsets.only(bottom: 10, left: 20, right: 20),
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          color: AppConstants.backgroundColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(5)),
        ),
        child: Padding(
          padding:
              isLandscape ? const EdgeInsets.only(right: 30) : EdgeInsets.zero,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Discussion Questions',
                  style: lbRegular.copyWith(fontSize: 18)),
              const SizedBox(width: 10),
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: AppConstants.textGreenColor),
                child: const Icon(Icons.keyboard_arrow_up_rounded,
                    color: AppConstants.primaryColor, size: 30),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget _buildDiscussionBottomSheetContent(
      BuildContext context, String? discussionQue, bool isLandscape) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: DraggableScrollableSheet(
        initialChildSize: 0.5,
        minChildSize: 0.2,
        maxChildSize: 0.7,
        shouldCloseOnMinExtent: false,
        builder: (ctx, scrollController) => Container(
          decoration: const BoxDecoration(
            color: AppConstants.backgroundColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 20),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2)),
                ),
                Padding(
                  padding: isLandscape
                      ? const EdgeInsets.only(right: 30)
                      : EdgeInsets.zero,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Discussion Questions',
                          style: lbRegular.copyWith(fontSize: 20)),
                      const SizedBox(width: 10),
                      GestureDetector(
                        onTap: () => Navigator.of(ctx).pop(),
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              color: AppConstants.textGreenColor),
                          child: const Icon(Icons.keyboard_arrow_down_rounded,
                              color: AppConstants.primaryColor, size: 30),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 10),
                        Text(discussionQue ?? '',
                            textAlign: TextAlign.start,
                            style: lbRegular.copyWith(fontSize: 14)),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// --- Helper Widget for Adaptive Video Layout Grid ---
class _AdaptiveVideoLayout extends StatelessWidget {
  final List<Widget> videoViews;
  const _AdaptiveVideoLayout({required this.videoViews});

  @override
  Widget build(BuildContext context) {
    int count = videoViews.length;
    final screenHeight = MediaQuery.of(context).size.height -
        (Scaffold.of(context).appBarMaxHeight ?? 0) -
        80 -
        80;
    final screenWidth = MediaQuery.of(context).size.width;

    if (count == 1) return videoViews[0];
    if (count == 2) {
      return Column(
          children: videoViews.map((v) => Expanded(child: v)).toList());
    }
    if (count == 3) {
      return Column(
        children: <Widget>[
          Expanded(flex: 3, child: videoViews[0]),
          Expanded(
            flex: 2,
            child: Row(
              children: <Widget>[
                Expanded(child: videoViews[1]),
                Expanded(child: videoViews[2])
              ],
            ),
          ),
        ],
      );
    }
    if (count == 4) {
      return GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: screenWidth / (screenHeight > 0 ? screenHeight : 1),
        ),
        itemCount: count,
        itemBuilder: (ctx, idx) => videoViews[idx],
      );
    }
    if (count == 5) {
      // Custom layout for 5
      return Column(
        children: <Widget>[
          Expanded(
            child: Row(
              children: <Widget>[
                Expanded(child: videoViews[0]),
                Expanded(child: videoViews[1])
              ],
            ),
          ),
          Expanded(
            child: Row(
              children: <Widget>[
                Expanded(child: videoViews[2]),
                Expanded(child: videoViews[3]),
                Expanded(child: videoViews[4])
              ],
            ),
          ),
        ],
      );
    }
    if (count == 6) {
      return GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: (screenWidth / 3) /
                (screenHeight > 0 ? (screenHeight / 2) : 1)),
        itemCount: count,
        itemBuilder: (ctx, idx) => videoViews[idx],
      );
    }
    // 7 or more
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: (screenWidth / 3) /
            (screenHeight > 0 ? (screenHeight / ((count + 2) ~/ 3)) : 1),
        mainAxisSpacing: 2.0,
        crossAxisSpacing: 2.0,
      ),
      itemCount: count,
      itemBuilder: (ctx, idx) => videoViews[idx],
    );
  }
}
