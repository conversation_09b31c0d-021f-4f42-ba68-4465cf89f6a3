import 'package:eljunto/views/meeting/meeting_screen.dart';
import 'package:eljunto/views/meeting/widgets/permission_dialog.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';

import 'base_video_call_mixin.dart';

mixin PermissionHandlerMixin on BaseVideoCallMixin<MeetingScreen> {
  Future<bool> checkPermission(Permission permission) async {
    final status = await permission.status;
    if (status.isDenied) {
      final result = await permission.request();
      return result.isGranted;
    }
    if (status.isPermanentlyDenied) {
      final permissionType =
          permission == Permission.camera ? 'Camera' : 'Microphone';
      await showFeaturePermissionDialog(permissionType);
      return false;
    }
    return status.isGranted;
  }

  static void showPermissionInfoDialog(
    BaseVideoCallMixin<MeetingScreen> mixin, {
    required bool isPermanentlyDeniedMic,
    required bool isPermanentlyDeniedCam,
  }) {
    if (!mixin.mounted) return;
    String message = 'Some features are limited:\n\n';
    if (isPermanentlyDeniedMic) message += '• Microphone access is disabled.\n';
    if (isPermanentlyDeniedCam) message += '• Camera access is disabled.\n';
    message += 'You can enable them in Settings.';

    showDialog(
      context: mixin.buildContext,
      builder: (ctx) => PermissionDialog(
        title: 'Limited Permissions',
        message: message,
        primaryActionText: 'Open Settings',
        onPrimaryAction: () {
          ctx.pop();
          openAppSettings();
        },
        secondaryActionText: 'Continue Anyway',
        onSecondaryAction: () => ctx.pop(),
      ),
    );
  }

  Future<void> showFeaturePermissionDialog(String permissionType) async {
    if (!mounted) return;
    await showDialog(
      context: buildContext,
      barrierColor: Colors.white38,
      builder: (ctx) => PermissionDialog(
        title: 'Enable $permissionType',
        message:
            'To use the $permissionType, please enable permission in Settings.',
        primaryActionText: 'Open Settings',
        onPrimaryAction: () {
          ctx.pop();
          openAppSettings();
        },
        secondaryActionText: 'Not now',
        onSecondaryAction: () => ctx.pop(),
        isFeatureSpecific: true,
      ),
    );
  }
}
