import 'package:eljunto/views/meeting/meeting_screen.dart';

import 'base_video_call_mixin.dart';

mixin CallManagerListenerMixin on BaseVideoCallMixin<MeetingScreen> {
  void addManagerListeners() {
    videoCallManager.callStateNotifier
        .addListener(() => _handleCallStateChange());
    videoCallManager.localAudioMutedNotifier
        .addListener(() => _updateLocalMuteStates());
    videoCallManager.localVideoMutedNotifier
        .addListener(() => _updateLocalMuteStates());
    videoCallManager.remoteUsersNotifier
        .addListener(() => _updateRemoteUsers());
    videoCallManager.remoteVideoMutedNotifier
        .addListener(() => _updateRemoteUsers());
    videoCallManager.remoteAudioMutedNotifier
        .addListener(() => _updateRemoteAudioStates());
    updateStateFromManager();
  }

  void removeManagerListeners() {
    videoCallManager.callStateNotifier
        .removeListener(() => _handleCallStateChange());
    videoCallManager.localAudioMutedNotifier
        .removeListener(() => _updateLocalMuteStates());
    videoCallManager.localVideoMutedNotifier
        .removeListener(() => _updateLocalMuteStates());
    videoCallManager.remoteUsersNotifier
        .removeListener(() => _updateRemoteUsers());
    videoCallManager.remoteVideoMutedNotifier
        .removeListener(() => _updateRemoteUsers());
    videoCallManager.remoteAudioMutedNotifier
        .removeListener(() => _updateRemoteAudioStates());
  }

  void _handleCallStateChange() {
    if (!mounted) return;
    final isActive = videoCallManager.callStateNotifier.value;
    setStateIfMounted(() {
      (widget as MeetingScreenStateAccessor).callJoinedSuccessfully = isActive;
      (widget as MeetingScreenStateAccessor).isLoading = !isActive;
      if (!isActive && engine == null) {
        (widget as MeetingScreenStateAccessor).onCallEnd(remoteInitiated: true);
      }
    });
  }

  void _updateLocalMuteStates() {
    setStateIfMounted(() {});
  }

  void _updateRemoteUsers() {
    if (!mounted) return;
    final newUids = videoCallManager.remoteUsersNotifier.value;
    (widget as MeetingScreenStateAccessor).syncScreenControllers(newUids);
    setStateIfMounted(() {});
  }

  void _updateRemoteAudioStates() {
    setStateIfMounted(() {});
  }

  void updateStateFromManager() {
    if (!mounted) return;
    (widget as MeetingScreenStateAccessor).engine = engine;
    (widget as MeetingScreenStateAccessor).callJoinedSuccessfully =
        videoCallManager.isCallActive;
    (widget as MeetingScreenStateAccessor).isLoading =
        !videoCallManager.isCallActive && engine != null;
    (widget as MeetingScreenStateAccessor)
        .syncScreenControllers(videoCallManager.remoteUsersNotifier.value);
    setStateIfMounted(() {});
  }
}
