import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:eljunto/services/video_call_manager.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

mixin BaseVideoCallMixin<T extends StatefulWidget> on State<T> {
  VideoCallManager get videoCallManager;
  RtcEngine? get engine;
  BuildContext get buildContext;

  void setStateIfMounted(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  void showErrorAndPop(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(buildContext).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
    Future.delayed(const Duration(milliseconds: 2100), () {
      if (mounted && buildContext.canPop()) {
        try {
          buildContext.pop();
        } catch (e) {
          // Ignore
        }
      }
    });
  }
}
