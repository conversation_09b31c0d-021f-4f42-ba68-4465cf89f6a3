import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:eljunto/views/meeting/meeting_screen.dart';
import 'package:flutter/foundation.dart';

import 'base_video_call_mixin.dart';

mixin VideoQualityMixin on BaseVideoCallMixin<MeetingScreen> {
  static const _lowQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 320, height: 240),
      frameRate: 15,
      bitrate: 300,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _wifilowQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 320, height: 240),
      frameRate: 15,
      bitrate: 300,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _mediumQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 640, height: 480),
      frameRate: 20,
      bitrate: 500,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _wifimediumQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 640, height: 480),
      frameRate: 20,
      bitrate: 500,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _highQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 1280, height: 720),
      frameRate: 30,
      bitrate: 800,
      orientationMode: OrientationMode.orientationModeAdaptive);
  static const _vHighQualityConfig = VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 1920, height: 1080),
      frameRate: 30,
      bitrate: 1000,
      orientationMode: OrientationMode.orientationModeAdaptive);

  void addNetworkQualityListener() {
    if (engine == null) return;
    engine!.registerEventHandler(
      RtcEngineEventHandler(
        onNetworkQuality: (connection, remoteUid, txQuality, rxQuality) async {
          if (!mounted) return;
          final List<ConnectivityResult> connectivityResult =
              await (Connectivity().checkConnectivity());
          _adjustVideoQuality(txQuality, rxQuality, connectivityResult);
        },
      ),
    );
  }

  Future<void> _adjustVideoQuality(QualityType txQuality, QualityType rxQuality,
      List<ConnectivityResult> connectivityResult) async {
    final state = widget as MeetingScreenStateAccessor;
    if (!mounted ||
        DateTime.now().difference(state.lastQualityAdjustment) <
            state.debounceInterval) {
      return;
    }

    bool isPoorNet = _isLowQuality(txQuality) || _isLowQuality(rxQuality);
    bool isGoodNet = _isGoodQuality(txQuality) && _isGoodQuality(rxQuality);

    if (isPoorNet) {
      state.poorQualityCounter++;
      state.goodQualityCounter = 0;
    } else if (isGoodNet) {
      state.goodQualityCounter++;
      state.poorQualityCounter = 0;
    } else {
      state.poorQualityCounter = 0;
    }

    // Move heavy computation to a compute function
    final configToSet = await compute(_determineVideoConfig, {
      'txQuality': txQuality.index,
      'rxQuality': rxQuality.index,
      'isWifi': connectivityResult.contains(ConnectivityResult.wifi),
      'poorCounter': state.poorQualityCounter,
      'goodCounter': state.goodQualityCounter,
    });

    if (configToSet != null && mounted) {
      await videoCallManager.setVideoEncoderConfiguration(configToSet);
      state.lastQualityAdjustment = DateTime.now();
    }
  }

  static bool _isVeryPoorQuality(QualityType q) =>
      q == QualityType.qualityBad ||
      q == QualityType.qualityVbad ||
      q == QualityType.qualityDown ||
      q == QualityType.qualityUnknown;
  static bool _isPoorQuality(QualityType q) => q == QualityType.qualityPoor;
  static bool _isGoodQuality(QualityType q) =>
      q == QualityType.qualityGood || q == QualityType.qualityExcellent;
  static bool _isLowQuality(QualityType q) =>
      _isVeryPoorQuality(q) || _isPoorQuality(q);

  // Static method for compute
  static VideoEncoderConfiguration? _determineVideoConfig(
      Map<String, dynamic> params) {
    final txQuality = QualityType.values[params['txQuality']];
    final rxQuality = QualityType.values[params['rxQuality']];
    final isWifi = params['isWifi'];
    final poorCounter = params['poorCounter'];
    final goodCounter = params['goodCounter'];

    VideoEncoderConfiguration? configToSet;

    if (_isVeryPoorQuality(txQuality) ||
        _isVeryPoorQuality(rxQuality) ||
        poorCounter >= 2) {
      configToSet = isWifi ? _wifilowQualityConfig : _lowQualityConfig;
    } else if ((_isPoorQuality(txQuality) || _isPoorQuality(rxQuality)) &&
        goodCounter < 3) {
      configToSet = isWifi ? _wifimediumQualityConfig : _mediumQualityConfig;
    } else if (goodCounter >= 3) {
      configToSet = isWifi ? _vHighQualityConfig : _highQualityConfig;
    }

    return configToSet;
  }
}
