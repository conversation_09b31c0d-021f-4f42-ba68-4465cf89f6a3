import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class PermissionDialog extends StatelessWidget {
  final String title;
  final String message;
  final String primaryActionText;
  final VoidCallback onPrimaryAction;
  final String secondaryActionText;
  final VoidCallback onSecondaryAction;
  final bool isFeatureSpecific;

  const PermissionDialog({
    super.key,
    required this.title,
    required this.message,
    required this.primaryActionText,
    required this.onPrimaryAction,
    required this.secondaryActionText,
    required this.onSecondaryAction,
    this.isFeatureSpecific = false,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      actionsPadding: const EdgeInsets.only(right: 10),
      insetPadding: const EdgeInsets.all(25),
      contentPadding: EdgeInsets.zero,
      backgroundColor: AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side:
            const BorderSide(color: AppConstants.popUpBorderColor, width: 1.5),
      ),
      surfaceTintColor: Colors.white,
      actions: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            NetworkAwareTap(
              onTap: () => context.pop(),
              child: Container(
                alignment: Alignment.centerRight,
                padding: const EdgeInsets.only(top: 10),
                child: Image.asset(AppConstants.closePopupImagePath,
                    height: 30, width: 30),
              ),
            ),
            Text(title,
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                    fontSize: 18, color: AppConstants.primaryColor)),
            const SizedBox(height: 25),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 25.0),
              child: Text(message,
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                      fontSize: 12, color: AppConstants.primaryColor)),
            ),
            const SizedBox(height: 20),
            Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: isFeatureSpecific ? 20 : 10),
              child: isFeatureSpecific
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildDialogButton(
                            context, primaryActionText, onPrimaryAction,
                            isPrimary: true, fullWidth: false),
                        _buildDialogButton(
                            context, secondaryActionText, onSecondaryAction,
                            isPrimary: false, fullWidth: false),
                      ],
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildDialogButton(
                            context, primaryActionText, onPrimaryAction,
                            isPrimary: true),
                        const SizedBox(height: 20),
                        _buildDialogButton(
                            context, secondaryActionText, onSecondaryAction,
                            isPrimary: false),
                      ],
                    ),
            ),
            const SizedBox(height: 30),
          ],
        )
      ],
    );
  }

  Widget _buildDialogButton(
      BuildContext context, String text, VoidCallback onPressed,
      {required bool isPrimary, bool fullWidth = true}) {
    return NetworkAwareTap(
      onTap: onPressed,
      child: Container(
        width: fullWidth ? null : MediaQuery.of(context).size.width / 3,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          color: isPrimary
              ? AppConstants.textGreenColor
              : AppConstants.backgroundColor,
          border:
              isPrimary ? null : Border.all(color: AppConstants.primaryColor),
        ),
        child: Center(
            child: Text(text,
                textAlign: TextAlign.center,
                style: lbBold.copyWith(fontSize: 16))),
      ),
    );
  }
}
