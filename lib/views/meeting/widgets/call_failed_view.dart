import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:flutter/material.dart';

class CallFailedView extends StatelessWidget {
  const CallFailedView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppConstants.backgroundColor,
      child: Center(
        child: Text(
          "Failed to join the call. Please try again.",
          style: lbBold.copyWith(
            color: AppConstants.redColor,
            fontSize: 18,
          ),
        ),
      ),
    );
  }
}
