import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:flutter/material.dart';

class DiscussionSheet extends StatelessWidget {
  final String? discussionQue;
  final double peekHeight;

  const DiscussionSheet({
    super.key,
    this.discussionQue,
    required this.peekHeight,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final isLandscape = size.width > size.height;
    return GestureDetector(
      onTap: () => showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        isDismissible: true,
        showDragHandle: false,
        constraints: BoxConstraints(minWidth: size.width, maxWidth: size.width),
        builder: (ctx) => _buildDiscussionBottomSheetContent(
          ctx,
          isLandscape,
        ),
      ),
      child: Container(
        height: peekHeight,
        padding: const EdgeInsets.only(bottom: 10, left: 20, right: 20),
        width: size.width,
        decoration: const BoxDecoration(
          color: AppConstants.backgroundColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(5)),
        ),
        child: Padding(
          padding:
              isLandscape ? const EdgeInsets.only(right: 30) : EdgeInsets.zero,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Discussion Questions',
                  style: lbRegular.copyWith(fontSize: 18)),
              const SizedBox(width: 10),
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: AppConstants.textGreenColor),
                child: const Icon(Icons.keyboard_arrow_up_rounded,
                    color: AppConstants.primaryColor, size: 30),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDiscussionBottomSheetContent(
      BuildContext context, bool isLandscape) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: DraggableScrollableSheet(
        initialChildSize: 0.5,
        minChildSize: 0.2,
        maxChildSize: 0.7,
        shouldCloseOnMinExtent: false,
        builder: (ctx, scrollController) => Container(
          decoration: const BoxDecoration(
            color: AppConstants.backgroundColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 20),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2)),
                ),
                Padding(
                  padding: isLandscape
                      ? const EdgeInsets.only(right: 30)
                      : EdgeInsets.zero,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Discussion Questions',
                          style: lbRegular.copyWith(fontSize: 20)),
                      const SizedBox(width: 10),
                      GestureDetector(
                        onTap: () => Navigator.of(ctx).pop(),
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              color: AppConstants.textGreenColor),
                          child: const Icon(Icons.keyboard_arrow_down_rounded,
                              color: AppConstants.primaryColor, size: 30),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 10),
                        Text(discussionQue ?? '',
                            textAlign: TextAlign.start,
                            style: lbRegular.copyWith(fontSize: 14)),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
