import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:eljunto/services/video_call_manager.dart';
import 'package:flutter/material.dart';

class VideoGridLayout extends StatelessWidget {
  final VideoCallManager videoCallManager;
  final RtcEngine? engine;
  final Map<int, VideoViewController> screenRemoteControllers;
  final Map<int, String> userHandlesMap;
  final Map<int, String> userProfilePictureMap;
  final String? profilePictureUrl;
  final bool callJoinedSuccessfully;

  const VideoGridLayout({
    super.key,
    required this.videoCallManager,
    required this.engine,
    required this.screenRemoteControllers,
    required this.userHandlesMap,
    required this.userProfilePictureMap,
    this.profilePictureUrl,
    required this.callJoinedSuccessfully,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Set<int>>(
      valueListenable: videoCallManager.remoteUsersNotifier,
      builder: (context, remoteUids, _) {
        final List<Widget> allParticipantViews = [];
        final localUid = videoCallManager.localUid;

        if (callJoinedSuccessfully && engine != null && localUid != null) {
          allParticipantViews.add(
            _buildVideoContainer(isLocal: true, uid: localUid),
          );
        }

        for (int uid in remoteUids) {
          final controller = screenRemoteControllers[uid];
          if (controller != null) {
            allParticipantViews.add(
              _buildVideoContainer(
                  isLocal: false, uid: uid, controller: controller),
            );
          }
        }

        if (allParticipantViews.isEmpty) {
          return const Center(
              child: Text('Waiting for participants...',
                  style: TextStyle(color: Colors.white)));
        }

        return _AdaptiveVideoLayout(videoViews: allParticipantViews);
      },
    );
  }

  Widget _buildVideoContainer({
    required bool isLocal,
    required int uid,
    VideoViewController? controller,
  }) {
    return Container(
      key: ValueKey(isLocal ? 'container_local_$uid' : 'container_remote_$uid'),
      color: Colors.black,
      child: Stack(
        children: [
          _buildVideoView(isLocal: isLocal, uid: uid, controller: controller),
          _buildAudioIconOverlay(isLocal: isLocal, uid: uid),
          _buildUserNameTag(isLocal: isLocal, uid: uid),
        ],
      ),
    );
  }

  Widget _buildVideoView({
    required bool isLocal,
    required int uid,
    VideoViewController? controller,
  }) {
    return ValueListenableBuilder<Map<int, bool>>(
      valueListenable: videoCallManager.remoteVideoMutedNotifier,
      builder: (context, remoteVideoStates, _) {
        return ValueListenableBuilder<bool>(
          valueListenable: videoCallManager.localVideoMutedNotifier,
          builder: (context, localIsMuted, _) {
            final bool videoIsMuted =
                isLocal ? localIsMuted : (remoteVideoStates[uid] ?? true);
            if (videoIsMuted) {
              final imageUrl = isLocal
                  ? profilePictureUrl
                  : Config.imageBaseUrl + (userProfilePictureMap[uid] ?? '');
              return Center(
                child: ClipOval(
                  child: CustomCachedNetworkImage(
                    imageUrl: imageUrl,
                    width: 100,
                    height: 100,
                    errorImage: AppConstants.profileLogoImagePath,
                  ),
                ),
              );
            } else {
              if (isLocal && engine != null) {
                final localController = VideoViewController(
                  rtcEngine: engine!,
                  canvas: const VideoCanvas(uid: 0),
                  useFlutterTexture: false,
                  useAndroidSurfaceView: false,
                );
                return AgoraVideoView(
                  key: ValueKey('local_view_0'),
                  controller: localController,
                  onAgoraVideoViewCreated: (viewId) {
                    if (!videoCallManager.localVideoMutedNotifier.value) {
                      engine?.startPreview();
                    }
                  },
                );
              } else if (!isLocal && controller != null) {
                return AgoraVideoView(
                    key: ValueKey('remote_view_$uid'), controller: controller);
              } else {
                return Container(color: Colors.grey[900]);
              }
            }
          },
        );
      },
    );
  }

  Widget _buildAudioIconOverlay({required bool isLocal, required int uid}) {
    return ValueListenableBuilder<Map<int, bool>>(
      valueListenable: videoCallManager.remoteAudioMutedNotifier,
      builder: (context, remoteAudioStates, _) {
        return ValueListenableBuilder<bool>(
          valueListenable: videoCallManager.localAudioMutedNotifier,
          builder: (context, localIsMuted, _) {
            final bool audioIsMuted =
                isLocal ? localIsMuted : (remoteAudioStates[uid] ?? true);
            return Positioned(
              top: 5,
              right: 5,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  audioIsMuted ? Icons.mic_off : Icons.mic,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildUserNameTag({required bool isLocal, required int uid}) {
    String displayName = isLocal ? 'You' : (userHandlesMap[uid] ?? 'User $uid');
    return Positioned(
      top: 5,
      left: 5,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
        decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            borderRadius: BorderRadius.circular(4)),
        child: Text(displayName,
            style: lbRegular.copyWith(fontSize: 10, color: Colors.white)),
      ),
    );
  }
}

class _AdaptiveVideoLayout extends StatelessWidget {
  final List<Widget> videoViews;
  const _AdaptiveVideoLayout({required this.videoViews});

  @override
  Widget build(BuildContext context) {
    int count = videoViews.length;
    final screenHeight = MediaQuery.of(context).size.height -
        (Scaffold.of(context).appBarMaxHeight ?? 0) -
        80 -
        80;
    final screenWidth = MediaQuery.of(context).size.width;

    if (count == 1) return videoViews[0];
    if (count == 2) {
      return Column(
          children: videoViews.map((v) => Expanded(child: v)).toList());
    }
    if (count == 3) {
      return Column(
        children: <Widget>[
          Expanded(flex: 3, child: videoViews[0]),
          Expanded(
            flex: 2,
            child: Row(
              children: <Widget>[
                Expanded(child: videoViews[1]),
                Expanded(child: videoViews[2])
              ],
            ),
          ),
        ],
      );
    }
    if (count == 4) {
      return GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: screenWidth / (screenHeight > 0 ? screenHeight : 1),
        ),
        itemCount: count,
        itemBuilder: (ctx, idx) => videoViews[idx],
      );
    }
    if (count == 5) {
      return Column(
        children: <Widget>[
          Expanded(
            child: Row(
              children: <Widget>[
                Expanded(child: videoViews[0]),
                Expanded(child: videoViews[1])
              ],
            ),
          ),
          Expanded(
            child: Row(
              children: <Widget>[
                Expanded(child: videoViews[2]),
                Expanded(child: videoViews[3]),
                Expanded(child: videoViews[4])
              ],
            ),
          ),
        ],
      );
    }
    if (count == 6) {
      return GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: (screenWidth / 3) /
                (screenHeight > 0 ? (screenHeight / 2) : 1)),
        itemCount: count,
        itemBuilder: (ctx, idx) => videoViews[idx],
      );
    }
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: (screenWidth / 3) /
            (screenHeight > 0 ? (screenHeight / ((count + 2) ~/ 3)) : 1),
        mainAxisSpacing: 2.0,
        crossAxisSpacing: 2.0,
      ),
      itemCount: count,
      itemBuilder: (ctx, idx) => videoViews[idx],
    );
  }
}
