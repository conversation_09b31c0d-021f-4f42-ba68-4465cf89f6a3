import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/services/video_call_manager.dart';
import 'package:flutter/material.dart';

class ControlPanel extends StatelessWidget {
  final VideoCallManager videoCallManager;
  final VoidCallback onToggleAudio;
  final VoidCallback onToggleVideo;
  final VoidCallback onToggleCamera;
  final VoidCallback onEndCall;
  final VoidCallback onTogglePipMode;
  final double height;

  const ControlPanel({
    super.key,
    required this.videoCallManager,
    required this.onToggleAudio,
    required this.onToggleVideo,
    required this.onToggleCamera,
    required this.onEndCall,
    required this.onTogglePipMode,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      color: Colors.black.withOpacity(0.5),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 6),
        margin: const EdgeInsets.symmetric(vertical: 6),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: ValueListenableBuilder<bool>(
                valueListenable: videoCallManager.localAudioMutedNotifier,
                builder: (ctx, isMuted, _) => RawMaterialButton(
                  onPressed: onToggleAudio,
                  shape: const CircleBorder(),
                  padding: const EdgeInsets.all(20),
                  visualDensity: VisualDensity.compact,
                  fillColor: isMuted
                      ? AppConstants.redColor
                      : AppConstants.primaryColor,
                  child: Icon(
                    isMuted ? Icons.mic_off : Icons.mic,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
            Expanded(
              child: RawMaterialButton(
                onPressed: onEndCall,
                shape: const CircleBorder(),
                padding: const EdgeInsets.all(15),
                fillColor: AppConstants.redColor,
                child: const Icon(
                  Icons.call_end,
                  color: Colors.white,
                  size: 25,
                ),
              ),
            ),
            Expanded(
              child: ValueListenableBuilder<bool>(
                valueListenable: videoCallManager.localVideoMutedNotifier,
                builder: (ctx, isMuted, _) => RawMaterialButton(
                  onPressed: onToggleVideo,
                  shape: const CircleBorder(),
                  padding: const EdgeInsets.all(20),
                  visualDensity: VisualDensity.compact,
                  fillColor: isMuted
                      ? AppConstants.redColor
                      : AppConstants.primaryColor,
                  child: Icon(
                    isMuted ? Icons.videocam_off : Icons.videocam,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
            Expanded(
              child: RawMaterialButton(
                onPressed: onToggleCamera,
                shape: const CircleBorder(),
                visualDensity: VisualDensity.compact,
                padding: const EdgeInsets.all(20),
                fillColor: AppConstants.primaryColor,
                child: const Icon(
                  Icons.cameraswitch_outlined,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
            Expanded(
              child: RawMaterialButton(
                onPressed: onTogglePipMode,
                shape: const CircleBorder(),
                padding: const EdgeInsets.all(20),
                visualDensity: VisualDensity.compact,
                fillColor: AppConstants.primaryColor,
                child: const Icon(
                  Icons.picture_in_picture_alt,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
