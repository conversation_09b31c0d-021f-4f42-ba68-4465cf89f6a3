import 'dart:async';

import 'package:eljunto/services/overlay_service.dart';
import 'package:eljunto/services/video_call_manager.dart';
import 'package:eljunto/views/meeting/widgets/picture_in_picture/pip_animated_controls.dart';
import 'package:eljunto/views/meeting/widgets/picture_in_picture/pip_main_video_view.dart';
import 'package:eljunto/views/meeting/widgets/picture_in_picture/pip_remote_preview.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class PipWidget extends StatefulWidget {
  final Offset initialOffset;
  final bool initialControlsVisible;
  final int initialRemoteUserIndex;
  final List<int> initialActiveRemoteUids;
  final VideoCallManager videoCallManager;
  final GlobalKey<NavigatorState> navigatorKey;
  final Function(DragUpdateDetails) onPanUpdate;
  final VoidCallback onHideOverlay;

  const PipWidget({
    super.key,
    required this.initialOffset,
    required this.initialControlsVisible,
    required this.initialRemoteUserIndex,
    required this.initialActiveRemoteUids,
    required this.videoCallManager,
    required this.navigator<PERSON><PERSON>,
    required this.onPanUpdate,
    required this.onHideOverlay,
  });

  @override
  State<PipWidget> createState() => PipWidgetState();
}

class PipWidgetState extends State<PipWidget> {
  late bool _pipControlsVisible;
  Timer? _pipAutoHideTimer;
  late int _currentRemoteUserIndex;

  @override
  void initState() {
    super.initState();
    _currentRemoteUserIndex = widget.initialRemoteUserIndex;
    _pipControlsVisible = widget.initialControlsVisible;
    _resetAutoHideTimer();

    // Listen for deep link navigation events
    _listenForDeepLinkNavigation();
  }

  void _listenForDeepLinkNavigation() =>
      widget.videoCallManager.setPipModeActive(true);

  @override
  void dispose() {
    _pipAutoHideTimer?.cancel();
    widget.videoCallManager.setPipModeActive(false);
    super.dispose();
  }

  void _togglePipControls() {
    if (!mounted) return;
    setState(() => _pipControlsVisible = !_pipControlsVisible);
    _pipAutoHideTimer?.cancel();
    if (_pipControlsVisible) {
      _pipAutoHideTimer = Timer(const Duration(seconds: 5), () {
        if (mounted && _pipControlsVisible) {
          setState(() => _pipControlsVisible = false);
        }
      });
    }
  }

  void _cycleRemoteUser(List<int> activeRemoteUids) {
    if (!mounted) return;
    if (activeRemoteUids.length > 1) {
      setState(
        () => _currentRemoteUserIndex =
            (_currentRemoteUserIndex + 1) % activeRemoteUids.length,
      );
      _resetAutoHideTimer(makeControlsVisible: true);
    } else if (activeRemoteUids.length == 1 && !_pipControlsVisible) {
      _resetAutoHideTimer(makeControlsVisible: true);
    }
  }

  void _resetAutoHideTimer({bool makeControlsVisible = false}) {
    if (!mounted) return;
    if (makeControlsVisible && !_pipControlsVisible) {
      setState(() => _pipControlsVisible = true);
    }
    _pipAutoHideTimer?.cancel();
    if (_pipControlsVisible || makeControlsVisible) {
      _pipAutoHideTimer = Timer(const Duration(seconds: 5), () {
        if (mounted && _pipControlsVisible) {
          setState(() => _pipControlsVisible = false);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Set<int>>(
        valueListenable: widget.videoCallManager.remoteUsersNotifier,
        builder: (context, remoteUidsSet, _) {
          final activeRemoteUids = remoteUidsSet.toList();

          if (activeRemoteUids.isNotEmpty) {
            if (_currentRemoteUserIndex >= activeRemoteUids.length ||
                _currentRemoteUserIndex < 0) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  setState(() => _currentRemoteUserIndex = 0);
                }
              });
            }
          } else if (_currentRemoteUserIndex != -1) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() => _currentRemoteUserIndex = -1);
              }
            });
          }

          if (widget.videoCallManager.engine == null ||
              !widget.videoCallManager.isCallActive) {
            return SizedBox.shrink();
          }

          double currentPipWidth = _pipControlsVisible
              ? OverlayService.pipExpandedWidth
              : OverlayService.pipBaseWidth;
          double currentPipHeight = _pipControlsVisible
              ? OverlayService.pipExpandedHeight
              : OverlayService.pipBaseHeight;

          int? remoteUidToShow;
          if (activeRemoteUids.isNotEmpty &&
              _currentRemoteUserIndex >= 0 &&
              _currentRemoteUserIndex < activeRemoteUids.length) {
            remoteUidToShow = activeRemoteUids[_currentRemoteUserIndex];
          }

          return Positioned(
            top: widget.initialOffset.dy,
            left: widget.initialOffset.dx,
            child: GestureDetector(
              onPanUpdate: widget.onPanUpdate,
              onTap: _togglePipControls,
              child: Material(
                color: Colors.transparent,
                elevation: 8.0,
                borderRadius: BorderRadius.circular(12),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  width: currentPipWidth,
                  height: currentPipHeight,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.white38, width: 1),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(11),
                    child: Stack(
                      children: [
                        PipMainVideoView(manager: widget.videoCallManager),
                        if (remoteUidToShow != null)
                          PipRemotePreview(
                            manager: widget.videoCallManager,
                            remoteUid: remoteUidToShow,
                            currentPipWidth: currentPipWidth,
                            currentPipHeight: currentPipHeight,
                            onCycle: () => _cycleRemoteUser(
                              activeRemoteUids,
                            ),
                          ),
                        if (_pipControlsVisible)
                          Container(color: Colors.black.withOpacity(0.2)),
                        PipAnimatedControls(
                          pipControlsVisible: _pipControlsVisible,
                          videoCallManager: widget.videoCallManager,
                          onExpand: () {
                            widget.onHideOverlay();
                            final metadata =
                                widget.videoCallManager.getCallMetadata();
                            if (metadata != null) {
                              widget.navigatorKey.currentContext?.pushNamed(
                                'MeetingScreen',
                                extra: metadata,
                              );
                            }
                          },
                          onEndCall: () {
                            widget.onHideOverlay();
                            widget.videoCallManager.endCall();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }
}
