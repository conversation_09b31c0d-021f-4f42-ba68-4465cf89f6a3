import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:eljunto/services/video_call_manager.dart';
import 'package:flutter/material.dart';

class PipVideoPlayer extends StatelessWidget {
  final RtcEngine engine;
  final int uid;
  final String? channelId;
  final bool isRemote;
  final VideoCallManager videoCallManager;

  const PipVideoPlayer({
    super.key,
    required this.engine,
    required this.uid,
    this.channelId,
    required this.isRemote,
    required this.videoCallManager,
  });

  @override
  Widget build(BuildContext context) {
    if (videoCallManager.engine == null) {
      return const SizedBox.shrink();
    }
    final controller = videoCallManager.getOrCreateController(
      uid,
      isRemote: isRemote,
      channelId: channelId,
    );
    return AgoraVideoView(controller: controller);
  }
}
