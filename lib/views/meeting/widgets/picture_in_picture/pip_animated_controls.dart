import 'package:eljunto/services/overlay_service.dart';
import 'package:eljunto/services/video_call_manager.dart';
import 'package:flutter/material.dart';

class PipAnimatedControls extends StatelessWidget {
  final bool pipControlsVisible;
  final VideoCallManager videoCallManager;
  final VoidCallback onExpand;
  final VoidCallback onEndCall;

  const PipAnimatedControls({
    super.key,
    required this.pipControlsVisible,
    required this.videoCallManager,
    required this.onExpand,
    required this.onEndCall,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: pipControlsVisible ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 200),
      child: Visibility(
        visible: pipControlsVisible,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildCallEndControl(onEndCall: onEndCall),
            _buildCallExpandControl(onExpand: onExpand),
            _buildMediaControls(videoCallManager: videoCallManager),
          ],
        ),
      ),
    );
  }

  Widget _buildCallEndControl({required VoidCallback onEndCall}) {
    return Container(
      alignment: Alignment.centerRight,
      height: OverlayService.pipHeaderHeight,
      color: Colors.black.withOpacity(0.4),
      child: IconButton(
        visualDensity: VisualDensity.compact,
        tooltip: "End Call",
        icon: Icon(Icons.close, color: Colors.white, size: 20),
        onPressed: onEndCall,
      ),
    );
  }

  Widget _buildCallExpandControl({required VoidCallback onExpand}) {
    return Container(
      alignment: Alignment.center,
      height: OverlayService.pipHeaderHeight,
      color: Colors.black.withOpacity(0.4),
      child: IconButton(
        visualDensity: VisualDensity.compact,
        tooltip: "Expand Call",
        icon: Icon(
          Icons.fullscreen_outlined,
          color: Colors.white,
          size: 30,
        ),
        onPressed: onExpand,
      ),
    );
  }

  Widget _buildMediaControls({required VideoCallManager videoCallManager}) {
    return Container(
      height: OverlayService.pipMediaControlsHeight,
      color: Colors.black.withOpacity(0.4),
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ValueListenableBuilder<bool>(
            valueListenable: videoCallManager.localAudioMutedNotifier,
            builder: (context, isMuted, _) => IconButton(
              visualDensity: VisualDensity.compact,
              tooltip: isMuted ? "Unmute Mic" : "Mute Mic",
              iconSize: 22,
              onPressed: videoCallManager.toggleAudio,
              icon: Icon(
                isMuted ? Icons.mic_off : Icons.mic,
                color: Colors.white,
              ),
            ),
          ),
          ValueListenableBuilder<bool>(
            valueListenable: videoCallManager.localVideoMutedNotifier,
            builder: (context, isMuted, _) => IconButton(
              visualDensity: VisualDensity.compact,
              tooltip: isMuted ? "Start Video" : "Stop Video",
              iconSize: 22,
              onPressed: videoCallManager.toggleVideo,
              icon: Icon(
                isMuted ? Icons.videocam_off : Icons.videocam,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
