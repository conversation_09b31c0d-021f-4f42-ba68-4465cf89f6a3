import 'package:eljunto/services/video_call_manager.dart';
import 'package:eljunto/views/meeting/widgets/picture_in_picture/pip_video_player.dart';
import 'package:flutter/material.dart';

class PipMainVideoView extends StatelessWidget {
  final VideoCallManager manager;

  const PipMainVideoView({
    super.key,
    required this.manager,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: manager.localVideoMutedNotifier,
      builder: (context, isMuted, _) {
        if (manager.engine == null) return _pipPlaceholder(icon: Icons.error);
        return isMuted
            ? _pipPlaceholder(icon: Icons.person_off_outlined, size: 50)
            : PipVideoPlayer(
                key: ValueKey('pip_local_${manager.localUid ?? 0}'),
                engine: manager.engine!,
                uid: 0,
                channelId: null,
                isRemote: false,
                videoCallManager: manager,
              );
      },
    );
  }

  Widget _pipPlaceholder({required IconData icon, double size = 30}) => Center(
        child: Icon(
          icon,
          color: Colors.grey[600],
          size: size,
        ),
      );
}
