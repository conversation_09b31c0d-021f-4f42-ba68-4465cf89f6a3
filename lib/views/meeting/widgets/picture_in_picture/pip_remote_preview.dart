import 'package:eljunto/services/video_call_manager.dart';
import 'package:eljunto/views/meeting/widgets/picture_in_picture/pip_video_player.dart';
import 'package:flutter/material.dart';

class PipRemotePreview extends StatelessWidget {
  final VideoCallManager manager;
  final int remoteUid;
  final double currentPipWidth;
  final double currentPipHeight;
  final VoidCallback onCycle;

  const PipRemotePreview({
    super.key,
    required this.manager,
    required this.remoteUid,
    required this.currentPipWidth,
    required this.currentPipHeight,
    required this.onCycle,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 5,
      right: 5,
      child: GestureDetector(
        onTap: onCycle,
        child: Container(
          width: currentPipWidth * 0.35,
          height: currentPipHeight * 0.35,
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.white24),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(7),
            child: ValueListenableBuilder<Map<int, bool>>(
              valueListenable: manager.remoteVideoMutedNotifier,
              builder: (context, videoStates, _) {
                if (manager.engine == null) {
                  return _pipPlaceholder(
                    icon: Icons.error_outline,
                    size: 15,
                  );
                }
                bool isRemoteVideoMuted = videoStates[remoteUid] ?? true;
                return isRemoteVideoMuted
                    ? _pipPlaceholder(
                        icon: Icons.videocam_off_outlined, size: 20)
                    : PipVideoPlayer(
                        key: ValueKey('pip_remote_$remoteUid'),
                        engine: manager.engine!,
                        uid: remoteUid,
                        channelId:
                            manager.getCallMetadata()?['channelName'] ?? "",
                        isRemote: true,
                        videoCallManager: manager,
                      );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _pipPlaceholder({required IconData icon, double size = 30}) => Center(
        child: Icon(
          icon,
          color: Colors.grey[600],
          size: size,
        ),
      );
}
