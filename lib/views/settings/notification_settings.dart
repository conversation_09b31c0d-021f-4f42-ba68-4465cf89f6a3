import 'dart:developer';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/models/notification_settings_model/notification_settings_model.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_switch/flutter_advanced_switch.dart';
import 'package:provider/provider.dart';

import '../../constants/common_helper.dart';

int? loginUserId;

Map<int, String> notificationTypeTitles = {
  1: 'New Club Meeting',
  2: 'Meeting Time Change',
  3: 'Club Meeting Reminders',
  4: 'New Invitations',
  5: 'Club Requests\n(For Club Leaders)',
  6: 'New Club Messages',
};

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  List<NotificationSetting> _settings = [
    /*  NotificationSetting('New Club Meeting', true, true, true, true),
    NotificationSetting('Meeting Time Change', true, true, true, true),
    NotificationSetting('Club Meeting Reminders', true, true, true, true),
    NotificationSetting('New Invitations', true, true, true, true),
    NotificationSetting(
        'Club Requests\n(For Club Leaders)', true, true, true, true),
    NotificationSetting('New Club Messages', false, true, true, true), */
  ];
  /* Map<int, String> notificationTypeTitles = {
    1: 'New Club Meeting',
    2: 'Meeting Time Change',
    3: 'Club Meeting Reminders',
    4: 'New Invitations',
    5: 'Club Requests\n(For Club Leaders)',
    6: 'New Club Messages',
  }; */
  @override
  void initState() {
    localData();
    super.initState();
  }

  Future<void> localData() async {
    loginUserId = await CommonHelper.getLoggedInUserId();
    print("Mail : $loginUserId");
    getNotificationSettings();
  }

  Future<void> getNotificationSettings() async {
    try {
      final responseMap =
          await Provider.of<UserController>(context, listen: false)
              .getNotificationSettings(loginUserId ?? 0, context);
      if (responseMap.containsKey('error')) {
        log(responseMap['error']);
      } else {
        // print("responseMap : $responseMap");
        setState(() {
          _settings = parseNotificationSettings(responseMap);
        });
        print("_settings : $_settings");
      }
    } catch (e) {
      log('An error occurred: $e');
    }
  }

  List<NotificationSetting> parseNotificationSettings(dynamic response) {
    List<NotificationSetting> settings = [];

    for (var item in response['data']) {
      int type = item['notificationType'];
      String title = notificationTypeTitles[type] ?? 'Unknown Notification';
      bool email = item['emailNotification'] == 1;
      bool os = item['osNotification'] == 1;

      settings.add(NotificationSetting(title, email, os, true, true));
    }

    return settings;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: Color.fromRGBO(37, 57, 67, 1),
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: 'Notification Settings',
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                AppConstants.bgImagePath,
              ),
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
            ),
          ),
          child: Column(
            children: [
              const SizedBox(
                height: 25,
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    const Spacer(),
                    Text(
                      'Email',
                      style: lbRegular.copyWith(fontSize: 14),
                    ),
                    const SizedBox(
                      width: 80,
                    ),
                    Text(
                      'OS',
                      style: lbRegular.copyWith(fontSize: 14),
                    ),
                    const SizedBox(
                      width: 30,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: _settings.length,
                  itemBuilder: (context, index) {
                    return NotificationSettingRow(
                      setting: _settings[index],
                      index: index,
                    );
                  },
                ),
              ),
            ],
          )),
    );
  }
}

class NotificationSettingRow extends StatelessWidget {
  final NotificationSetting setting;
  final int? index;
  // final int? userId;

  const NotificationSettingRow({
    super.key,
    required this.setting,
    this.index,
    // this.userId,
  });

  int? getNotificationTypeKey(String title) {
    return notificationTypeTitles.entries
        .firstWhere((entry) => entry.value == title,
            orElse: () => MapEntry(-1, ''))
        .key;
  }

  Future<void> _handleUpdateNotificationSetting(
      Map<String, dynamic> payload, context) async {
    await Provider.of<UserController>(context, listen: false)
        .controlNotification(payload)
        .then((value) {
      if (value) {
        log("Notification value update successful");
      }
    });
  }

  Map<String, dynamic> createNotificationPayload(
      int userId, String notificationTypeKey, bool emailValue, bool osValue) {
    return {
      "userId": userId,
      "notificationTypeKey": notificationTypeKey,
      "emailNotification": emailValue ? 1 : 0,
      "osNotification": osValue ? 1 : 0,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end, // spaceBetween
        children: [
          Expanded(
            child: Text(
              setting.title,
              style: lbRegular.copyWith(fontSize: 14),
            ),
          ),
          if (setting.showEmailController &&
              setting.title != 'New Club Messages') ...[
            Expanded(
              child: Align(
                alignment: Alignment.center,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border: Border.all(color: AppConstants.primaryColor),
                  ),
                  child: AdvancedSwitch(
                    controller: setting.emailController,
                    width: 75,
                    height: 35,
                    initialValue: setting.emailController.value,
                    thumb: ValueListenableBuilder(
                      valueListenable: setting.emailController,
                      builder: (context, val, _) {
                        return Container(
                          height: 10,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border:
                                Border.all(color: AppConstants.primaryColor),
                            borderRadius: BorderRadius.circular(50),
                          ),
                        );
                      },
                    ),
                    borderRadius: BorderRadius.circular(20),
                    activeChild: Text(
                      "ON",
                      style: lbBold.copyWith(fontSize: 12),
                    ),
                    inactiveChild: Text(
                      "OFF",
                      style: lbBold.copyWith(fontSize: 12),
                    ),
                    activeColor: AppConstants.textGreenColor,
                    inactiveColor: Colors.transparent,
                    onChanged: (value) {
                      final notificationTypeKey =
                          getNotificationTypeKey(setting.title);
                      if (notificationTypeKey != -1) {
                        final payload = createNotificationPayload(
                          loginUserId ?? 0,
                          notificationTypeKey.toString(),
                          value,
                          setting.osController.value,
                        );
                        _handleUpdateNotificationSetting(payload, context);
                      } else {
                        log('Invalid notification title: ${setting.title}');
                      }
                    },
                  ),
                ),
              ),
            ),
          ],
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              border: Border.all(color: AppConstants.primaryColor),
            ),
            child: AdvancedSwitch(
              controller: setting.osController,
              width: 75,
              height: 35,
              initialValue: setting.osController.value,
              thumb: ValueListenableBuilder(
                valueListenable: setting.osController,
                builder: (context, val, _) {
                  return Container(
                    height: 10,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: AppConstants.primaryColor),
                      borderRadius: BorderRadius.circular(50),
                    ),
                  );
                },
              ),
              borderRadius: BorderRadius.circular(20),
              activeChild: Text(
                "ON",
                style: lbBold.copyWith(fontSize: 12),
              ),
              inactiveChild: Text(
                "OFF",
                style: lbBold.copyWith(fontSize: 12),
              ),
              activeColor: AppConstants.textGreenColor,
              inactiveColor: Colors.transparent,
              onChanged: (value) {
                final notificationTypeKey =
                    getNotificationTypeKey(setting.title);
                if (notificationTypeKey != -1) {
                  final payload = createNotificationPayload(
                    loginUserId ?? 0,
                    notificationTypeKey.toString(),
                    setting.emailController.value,
                    value,
                  );
                  _handleUpdateNotificationSetting(payload, context);
                } else {
                  log('Invalid notification title: ${setting.title}');
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
