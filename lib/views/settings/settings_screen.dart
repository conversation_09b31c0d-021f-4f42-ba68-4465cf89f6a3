import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/controller/login_controller.dart';
import 'package:eljunto/reusableWidgets/appbar.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/settings_item.dart';
import 'package:eljunto/reusableWidgets/version_display.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../constants/text_style.dart';
import '../../reusableWidgets/custom_button.dart';

class SettingScreen extends StatefulWidget {
  const SettingScreen({super.key});

  @override
  State<SettingScreen> createState() => _SettingScreenState();
}

class _SettingScreenState extends State<SettingScreen> {
  String? userMailId;
  String? appVersion, appName;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  Future<void> localData() async {
    userMailId = await CommonHelper.getLoggedinUserMail();
    print("Mail : $userMailId");
  }

  Future<bool> logOutFunction() async {
    bool value = await Provider.of<LoginController>(context, listen: false)
        .logOutFunction(userMailId ?? '');
    return value;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const AppBarWidget(appBarText: 'Settings'),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: FutureBuilder(
            future: localData(),
            builder: (context, snapShot) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 25,
                      ),
                      SettingsItem(
                        label: 'Change Password',
                        onTap: () {
                          context.pushNamed('change-password');
                        },
                      ),
                      SettingsItem(
                        label: 'Change Email',
                        onTap: () {
                          context.pushNamed('manage-email');
                        },
                      ),
                      SettingsItem(
                        label: 'Notification Settings',
                        onTap: () {
                          context.pushNamed('notification-settings');
                        },
                      ),
                      // SettingsItem(
                      //   label: 'Manage Block List',
                      //   onTap: () {
                      //     context.pushNamed('block-list');
                      //   },
                      // ),
                      SettingsItem(
                        label: 'Privacy Policy',
                        onTap: () async =>
                            await launchUrl(AppConstants.privacyPolicyUrl),
                      ),
                      SettingsItem(
                        label: 'Terms of Service',
                        onTap: () async =>
                            await launchUrl(AppConstants.termsAndConditionUrl),
                      ),
                      SettingsItem(
                        label: 'Manage Subscription',
                        onTap: () {
                          context.pushNamed('manage-subscription');
                        },
                      ),
                      SettingsItem(
                        label: 'Logout',
                        onTap: handleLogoutAccount,
                      ),
                      SettingsItem(
                        label: 'Delete Account',
                        onTap: () {
                          context.pushNamed('delete-account');
                        },
                        textColor: AppConstants.redColor,
                      ),
                      const VersionDisplay(),
                      const SizedBox(height: 25),
                    ],
                  ),
                ),
              );
            }),
      ),
    );
  }

  handleLogoutAccount() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.symmetric(horizontal: 20),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Column(
                children: [
                  NetworkAwareTap(
                    onTap: () {
                      context.pop();
                    },
                    child: Container(
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(top: 10),
                      child: Image.asset(
                        AppConstants.closePopupImagePath,
                        height: 30,
                        width: 30,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 30.0, right: 20),
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: Text(
                        "Are you sure you want to logout?",
                        textAlign: TextAlign.center,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 30.0, right: 20),
                    child: Row(
                      mainAxisAlignment: isLoading
                          ? MainAxisAlignment.center
                          : MainAxisAlignment.spaceBetween,
                      children: [
                        CustomLoaderButton(
                          buttonWidth: isLoading
                              ? 45.0
                              : MediaQuery.of(context).size.width / 3.2,
                          buttonRadius: 30.0,
                          buttonChild: isLoading
                              ? const CircularProgressIndicator(
                                  valueColor:
                                      AlwaysStoppedAnimation(Colors.white),
                                  strokeWidth: 3.0,
                                )
                              : Text(
                                  'Logout',
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                          buttonPressed: () async {
                            Map<String, dynamic> payload = {};
                            setState(() {
                              isLoading = true;
                            });
                            if (context.mounted) {
                              await CommonHelper()
                                  .userLogoutFunction(context, payload);
                            }

                            setState(() {
                              isLoading = false;
                            });
                          },
                        ),
                        !isLoading
                            ? NetworkAwareTap(
                                onTap: () {
                                  context.pop();
                                },
                                child: Container(
                                  height: 45,
                                  width:
                                      MediaQuery.of(context).size.width / 3.2,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(49),
                                    color: AppConstants.backgroundColor,
                                    border: Border.all(
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      "Cancel",
                                      textAlign: TextAlign.center,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              ),
            ],
          );
        });
      },
    );
  }
}
