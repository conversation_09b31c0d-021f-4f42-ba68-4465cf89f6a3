import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/generic_messages.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/login_controller.dart';
import 'package:eljunto/controller/profile_controller.dart';
import 'package:eljunto/models/profile_model/edit_profile/update_user_profile_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/custom_text_widget.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ManageEmailPage extends StatefulWidget {
  const ManageEmailPage({super.key});

  @override
  State createState() => _ManageEmailPageState();
}

class _ManageEmailPageState extends State<ManageEmailPage> {
  final _emailFormKey = GlobalKey<FormState>();
  final _otpFormKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _otpController = TextEditingController();
  String? _loggedInEmail;
  String errorMessage = '';
  final List<String> _existingEmails = ['<EMAIL>'];
  int? removeEmailIndex = 0;
  bool isMailAlreadyExit = false;

  bool checkMail = false;
  bool checkMailValidation = false;
  bool checkOtp = false;
  bool checkOtpValidation = false;
  Future<void> _initializeUserEmail() async {
    _loggedInEmail = await CommonHelper.getLoggedinUserMail();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: 'Change Email',
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: FutureBuilder(
          future: _initializeUserEmail(),
          builder: (context, snapShot) {
            return Skeletonizer(
                effect: const SoldColorEffect(
                  color: AppConstants.skeletonforgroundColor,
                  lowerBound: 0.1,
                  upperBound: 0.5,
                ),
                containersColor: AppConstants.skeletonBackgroundColor,
                enabled: snapShot.connectionState == ConnectionState.waiting,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(25.0),
                  child: Column(children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          'Email',
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    SizedBox(
                      child: TextFormField(
                        initialValue: _loggedInEmail,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                        readOnly: true,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.all(10),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5.0),
                            borderSide: const BorderSide(
                                color: AppConstants.primaryColor, width: 2.5),
                          ),
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ),
                    const SizedBox(height: 25),
                    CustomButton(
                      text: 'Change Email',
                      onPressed: _submitForm,
                    ),
                  ]),
                ));
          },
        ),
      ),
    );
  }

  void _submitForm() {
    showAddEmailPopup();
  }

  void showAddEmailPopup() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        _emailController.text = '';
        checkMail = false;
        checkMailValidation = false;
        isMailAlreadyExit = false;
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.symmetric(horizontal: 25),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Form(
                key: _emailFormKey,
                child: Column(
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(top: 10),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      child: Center(
                        child: Text(
                          "Change Email",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: TextFormField(
                        controller: _emailController,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                        keyboardType: TextInputType.emailAddress,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.all(10),
                          border: const OutlineInputBorder(),
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.8),
                          errorStyle: errorMsg,
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Row(
                        children: [
                          checkMail
                              ? Text(
                                  '*Enter your email',
                                  style: lbBold.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              : const SizedBox.shrink(),
                          checkMailValidation
                              ? Text(
                                  '*Enter valid email',
                                  style: lbBold.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              : const SizedBox.shrink(),
                          isMailAlreadyExit
                              ? Text(
                                  '*Email is already in use!',
                                  style: lbBold.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NetworkAwareTap(
                            onTap: () {
                              bool validation =
                                  _emailFormKey.currentState!.validate();

                              if (validation &&
                                  // _emailController.text.isNotEmpty &&
                                  EmailValidator.validate(
                                      _emailController.text)) {
                                sendOTP().then((value) {
                                  setState(
                                    () {
                                      if (value) {
                                        context.pop();
                                        showOTPDialogue(otpVerificationToken);
                                      } else {
                                        isMailAlreadyExit = true;
                                      }
                                    },
                                  );
                                });
                              } else if (_emailController.text.isEmpty &&
                                  validation) {
                                setState(() {
                                  checkMail = true;
                                  checkMailValidation = false;
                                });
                              } else if (_emailController.text.isNotEmpty &&
                                  !EmailValidator.validate(
                                      _emailController.text) &&
                                  //!_emailController.text.endsWith("@gmail.com") &&
                                  validation) {
                                setState(() {
                                  checkMail = false;
                                  checkMailValidation = true;
                                  _emailController.clear();
                                });
                              }
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text(
                                  "Submit",
                                  textAlign: TextAlign.center,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          NetworkAwareTap(
                            onTap: () {
                              setState(() {
                                checkMail = false;
                                checkMailValidation = false;
                                _emailController.clear();
                              });
                              context.pop();
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.backgroundColor,
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  "Cancel",
                                  textAlign: TextAlign.center,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              )
            ],
          );
        });
      },
    );
  }

  bool isMailChecked = false;
  String otpVerificationToken = '';
  Future<bool> sendOTP() async {
    try {
      await Provider.of<LoginController>(context, listen: false)
          .verifyEmail(_emailController.text.toLowerCase(), context)
          .then((responseMap) {
        if (responseMap["statusCode"] == 200) {
          final token = responseMap['data']['token'];
          otpVerificationToken = token;
          isMailChecked = true;
        } else {
          setState(() {
            errorMessage = responseMap['error'];
            isMailChecked = false;
          });
        }
      });
    } catch (e) {
      errorMessage = "Failed to submit request: $e";
      isMailChecked = false;
    }
    return isMailChecked;
  }

  void showOTPDialogue(String _token) {
    print(_token);
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.all(25),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Form(
                key: _otpFormKey,
                child: Column(
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(top: 10),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Center(
                          child: Text(
                            "Confirm Email",
                            textAlign: TextAlign.center,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              "Enter code sent to: ${_emailController.text}",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: TextFormField(
                        controller: _otpController,
                        style: lbRegular.copyWith(
                          fontSize: 18,
                        ),
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.all(10),
                          filled: true,
                          fillColor: AppConstants.backgroundColor,
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(
                              color: AppConstants.primaryColor,
                              width: 1.5,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(
                              color: AppConstants.primaryColor,
                              width: 1.5,
                            ),
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: const BorderSide(
                              color: AppConstants.primaryColor,
                              width: 1.5,
                            ),
                          ),
                          // errorStyle: errorMsg,
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Row(
                        children: [
                          checkOtp
                              ? Text(
                                  '*Enter verification code',
                                  style: lbBold.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              : const SizedBox.shrink(),
                          checkOtpValidation
                              ? Text(
                                  '*Enter valid code',
                                  style: lbBold.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NetworkAwareTap(
                            onTap: () {
                              bool validation =
                                  _otpFormKey.currentState!.validate();

                              if (validation &&
                                  _otpController.text.isNotEmpty) {
                                context.pop();
                                submitOTP(_token);
                                setState(() {});
                              } else if (_otpController.text.isEmpty &&
                                  !validation) {
                                setState(() {
                                  checkOtp = true;
                                  checkOtpValidation = false;
                                });
                              }
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3.5,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text(
                                  "Submit",
                                  textAlign: TextAlign.center,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          NetworkAwareTap(
                            onTap: () {
                              context.pop();
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3.5,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.backgroundColor,
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  "Cancel",
                                  textAlign: TextAlign.center,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              )
            ],
          );
        });
      },
    );
  }

  Future<UserProfileUpdateModel> getUserProfileUpdateModel(_email) async {
    int? userId = await CommonHelper.getLoggedInUserId();
    String? userName = await CommonHelper.getLoggedinUserName();
    String? userLocation = await CommonHelper.getLoggedinUserLocation();
    String? userBio = await CommonHelper.getLoggedinUserBio();
    var userData = UserProfileUpdateModel(
        userId: userId,
        userName: userName ?? '',
        userEmailId: _email,
        userLocation: userLocation ?? '',
        userBio: userBio ?? '');
    return userData;
  }

  bool showDoneImg = false;
  Future<void> submitOTP(String currentToken) async {
    String otp = _otpController.text;
    String email = _emailController.text;
    String respMessage = '';
    // resetForm();

    final responseMap =
        await Provider.of<LoginController>(context, listen: false)
            .verifyOTP(email.toLowerCase(), currentToken, otp, false, context);

    if (responseMap["statusCode"] == 200) {
      var userData = await getUserProfileUpdateModel(email);

      bool success =
          await Provider.of<ProfileController>(context, listen: false)
              .updateProfileFunction(userData, context);
      if (success) {
        respMessage = "Email has been confirmed and added.";
        showDoneImg = true;
      } else {
        respMessage = GenericMessages.somethingWrong;
        showDoneImg = false;
      }
    } else {
      respMessage = responseMap['error'];
      showDoneImg = false;
    }

    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () async {
                    SharedPreferences pref =
                        await SharedPreferences.getInstance();
                    pref.setString('userEmailId', _emailController.text);
                    setState(() {});
                    if (context.mounted) {
                      context.pop();
                    }
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          respMessage,
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NetworkAwareTap(
                      onTap: () async {
                        SharedPreferences pref =
                            await SharedPreferences.getInstance();
                        pref.setString('userEmailId', _emailController.text);
                        setState(() {});
                        if (context.mounted) {
                          context.pop();
                        }
                        resetForm();
                        // if (showDoneImg) {
                        //   context.pushNamed('login');
                        // }
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.5,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(fontSize: 14),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void handleConfirmRemoveEmail(index) {
    setState(() {
      removeEmailIndex = index;
    });
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Form(
              key: _otpFormKey,
              child: Column(
                children: [
                  NetworkAwareTap(
                    onTap: () {
                      context.pop();
                    },
                    child: Container(
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(top: 10),
                      child: Image.asset(
                        AppConstants.closePopupImagePath,
                        height: 30,
                        width: 30,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 30.0, right: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                            "Are you sure you want to remove this email?",
                            textAlign: TextAlign.center,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 30.0, right: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        NetworkAwareTap(
                          onTap: () {
                            if (_otpFormKey.currentState?.validate() ?? false) {
                              context.pop();
                              confirmRemoveEmail();
                            }
                          },
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width / 3.5,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.textGreenColor,
                            ),
                            child: Center(
                              child: Text(
                                "Yes",
                                textAlign: TextAlign.center,
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ),
                        NetworkAwareTap(
                          onTap: () {
                            context.pop();
                          },
                          child: Container(
                            height: 45,
                            width: MediaQuery.of(context).size.width / 3.5,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(49),
                              color: AppConstants.backgroundColor,
                              border: Border.all(
                                color: AppConstants.primaryColor,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                "Cancel",
                                textAlign: TextAlign.center,
                                style: lbRegular.copyWith(
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          ],
        );
      },
    );
  }

  void confirmRemoveEmail() {
    setState(() {
      _existingEmails.removeAt(removeEmailIndex!);
      //_existingEmailControllers.removeAt(removeEmailIndex!);
    });
  }

  void resetForm() {
    _emailController.clear();
    _otpController.clear();
  }
}
