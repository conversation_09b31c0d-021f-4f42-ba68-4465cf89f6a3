import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/models/user_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_text_widget.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class BlockListPage extends StatefulWidget {
  const BlockListPage({super.key});
  @override
  State createState() => _BlockListPageState();
}

class _BlockListPageState extends State<BlockListPage> {
  int? removeUserindex;
  String userName = '';
  String userHandle = '';
  String confirmationMessage = '';
  bool isUserUnblocked = false;

  List<UserModel> blockedUsersList = [
    UserModel(
      data: Data(
        userName: "Draper Pheonix",
        userHandle: "@botdude",
        userProfilePicture: AppConstants.profileLogoImagePath,
      ),
    ),
    UserModel(
      data: Data(
        userName: "Samy Spammer",
        userHandle: "@botgirl",
        userProfilePicture: AppConstants.profileLogoImagePath,
      ),
    )
  ];
  confirmRemoveFromBlockList() {
    setState(() {
      blockedUsersList.removeAt(removeUserindex!);
      confirmationMessage = "$userName has been unblocked.";
      isUserUnblocked = true;
    });
  }

  handleRemoveFromBlockList(index) {
    if (index >= 0 && index < blockedUsersList.length) {
      setState(() {
        userName = blockedUsersList[index].data?.userName ?? '';
        userHandle = blockedUsersList[index].data?.userHandle ?? '';
        removeUserindex = index;
        confirmationMessage =
            "Are you sure you want to unblock? \n\n $userName ($userHandle)";
      });
    }
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Padding(
                padding: const EdgeInsets.only(top: 10, right: 10),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      alignment: Alignment.centerRight,
                      child: NetworkAwareTap(
                        onTap: () => context.pop(),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                    // const SizedBox(height: 25),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Text(
                          confirmationMessage,
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        if (isUserUnblocked) ...[
                          NetworkAwareTap(
                            onTap: () {
                              context.pop();
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text(
                                  "Ok",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ] else ...[
                          NetworkAwareTap(
                            onTap: () {
                              setState(() {
                                confirmRemoveFromBlockList();
                              });
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text(
                                  "Submit",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          NetworkAwareTap(
                            onTap: () => context.pop(),
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.backgroundColor,
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  "Cancel",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ]
                      ],
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: Color.fromRGBO(37, 57, 67, 1),
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: 'Manage Blolcked List',
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Currently Blocked",
                style: lbRegular.copyWith(
                  fontSize: 18,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              ListView.builder(
                padding: const EdgeInsets.only(bottom: 25),
                shrinkWrap:
                    true, // Ensures the ListView takes only necessary height
                physics:
                    const NeverScrollableScrollPhysics(), // Prevents nested scrolling
                itemCount: blockedUsersList.length,
                itemBuilder: (context, index) {
                  return NetworkAwareTap(
                    onTap: () {},
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 25.0),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: AppConstants.primaryColor,
                            width: 1.5,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(14.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      blockedUsersList[index].data?.userName ??
                                          '',
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                    Text(
                                      blockedUsersList[index]
                                              .data
                                              ?.userHandle ??
                                          '',
                                      style: lbBold.copyWith(
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    NetworkAwareTap(
                                      onTap: () {
                                        handleRemoveFromBlockList(index);
                                      },
                                      child: Text(
                                        "Remove",
                                        style: libreBaskervilleRegular.copyWith(
                                          fontSize: 14,
                                          fontStyle: FontStyle.italic,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Align(
                                alignment: Alignment.center,
                                child: Image.asset(
                                  blockedUsersList[index]
                                          .data
                                          ?.userProfilePicture ??
                                      '',
                                  height: 50,
                                  width: 50,
                                  fit: BoxFit.cover,
                                  filterQuality: FilterQuality.high,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
