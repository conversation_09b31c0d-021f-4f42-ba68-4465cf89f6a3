import 'dart:developer';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/currency_helper.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../constants/text_style.dart';
import '../../controller/subscription_controller.dart';
import '../../models/subscription_model/subscription_model.dart';

class ManageSubscritionPage extends StatefulWidget {
  const ManageSubscritionPage({super.key});

  @override
  State createState() => _ManageSubscritionPageState();
}

class _ManageSubscritionPageState extends State<ManageSubscritionPage> {
  SubscriptionController? subscriptionController;

  @override
  void initState() {
    subscriptionController =
        Provider.of<SubscriptionController>(context, listen: false);
    getSubscription();
    super.initState();
  }

  List<SubscriptionDetail>? subscriptionDetails;
  String? subScriptionName;
  double? subScriptionValue;
  String? subScriptionCurrency;
  String currencySymbol = '';
  String? purchasePlatform;

  Future<void> getSubscription() async {
    final subscriptionDetails =
        subscriptionController?.subscriptionDetails?.data?.subscriptionDetails;
    subScriptionValue =
        subscriptionController?.verifySubscriptionModel?.data?.ubAmount;
    subScriptionName =
        subscriptionController?.verifySubscriptionModel?.data?.usubProductId;
    subScriptionCurrency =
        subscriptionController?.verifySubscriptionModel?.data?.ubCurrency;
    purchasePlatform =
        subscriptionController?.verifySubscriptionModel?.data?.ubPlatform;
    log("Subscription Amount : $subScriptionValue");
    log("Subscription Name : $subScriptionName");
    log("Subscription Currency : $subScriptionCurrency");
    log("Subscription Details : ${subscriptionDetails?.last.productId}");
    log("Purchase Platform : $purchasePlatform");
    await CurrencyHelper.getSymbolsFromCode(subScriptionCurrency ?? '')
        .then((value) {
      currencySymbol = value?[0] ?? '';
      log("Currency Code : $currencySymbol");
    });
    for (var element in subscriptionDetails ?? <SubscriptionDetail>[]) {
      if (element.productId == subScriptionName) {
        subScriptionName = element.subscriptionName;
        log("Subscription Name : $subScriptionName");
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: 'Manage Subscription',
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(25.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Current Plan",
                style: lbRegular.copyWith(
                  fontSize: 18,
                  color: AppConstants.primaryColor,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Container(
                margin: EdgeInsets.only(bottom: 25),
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: AppConstants.primaryColor,
                    width: 2,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.7,
                          child: Text(
                            subScriptionName ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: lbRegular.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              currencySymbol,
                              style: lbBold.copyWith(
                                fontSize: 24,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                            Text(
                              "$subScriptionValue",
                              style: lbBold.copyWith(
                                fontSize: 24,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              CustomButton(
                text: 'Cancel Subscription',
                onPressed: () async {
                  String cancellationUrl = '';
                  if (purchasePlatform == PlatformPurchase.google) {
                    cancellationUrl =
                        'https://play.google.com/store/account/subscriptions';
                  } else if (purchasePlatform == PlatformPurchase.apple) {
                    cancellationUrl =
                        'https://apps.apple.com/account/subscriptions';
                  }
                  setState(() {});
                  await launchUrl(
                    Uri.parse(cancellationUrl),
                    mode: LaunchMode.externalApplication,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
