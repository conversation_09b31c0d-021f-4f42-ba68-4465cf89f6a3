import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/models/club_charter_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/custom_button.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DeleteAccountPage extends StatefulWidget {
  const DeleteAccountPage({super.key});

  @override
  State createState() => _DeleteAccountPageState();
}

class _DeleteAccountPageState extends State<DeleteAccountPage> {
  int? loggedinUserId;
  @override
  void initState() {
    _initializeUserId();
    super.initState();
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();

    // if (userId != null) {
    //   loggedinUserId = userId;
    // } else {}
  }

  List<ClubCharterModel> information = [
    ClubCharterModel(
      rules: "Are you sure you want to permanently delete your account?",
    ),
    ClubCharterModel(
      rules: "This is irreversible.",
    ),
    ClubCharterModel(
      rules:
          "Deleting your account does not cancel a paid El Junto Subscription. You must manage your paid subscription through the platform or app store that you originally used to subscribe.",
    ),
    ClubCharterModel(
      rules:
          "All of you information will be deleted, and you will need to create a new account and resubscribe under that new account to use this app again.",
    ),
  ];

  Future<bool> confirmDeleteAccount() async {
    bool value = await Provider.of<UserController>(context, listen: false)
        .deleteAccount(loggedinUserId);
    return value;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: Color.fromRGBO(37, 57, 67, 1),
              ),
            ),
          ),
          child: const PreviousScreenAppBar(
            bookName: 'Delete Account',
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: FutureBuilder(
          future: _initializeUserId(),
          builder: (context, snapShot) {
            return ListView(
              children: [
                const SizedBox(height: 10),
                ...information.map((policy) {
                  return Padding(
                    padding:
                        const EdgeInsets.only(left: 20, right: 20, top: 15),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 3.0),
                          child: Align(
                            alignment: Alignment.topCenter,
                            child: Text(
                              "•",
                              textAlign: TextAlign.start,
                              style: lbRegular.copyWith(
                                fontSize: 20,
                                height: 0.8,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 5),
                        Expanded(
                          child: Text(
                            textAlign: TextAlign.start,
                            policy.rules ?? '',
                            style: lbRegular.copyWith(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: CustomButton(
                    text: "Delete Account",
                    onPressed: handleDeleteAccount,
                  ),
                )
              ],
            );
          },
        ),
      ),
    );
  }

  handleDeleteAccount() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.symmetric(horizontal: 25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Text(
                  "Just a final check",
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Text(
                    "Are you sure you wish to delete your account?",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () async {
                          SharedPreferences pref =
                              await SharedPreferences.getInstance();
                          Map<String, dynamic> payload = {};
                          confirmDeleteAccount().then((value) async {
                            if (value) {
                              if (context.mounted) {
                                await pref.clear();
                                log('Token expire');
                                if (context.mounted) {
                                  context.goNamed('login');
                                }
                              }

                              // print(value);
                              // context.goNamed('login');
                            }
                          });
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.0,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Yes",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.0,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.backgroundColor,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
