import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../constants/text_style.dart';

Future<DateTime?> selectDate({
  required BuildContext context,
  required TextEditingController meetingDateController,
  required bool isAndroid,
  required void Function() generateTimeOptions,
}) async {
  String daySuffix(int day) {
    if (day >= 11 && day <= 13) {
      return 'th';
    }
    switch (day % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }

  DateTime? pickDate;

  if (isAndroid) {
    pickDate = await showDatePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      barrierColor: AppConstants.textGreenColor,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppConstants.primaryColor,
              surface: AppConstants.backgroundColor,
              onSurface: AppConstants.primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );
    if (pickDate != null) {
      generateTimeOptions();
      String formattedDate = DateFormat('EEE MMM d').format(pickDate);
      String dayWithSuffix = daySuffix(pickDate.day);
      String yearAndTime = DateFormat('yyyy').format(pickDate);
      meetingDateController.text = '$formattedDate$dayWithSuffix, $yearAndTime';
    }
  } else {
    await showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) {
        String iosDatePick = "";
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              height: MediaQuery.of(context).size.height * .50,
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: MediaQuery.of(context).size.height * .30,
                    child: CupertinoDatePicker(
                      mode: CupertinoDatePickerMode.date,
                      showDayOfWeek: true,
                      minimumYear: DateTime.now().year,
                      minimumDate: DateTime.now(),
                      initialDateTime: DateTime.now(),
                      maximumYear: DateTime.now().year + 1,
                      onDateTimeChanged: (value) {
                        pickDate = value;
                        generateTimeOptions();
                        String formattedDate =
                            DateFormat('EEE MMM d').format(value);
                        String dayWithSuffix = daySuffix(value.day);
                        String yearAndTime = DateFormat('yyyy').format(value);
                        String timeZone = DateFormat('z').format(value);
                        iosDatePick =
                            '$formattedDate$dayWithSuffix, $yearAndTime $timeZone';
                        setState(() {});
                      },
                    ),
                  ),
                  NetworkAwareTap(
                    onTap: () {
                      if (iosDatePick.isEmpty) {
                        final currentDateTime = DateTime.now();
                        String formattedDate =
                            DateFormat('EEE MMM d').format(currentDateTime);
                        String dayWithSuffix = daySuffix(currentDateTime.day);
                        String yearAndTime =
                            DateFormat('yyyy').format(currentDateTime);
                        String timeZone =
                            DateFormat('z').format(currentDateTime);
                        meetingDateController.text =
                            '$formattedDate$dayWithSuffix, $yearAndTime $timeZone';
                        pickDate = currentDateTime;
                      } else {
                        meetingDateController.text = iosDatePick;
                      }
                      context.pop();
                    },
                    child: _buildButton("Save", AppConstants.textGreenColor),
                  ),
                  const SizedBox(height: 10),
                  NetworkAwareTap(
                    onTap: () {
                      meetingDateController.text = "TBD";
                      context.pop();
                    },
                    child: _buildButton("Cancel", Colors.transparent,
                        AppConstants.primaryColor),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
  return pickDate;
}

Widget _buildButton(String text, Color bgColor, [Color? borderColor]) {
  return Container(
    height: 45,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(49),
      color: bgColor,
      border: borderColor != null ? Border.all(color: borderColor) : null,
    ),
    child: Center(
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: lbBold.copyWith(
          fontSize: 18,
        ),
      ),
    ),
  );
}
