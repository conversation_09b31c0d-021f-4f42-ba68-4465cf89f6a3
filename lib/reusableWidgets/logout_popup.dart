

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';

// Future<void> logoutPopup(BuildContext context)async {
//   showCupertinoModalPopup(context: context, builder: (context) {
//     return AlertDialog(
//       title: Text(''),
//       actions: [
//         Container(
//           height: 45,
//           width: MediaQuery.of(context).size.width,

//         ),
//       ],
//     );
//   },);
// }