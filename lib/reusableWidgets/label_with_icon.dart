import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';

class LabelWithIcon extends StatelessWidget {
  final String labelText;
  final VoidCallback onIconTap;
  final bool isOptional;
  final String iconPath;

  const LabelWithIcon({
    super.key,
    required this.labelText,
    required this.onIconTap,
    this.isOptional = false,
    this.iconPath = 'assets/icons/information.png',
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        children: [
          Text(
            labelText,
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(
              fontSize: 18,
              color: AppConstants.primaryColor,
            ), // Replace with lbRegular if needed
          ),
          const SizedBox(width: 10),
          NetworkAwareTap(
            onTap: onIconTap,
            child: Image.asset(
              iconPath,
              filterQuality: FilterQuality.high,
              fit: BoxFit.cover,
              height: 22,
              width: 22,
            ),
          ),
          if (isOptional) ...[
            const Spacer(),
            Text(
              '(Optional)',
              overflow: TextOverflow.ellipsis,
              style: lbItalic.copyWith(
                fontSize: 14,
                color: AppConstants.primaryColor,
              ), // Replace with lbItalic if needed
            ),
          ],
        ],
      ),
    );
  }
}
