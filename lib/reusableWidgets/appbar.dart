import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

class AppBarWidget extends StatefulWidget {
  final String? appBarText;
  const AppBarWidget({super.key, this.appBarText});

  @override
  State<AppBarWidget> createState() => _AppBarWidgetState();
}

class _AppBarWidgetState extends State<AppBarWidget> {
  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppConstants.textGreenColor,
      centerTitle: true,
      title: Padding(
        padding: const EdgeInsets.only(top: 20.0),
        child: Text(
          widget.appBarText ?? AppConstants.appName,
          style: lbBold.copyWith(
            fontSize: 18,
          ),
        ),
      ),
      leading: Padding(
        padding: const EdgeInsets.only(left: 17.0, top: 10),
        child: Skeleton.replace(
          replacement: ClipRRect(
            borderRadius: const BorderRadius.all(
              Radius.circular(49),
            ),
            child: Image.asset(
              "assets/images/eljunto_logo_dark_teal.png",
              fit: BoxFit.cover,
              // height: 80,
              // width: 50,
              filterQuality: FilterQuality.high,
              alignment: Alignment.center,
            ),
          ),
          child: Image.asset(
            "assets/images/eljunto_logo_dark_teal.png",
            fit: BoxFit.cover,
            // height: 80,
            // width: 50,
            filterQuality: FilterQuality.high,
            alignment: Alignment.center,
          ),
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 20.0, top: 10),
          child: NetworkAwareTap(
            onTap: () {
              showDialog(
                barrierColor: Colors.white60,
                barrierDismissible: false,
                context: context,
                builder: (BuildContext context) {
                  return const QuestionFeedbackDialog();
                },
              );
            },
            child: Skeleton.replace(
              replacement: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Image.asset(
                  AppConstants.questionLogoImagePath,
                  height: 34,
                  width: 34,
                ),
              ),
              child: Image.asset(
                AppConstants.questionLogoImagePath,
                height: 34,
                width: 34,
              ),
            ),
          ),
        )
      ],
    );
  }
}
