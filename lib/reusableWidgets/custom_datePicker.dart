import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class YearMonthPicker extends StatefulWidget {
  final DateTime selectedDate;
  final ValueChanged<DateTime> onDateChanged;

  YearMonthPicker({
    required this.selectedDate,
    required this.onDateChanged,
  });

  @override
  _YearMonthPickerState createState() => _YearMonthPickerState();
}

class _YearMonthPickerState extends State<YearMonthPicker> {
  late int selectedYear;
  late int selectedMonth;

  @override
  void initState() {
    super.initState();
    selectedYear = widget.selectedDate.year;
    selectedMonth = widget.selectedDate.month;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DropdownButton<int>(
          value: selectedYear,
          items: List.generate(
            100,
            (index) => DropdownMenuItem(
              value: 2000 + index,
              child: Text((2000 + index).toString()),
            ),
          ),
          onChanged: (year) {
            setState(() {
              selectedYear = year!;
              widget.onDateChanged(DateTime(selectedYear, selectedMonth));
            });
          },
        ),
        SizedBox(width: 10),
        DropdownButton<int>(
          value: selectedMonth,
          items: List.generate(
            12,
            (index) => DropdownMenuItem(
              value: index + 1,
              child: Text(DateFormat.MMM().format(DateTime(0, index + 1))),
            ),
          ),
          onChanged: (month) {
            setState(() {
              selectedMonth = month!;
              widget.onDateChanged(DateTime(selectedYear, selectedMonth));
            });
          },
        ),
      ],
    );
  }
}
