import 'dart:developer';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/inapp_purchase_controller.dart';
import 'package:eljunto/controller/subscription_controller.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:provider/provider.dart';

String? selectedOutcome;
// int? selectedIndex;
String? currencyCode;
double? planPurchasePrice;

class SubscriptionListUI extends StatefulWidget {
  final List<ProductDetails>? subProducts;
  const SubscriptionListUI({
    super.key,
    this.subProducts,
  });

  @override
  State<SubscriptionListUI> createState() => _SubscriptionListUIState();
}

class _SubscriptionListUIState extends State<SubscriptionListUI> {
  List<ProductDetails> productsList = [];
  List<ProductDetails> freeProduct = [];
  List<ProductDetails> displayProduct = [];
  List<ProductDetails> emptyList = [];

  ProductDetails? purchaseProduct;
  bool isFreeTrial = false;
  @override
  void initState() {
    showSubscriptionOptions();
    super.initState();
  }

  void showSubscriptionOptions() async {
    // Get list of purchased subscriptions
    List<String> purchasedProductIds =
        await Provider.of<InAppPurchaseController>(context, listen: false)
            .getUserPurchasedProducts();

    if (mounted) {
      displayProduct =
          Provider.of<InAppPurchaseController>(context, listen: false)
              .products
              .where((product) => product.price.toLowerCase() != 'free')
              .toList();
      // for (int i = 0; i < 3; i++) {
      //   emptyList.add(displayProduct[0]);
      // }

      log("Display Product List : $displayProduct");

      // Filter out free trials for users who have already subscribed
      productsList =
          Provider.of<InAppPurchaseController>(context, listen: false)
              .products
              .where((product) {
        bool hasFreeTrial = product.price.toLowerCase() == 'free';
        if (hasFreeTrial) {
          freeProduct.add(product);
        }

        if (purchasedProductIds.contains(product.id)) {
          return !hasFreeTrial; // Remove free trial if already subscribed
        }
        return true;
      }).toList();
      log("Product List : $productsList");

      isFreeTrial = Provider.of<SubscriptionController>(context, listen: false)
          .isFreeTraial;

      log("Subscription Response : $isFreeTrial");

      log("Free Product List : $freeProduct");
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InAppPurchaseController>(
      builder: (context, inAppPurchasaeController, child) {
        return Column(
          children: [
            Column(
              children: displayProduct.map((product) {
                return Container(
                  margin: EdgeInsets.only(bottom: 25),
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: AppConstants.primaryColor,
                      width: 2,
                    ),
                  ),
                  child: ListTile(
                    contentPadding: EdgeInsets.only(left: 15, right: 10),
                    horizontalTitleGap: 0,
                    onTap: () async {
                      if (selectedOutcome == product.id) {
                        selectedOutcome = null;
                        purchaseProduct = null;
                        currencyCode = null;
                        planPurchasePrice = null;
                        log("Deselected Product");
                      } else {
                        selectedOutcome = product.id;
                        log("Selected Products : \$selectedOutcome");
                        currencyCode = product.currencyCode;
                        planPurchasePrice = product.rawPrice;

                        if (isFreeTrial) {
                          if (freeProduct.isNotEmpty) {
                            for (var list in freeProduct) {
                              if (list.id == product.id) {
                                log("Selected Plan : \$list");
                                purchaseProduct = list;
                                selectedOutcome = list.id;
                                currencyCode = list.currencyCode;
                                planPurchasePrice = list.rawPrice;
                                break;
                              } else {
                                log('Free Product : ${product.price}');
                                purchaseProduct = product;
                              }
                            }
                          } else {
                            log('no free product');
                            purchaseProduct = product;
                          }
                        } else {
                          purchaseProduct = product;
                          log('no trial product');
                        }
                      }
                      await Provider.of<InAppPurchaseController>(context,
                              listen: false)
                          .purchasePlan(purchaseProduct);
                      setState(() {});
                    },
                    title: Text(
                      product.title,
                      overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    subtitle: Text(
                      product.price,
                      style: lbBold.copyWith(
                        fontSize: 24,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    trailing: Transform.scale(
                      scale: 1.6,
                      child: Radio(
                        fillColor: WidgetStatePropertyAll(
                          AppConstants.primaryColor,
                        ),
                        value: product.id,
                        groupValue: selectedOutcome,
                        onChanged: null,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     NetworkAwareTap(
            //       onTap: () async {
            //         log("Selected Index1111 : $purchaseProduct");
            //         if (purchaseProduct != null) {
            //           await Provider.of<InAppPurchaseController>(context,
            //                   listen: false)
            //               .buyProduct(purchaseProduct!);
            //         }
            //       },
            //       child: Container(
            //         padding: const EdgeInsets.symmetric(vertical: 12),
            //         width: MediaQuery.of(context).size.width / 2.5,
            //         decoration: BoxDecoration(
            //           color: AppConstants.textGreenColor,
            //           borderRadius: BorderRadius.circular(49),
            //         ),
            //         child: Text(
            //           "Subscribe",
            //           textAlign: TextAlign.center,
            //           style: lbBold.copyWith(
            //             fontSize: 18,
            //             color: AppConstants.primaryColor,
            //           ),
            //         ),
            //       ),
            //     ),
            //     NetworkAwareTap(
            //       onTap: () async {
            //         Map<String, dynamic> payload = {};
            //         if (context.mounted) {
            //           await CommonHelper().userLogoutFunction(context, payload);
            //         }
            //       },
            //       child: Container(
            //         padding: const EdgeInsets.symmetric(vertical: 12),
            //         width: MediaQuery.of(context).size.width / 2.5,
            //         decoration: BoxDecoration(
            //           borderRadius: BorderRadius.circular(49),
            //           border: Border.all(
            //             color: AppConstants.primaryColor,
            //             width: 1,
            //           ),
            //         ),
            //         child: Text(
            //           "Logout",
            //           textAlign: TextAlign.center,
            //           style: lbBold.copyWith(
            //             fontSize: 18,
            //             color: AppConstants.primaryColor,
            //           ),
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
          ],
        );
      },
    );
  }
}
