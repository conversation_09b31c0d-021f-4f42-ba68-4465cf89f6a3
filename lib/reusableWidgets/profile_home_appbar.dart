import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

import 'question_feedback_dialog.dart';

class ProfileHomeAppBar extends StatefulWidget {
  final String? userName;
  final String? userHandler;
  final bool isClubInvitation;
  final int? index;
  final bool userOwnProfile;
  final String? userProfilePicture;

  const ProfileHomeAppBar({
    super.key,
    this.userName,
    this.userHandler,
    this.index,
    this.isClubInvitation = false,
    this.userOwnProfile = false,
    this.userProfilePicture,
  });

  @override
  State<ProfileHomeAppBar> createState() => _ProfileHomeAppBarState();
}

class _ProfileHomeAppBarState extends State<ProfileHomeAppBar> {
  bool isDelay = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    String image = Config.imageBaseUrl;
    String? userProfilePicture = widget.userProfilePicture;
    String fullImageUrl = image + (userProfilePicture ?? '');

    return AppBar(
      backgroundColor: AppConstants.textGreenColor,
      leading: Padding(
        padding: const EdgeInsets.only(left: 17.0, top: 10),
        child: Skeleton.replace(
          replacement: ClipRRect(
            borderRadius: BorderRadius.circular(49),
            child: Image.asset(
              "assets/images/eljunto_logo_dark_teal.png",
              fit: BoxFit.cover,
              // height: 80,
              // width: 50,
              filterQuality: FilterQuality.high,
              alignment: Alignment.center,
            ),
          ),
          child: Image.asset(
            "assets/images/eljunto_logo_dark_teal.png",
            fit: BoxFit.cover,
            // height: 80,
            // width: 50,
            filterQuality: FilterQuality.high,
            alignment: Alignment.center,
          ),
        ),
      ),
      centerTitle: true,
      title: Padding(
        padding: const EdgeInsets.only(top: 10.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: CustomCachedNetworkImage(
                imageUrl: fullImageUrl,
                width: 45,
                height: 45,
                errorImage: AppConstants.profileLogoImagePath,
              ),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.5,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MarqueeList(
                      children: [
                        Text(
                          widget.userName ?? '',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: lbBold.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            widget.userHandler ?? '',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: lbBold.copyWith(
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (widget.isClubInvitation)
                          Image.asset(
                            AppConstants.openToInvitationImagePath,
                            height: 15,
                            width: 15,
                          )
                        else if (!widget.isClubInvitation &&
                            widget.userOwnProfile)
                          Image.asset(
                            AppConstants.notOpenToInvitationImagePath,
                            height: 20,
                            width: 20,
                          )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 20.0, top: 10),
          child: NetworkAwareTap(
            onTap: () {
              showDialog(
                context: context,
                barrierColor: Colors.white60,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return const QuestionFeedbackDialog();
                },
              );
            },
            child: Skeleton.replace(
              replacement: ClipRRect(
                borderRadius: BorderRadius.circular(49),
                child: Image.asset(
                  AppConstants.questionLogoImagePath,
                  height: 34,
                  width: 34,
                ),
              ),
              child: Image.asset(
                AppConstants.questionLogoImagePath,
                height: 34,
                width: 34,
              ),
            ),
          ),
        )
      ],
    );
  }
}

 //   Expanded(
        //     child: Padding(
        //       padding: const EdgeInsets.only(top: 10.0, left: 40),
        //       child: Row(
        //         mainAxisAlignment: MainAxisAlignment.center,
        //         children: [
        //           ClipRRect(
        //             borderRadius: BorderRadius.circular(50),
        //             child: CustomCachedNetworkImage(
        //               imageUrl: fullImageUrl,
        //               width: 45,
        //               height: 45,
        //               errorImage: AppConstants.profileLogoImagePath,
        //             ),
        //           ),
        //           const SizedBox(
        //             width: 7.5,
        //           ),
        //           Column(
        //             crossAxisAlignment: CrossAxisAlignment.start,
        //             children: [
        //               Text(
        //                 widget.userName ?? '',
        //                 overflow: TextOverflow.ellipsis,
        //                 style: lbBold.copyWith(
        //                   fontSize: 18,
        //                 ),
        //               ),
        //               Row(
        //                 children: [
        //                   Text(
        //                     widget.userHandler ?? '',
        //                     overflow: TextOverflow.ellipsis,
        //                     style: lbBold.copyWith(
        //                       fontSize: 14,
        //                     ),
        //                   ),
        //                   const SizedBox(
        //                     width: 8,
        //                   ),
        //                   if (isDelay) ...[
        //                     const SizedBox.shrink(),
        //                   ] else ...[
        //                     if (widget.isClubInvitation) ...[
        //                       Image.asset(
        //                         AppConstants.openToInvitationImagePath,
        //                         height: 15,
        //                         width: 15,
        //                       )
        //                     ] else if (!widget.isClubInvitation &&
        //                         widget.userOwnProfile) ...[
        //                       Image.asset(
        //                         AppConstants.notOpenToInvitationImagePath,
        //                         height: 20,
        //                         width: 20,
        //                       )
        //                     ]
        //                   ],
        //                 ],
        //               ),
        //             ],
        //           ),
        //         ],
        //       ),
        //     ),
        //   ),
