import 'package:eljunto/constants/text_style.dart';
import 'package:flutter/material.dart';

class UnorderedList extends StatelessWidget {
  final List<String> items;

  const UnorderedList(this.items, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: items.map((item) => UnorderedListItem(item)).toList(),
    );
  }
}

/* class UnorderedListItem extends StatelessWidget {
  final String text;

  const UnorderedListItem(this.text, {super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.circle, size: 8, color: Colors.black),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: libreBaskervilleRegular.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
 */
class UnorderedListItem extends StatelessWidget {
  final String item;

  const UnorderedListItem(this.item, {super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '• ',
            style: TextStyle(fontSize: 18),
          ),
          Expanded(
            child: Text(
              item,
              style: lbRegular.copyWith(
                fontSize: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
