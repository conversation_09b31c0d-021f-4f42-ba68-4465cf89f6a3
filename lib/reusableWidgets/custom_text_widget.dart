import 'package:eljunto/constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/* const libreBaskervilleRegular = TextStyle(
    fontFamily: 'Libre Baskerville',
    fontWeight: FontWeight.w700,
    fontSize: 14,
    height: 1.24); */

/* const errorMsg = TextStyle(
    fontFamily: 'Libre Baskerville',
    fontWeight: FontWeight.w700,
    fontSize: 14,
    height: 1.24,
    color: Colors.red); */

final TextStyle libreBaskervilleRegular = GoogleFonts.libreBaskerville(
    // fontWeight: FontWeight.w700,
    // fontSize: 14,
    color: AppConstants.primaryColor);

final TextStyle errorMsg = GoogleFonts.libreBaskerville(
  fontWeight: FontWeight.w700,
  fontSize: 14,
  height: 1.24,
  color: const Color.fromRGBO(246, 3, 3, 1),
);
