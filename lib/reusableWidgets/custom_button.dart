import 'dart:developer';

import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/connectivity_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/connection_lost_screen.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/services/setup_locator.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../constants/constants.dart';

class CustomButton extends StatelessWidget {
  final String? text;
  final VoidCallback? onPressed;
  final Color? color;
  final Color? borderColor;
  final Color? textColor;
  final bool? isOutlined;

  const CustomButton({
    super.key,
    this.text,
    this.onPressed,
    this.color,
    this.borderColor,
    this.textColor,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    return NetworkAwareTap(
      onTap: onPressed!,
      child: Container(
        height: 45,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          color: color ?? AppConstants.textGreenColor,
          border: (isOutlined ?? false)
              ? Border.all(
                  color: (borderColor ?? const Color(0xff5a5a5a))
                      .withOpacity(0.80),
                  width: 2,
                )
              : null,
          // 0xff5a5a5a
        ),
        child: Center(
          child: Text(
            text ?? "",
            textAlign: TextAlign.center,
            style: lbBold.copyWith(
              fontSize: 18,
            ),
          ),
        ),
      ),
    );
  }
}

class CustomLoaderButton extends StatelessWidget {
  final bool? subscriptionLogout;
  final double? buttonWidth;
  final double? buttonRadius;
  final String? loginText;
  final Function()? buttonPressed;
  final Widget? buttonChild;
  const CustomLoaderButton({
    super.key,
    this.loginText,
    this.buttonPressed,
    this.buttonWidth,
    this.buttonRadius,
    this.buttonChild,
    this.subscriptionLogout = false,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: buttonWidth,
        height: 45.0,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(buttonRadius ?? 0),
        ),
        child: ElevatedButton(
          onPressed: () {
            final provider = locator<ConnectivityProvider>();
            if (provider.status == InternetStatus.disconnected) {
              showModalBottomSheet(
                context: context,
                isScrollControlled: false,
                isDismissible: true,
                enableDrag: true,
                backgroundColor: Colors.transparent,
                useRootNavigator: true,
                barrierColor: AppConstants.backgroundColor.withOpacity(.5),
                sheetAnimationStyle: AnimationStyle(
                  duration: Duration(milliseconds: 300),
                  curve: Curves.elasticIn,
                  reverseDuration: Duration(milliseconds: 300),
                  reverseCurve: Curves.elasticOut,
                ),
                builder: (context) => ConnectivityLossSheet(
                  onTryAgain: () async {
                    if (provider.status == InternetStatus.connected) {
                      log('try again triggered');
                      Navigator.pop(context);
                      buttonPressed?.call();
                    }
                  },
                ),
              );
              return;
            }
            buttonPressed?.call();
          },
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(buttonRadius ?? 0),
            ),
            backgroundColor: AppConstants.textGreenColor,
          ),
          child: buttonChild,
        ),
      ),
    );
  }
}

class CustomLoaderLogoutButton extends StatelessWidget {
  final bool subscriptionLogout;
  final double? buttonWidth;
  final double? buttonRadius;
  final String? loginText;
  final Function()? buttonPressed;
  final Widget? buttonChild;
  const CustomLoaderLogoutButton({
    super.key,
    this.loginText,
    this.buttonPressed,
    this.buttonWidth,
    this.buttonRadius,
    this.buttonChild,
    this.subscriptionLogout = false,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: buttonWidth,
        height: 45.0,
        decoration: BoxDecoration(
          border: Border.all(color: AppConstants.primaryColor),
          // color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(buttonRadius ?? 0),
        ),
        child: NetworkAwareTap(
          onTap: buttonPressed!,
          child: Container(
            alignment: Alignment.center,
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(buttonRadius ?? 0),
              color: Colors.transparent,
            ),
            child: buttonChild,
          ),
        ),
        //  ElevatedButton(
        //   onPressed: buttonPressed,
        //   style: ElevatedButton.styleFrom(
        //     padding: EdgeInsets.zero,
        //     shape: RoundedRectangleBorder(
        //       borderRadius: BorderRadius.circular(buttonRadius ?? 0),
        //     ),
        //     backgroundColor: AppConstants.textGreenColor,
        //   ),
        //   child: buttonChild,
        // ),
      ),
    );
  }
}
