import 'dart:developer';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:provider/provider.dart';

import '../controller/book_case_controller.dart';
import '../services/diacritics_remover.dart';

class CustomTypeAheadField<T> extends StatefulWidget {
  final bool clubMeetingCreation;
  final SuggestionsController<T>? suggestionsController;
  final TextEditingController controller;
  final ScrollController? scrollController;
  final Future<List<T>> Function(String) onSuggestions;
  final Widget Function(BuildContext, T) itemBuilder;
  final void Function(T) onSelected;
  final String? Function(String?)? validator;
  final bool autofocus;
  final InputDecoration? decoration;
  final int minCharsForSuggestions;
  final TextStyle? textStyle;
  final bool showDiacriticWarning;

  const CustomTypeAheadField({
    super.key,
    this.clubMeetingCreation = false,
    required this.suggestionsController,
    required this.controller,
    required this.onSuggestions,
    required this.itemBuilder,
    required this.onSelected,
    this.scrollController,
    this.validator,
    this.autofocus = false,
    this.decoration,
    this.minCharsForSuggestions = 3,
    this.textStyle,
    this.showDiacriticWarning = true,
  });

  @override
  State<CustomTypeAheadField<T>> createState() =>
      _CustomTypeAheadFieldState<T>();
}

class _CustomTypeAheadFieldState<T> extends State<CustomTypeAheadField<T>> {
  final _textCleaner = TextCleaner();
  bool hasDiacritics = false;
  BookCaseController? bookCaseController;

  @override
  void initState() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    super.initState();
  }

  void _updateDiacriticStatus(String text) {
    if (widget.showDiacriticWarning) {
      setState(() {
        hasDiacritics = _textCleaner.containsSpecialChars(text);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return TypeAheadField<T>(
      suggestionsController: widget.suggestionsController,
      scrollController: widget.scrollController,
      controller: widget.controller,
      builder: (context, controller, focusNode) => _buildTextField(
        controller,
        focusNode,
      ),
      itemBuilder: widget.itemBuilder,
      onSelected: widget.onSelected,
      suggestionsCallback: _handleSuggestions,
      decorationBuilder: _buildDecoration,
      loadingBuilder: _buildLoadingIndicator,
      emptyBuilder: _buildEmptyState,
      hideOnEmpty: true,
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    FocusNode focusNode,
  ) {
    return TextFormField(
      inputFormatters: [
        controller.text.isEmpty
            ? FilteringTextInputFormatter.deny(RegExp(r'\s'))
            : FilteringTextInputFormatter.singleLineFormatter,
      ],
      controller: controller,
      focusNode: focusNode,
      style: widget.textStyle ?? const TextStyle(fontSize: 16),
      autofocus: widget.autofocus,
      validator: widget.validator,
      textCapitalization: TextCapitalization.sentences,
      decoration: (widget.decoration ?? const InputDecoration()).copyWith(
        suffixIcon: NetworkAwareTap(
          onTap: () => controller.text.isNotEmpty ? controller.clear() : null,
          child: Icon(
            controller.text.isEmpty
                ? Icons.search_rounded
                : Icons.clear_rounded,
            size: 25,
            color: AppConstants.primaryColor,
          ),
        ),
      ),
      onChanged: (value) {
        setState(() {
          bookCaseController?.updateTypeAheadFlag(false);
        });
        _updateDiacriticStatus(value);
      },
    );
  }

  Future<List<T>> _handleSuggestions(String pattern) async {
    if (pattern.length < widget.minCharsForSuggestions) {
      return [];
    }
    final normalizedPattern = _textCleaner.removeAll(pattern.toLowerCase());
    log("Normalized Pattern: $normalizedPattern");
    return widget.onSuggestions(normalizedPattern);
  }

  Widget _buildDecoration(BuildContext context, Widget child) {
    return Material(
      type: MaterialType.card,
      elevation: 0,
      color: widget.clubMeetingCreation
          ? Colors.white
          : AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(
          color: AppConstants.primaryColor,
        ),
      ),
      child: child,
    );
  }

  Widget _buildLoadingIndicator(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(
        color: AppConstants.primaryColor,
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return ListTile(
      title: Text(
        'No items found!',
        style: lbRegular.copyWith(fontSize: 14),
      ),
    );
  }
}
