import 'package:cached_network_image/cached_network_image.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:flutter/material.dart';

Widget imageBuilder(String imageUrl) {
  if (imageUrl.startsWith('http') || imageUrl.startsWith('https')) {
    return Container(
      height: 50,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
      ),
      child: Image.network(
        imageUrl,
        height: 50,
        width: 50,
        fit: BoxFit.fitHeight,
        filterQuality: FilterQuality.high,
        errorBuilder: (context, error, stackTrace) {
          return Image.asset(
            AppConstants.profileLogoImagePath,
            height: 50,
            width: 50,
            fit: BoxFit.cover,
            filterQuality: FilterQuality.high,
          );
        },
      ),
    );
  } else {
    return Image.asset(
      AppConstants.profileLogoImagePath,
      height: 50,
      width: 50,
      fit: BoxFit.cover,
      filterQuality: FilterQuality.high,
    );
  }
}

class CustomCachedNetworkImage extends StatelessWidget {
  final String? imageUrl, errorImage;
  final double width, height;
  final bool isPrfileVisible;

  const CustomCachedNetworkImage({
    super.key,
    required this.imageUrl,
    required this.width,
    required this.height,
    required this.errorImage,
    this.isPrfileVisible = false,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl ?? '',
      // cacheKey: imageUrl,
      width: width,
      height: height,
      imageBuilder: (context, imageProvider) => Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: imageProvider,
            fit: BoxFit.fill,
          ),
        ),
      ),
      placeholder: (context, url) => const Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryColor,
          strokeWidth: 1,
        ),
      ),
      errorWidget: (context, url, error) => Image.asset(
        errorImage ?? '',
        fit: BoxFit.fill,
      ),
    );
  }
}
