import 'dart:async';

import 'package:flutter/material.dart';

class MarqueeList extends StatefulWidget {
  const MarqueeList({
    super.key,
    required this.children,
    this.scrollDuration = const Duration(milliseconds: 1500),
    this.pauseDuration = const Duration(seconds: 1),
    this.scrollDirection = Axis.horizontal,
  });

  /// The list of widgets to be scrolled.
  final List<Widget> children;

  /// The duration of each scroll animation.
  final Duration scrollDuration;

  /// The duration to pause at the end before restarting.
  final Duration pauseDuration;

  /// The direction in which the children will be scrolled.
  final Axis scrollDirection;

  @override
  State createState() => _MarqueeListState();
}

class _MarqueeListState extends State<MarqueeList> {
  late final ScrollController _scrollController;
  double _scrollPosition = 0.0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startScrolling();
    });
  }

  void _startScrolling() {
    if (!mounted) return; // Check if the widget is mounted before proceeding
    // final size = _itemSize;

    _timer = Timer.periodic(widget.scrollDuration, (_) {
      if (_isScrollRemain) {
        _moveForward();
      } else {
        _resetAndPause();
      }
    });
  }

  bool get _isScrollRemain =>
      _scrollController.position.pixels <
      _scrollController.position.maxScrollExtent;

  double get _itemSize => _scrollDirection == Axis.horizontal
      ? context.size?.width ?? 0.0
      : context.size?.height ?? 0.0;

  void _moveForward() {
    _scrollPosition += 100;
    if (mounted) {
      // Ensure the widget is mounted before starting the animation
      _scrollController.animateTo(
        _scrollPosition,
        duration: widget.scrollDuration,
        curve: Curves.linear,
      );
    }
  }

  void _resetAndPause() {
    _timer?.cancel();
    _scrollController.jumpTo(_scrollController.position.minScrollExtent);
    _scrollPosition = 0.0;
    // CHECK IF THE CHILDREN LIST IS EMPTY BEFORE STARTING SCROLLING
    if (widget.children.isEmpty) {
      return;
    }

    Future.delayed(widget.pauseDuration, () {
      if (mounted) {
        _startScrolling();
      }
    });
  }

  Axis get _scrollDirection => widget.scrollDirection;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      scrollDirection: _scrollDirection,
      controller: _scrollController,
      child: switch (_scrollDirection) {
        Axis.horizontal => Row(children: widget.children),
        Axis.vertical => Column(children: widget.children),
      },
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }
}
