import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/app_version_controller.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class VersionDisplay extends StatelessWidget {
  final double fontSize;
  final Color? textColor;

  const VersionDisplay({
    super.key,
    this.fontSize = 18,
    this.textColor = AppConstants.primaryColor,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AppVersionProvider>(
      builder: (context, versionProvider, _) {
        if (!versionProvider.isLoaded) {
          return const SizedBox.shrink();
        }

        return Text(
          '${versionProvider.appName} v${versionProvider.versionString}',
          style: lbBold.copyWith(
            fontSize: fontSize,
            color: textColor,
          ),
        );
      },
    );
  }
}
