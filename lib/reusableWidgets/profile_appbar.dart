import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProfileAppBar extends StatefulWidget {
  final String? userName;
  final String? userHandle;
  final bool isOpenToClubInvitation;
  final bool userOwnProfile;
  final String? userProfilePicture;
  const ProfileAppBar({
    super.key,
    this.userName,
    this.userHandle,
    this.isOpenToClubInvitation = false,
    this.userOwnProfile = false,
    this.userProfilePicture,
  });

  @override
  State<ProfileAppBar> createState() => _ProfileAppBarState();
}

class _ProfileAppBarState extends State<ProfileAppBar> {
  @override
  void initState() {
    print("isOpenInvitation : ${widget.isOpenToClubInvitation}");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    String image = Config.imageBaseUrl;
    String fullImageUrl = image + (widget.userProfilePicture ?? '');
    return AppBar(
      backgroundColor: AppConstants.textGreenColor,
      leading: Padding(
        padding: const EdgeInsets.only(left: 20.0, top: 10),
        child: NetworkAwareTap(
          onTap: () {
            GoRouter.of(context).pop();
            // context.pop();
          },
          child: SvgPicture.asset(
            "assets/icons/svg/Back.svg",
            width: 73,
            height: 65,
            fit: BoxFit.contain,
          ),
        ),
      ),
      centerTitle: true,
      title: Padding(
        padding: const EdgeInsets.only(top: 10.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: CustomCachedNetworkImage(
                imageUrl: fullImageUrl,
                width: 45,
                height: 45,
                errorImage: AppConstants.profileLogoImagePath,
              ),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.5,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MarqueeList(
                      children: [
                        Text(
                          widget.userName ?? '',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: lbBold.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            widget.userHandle ?? '',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: lbBold.copyWith(
                              fontSize: 14,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (widget.isOpenToClubInvitation)
                          Image.asset(
                            AppConstants.openToInvitationImagePath,
                            height: 15,
                            width: 15,
                          )
                        else if (!widget.isOpenToClubInvitation &&
                            widget.userOwnProfile)
                          Image.asset(
                            AppConstants.notOpenToInvitationImagePath,
                            height: 20,
                            width: 20,
                          )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 20.0, top: 10),
          child: GestureDetector(
            onTap: () {
              showDialog(
                context: context,
                barrierColor: Colors.white60,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return const QuestionFeedbackDialog();
                },
              );
            },
            child: Skeleton.replace(
              replacement: ClipRRect(
                borderRadius: BorderRadius.circular(49),
                child: Image.asset(
                  AppConstants.questionLogoImagePath,
                  height: 34,
                  width: 34,
                ),
              ),
              child: Image.asset(
                AppConstants.questionLogoImagePath,
                height: 34,
                width: 34,
              ),
            ),
          ),
        )
      ],
    );
  }
}
