import 'package:eljunto/constants/constants.dart';
import 'package:flutter/material.dart';

class BackgroundScaffold extends StatelessWidget {
  final Widget child;

  const BackgroundScaffold({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(
            AppConstants.bgImagePath,
          ),
          filterQuality: FilterQuality.high,
          fit: BoxFit.fitWidth,
        ),
      ),
      child: child,
    );
  }
}
