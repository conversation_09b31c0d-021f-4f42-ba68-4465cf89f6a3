import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:flutter/material.dart';

class ProfileAppBarWithLogo extends StatefulWidget {
  final String? userName;
  final String? handle;
  final int? index;
  final bool isOpenToInvitation;
  const ProfileAppBarWithLogo({
    super.key,
    this.userName,
    this.handle,
    this.index,
    this.isOpenToInvitation = true,
  });

  @override
  State<ProfileAppBarWithLogo> createState() => _ProfileAppBarWithLogoState();
}

class _ProfileAppBarWithLogoState extends State<ProfileAppBarWithLogo> {
  @override
  Widget build(BuildContext context) {
    final invitationImage = widget.isOpenToInvitation
        ? "assets/icons/Open_to_Invitation.png"
        : "assets/icons/NotOpenToInvitation.png";

    return AppBar(
      backgroundColor: AppConstants.textGreenColor,
      leading: Padding(
        padding: const EdgeInsets.only(left: 20.0, top: 10),
        child: Image.asset(
          "assets/images/eljunto_logo_dark_teal.png",
          fit: BoxFit.cover,
          filterQuality: FilterQuality.high,
          alignment: Alignment.center,
        ),
      ),
      actions: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 10.0, left: 40),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  "assets/icons/Profile_2.png",
                  height: 38,
                  width: 44,
                  filterQuality: FilterQuality.high,
                  fit: BoxFit.contain,
                ),
                const SizedBox(
                  width: 7.5,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.userName ?? '',
                      style: lbBold.copyWith(
                        fontSize: 18,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          widget.handle ?? "",
                          style: lbBold.copyWith(
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(
                          width: 8,
                        ),
                        Image.asset(
                          invitationImage,
                          height: 20,
                          width: 20,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 25.0, top: 10),
          child: NetworkAwareTap(
            onTap: () {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return const QuestionFeedbackDialog();
                },
              );
            },
            child: Image.asset(
              AppConstants.questionLogoImagePath,
              height: 34,
              width: 34,
            ),
          ),
        )
      ],
    );
  }
}
