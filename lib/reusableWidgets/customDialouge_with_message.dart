import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class CustomDialog extends StatelessWidget {
  final String title;
  final String message;
  final bool showDoneImage;
  final String? subTitle;
  final String? subMessage;
  final bool incomingClubFont;
  //final VoidCallback onOkPressed;

  const CustomDialog({
    super.key,
    required this.title,
    required this.message,
    this.showDoneImage = false,
    this.subTitle = '',
    this.subMessage = '',
    this.incomingClubFont = false,
    //required this.onOkPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      actionsPadding: const EdgeInsets.only(right: 10),
      insetPadding: const EdgeInsets.all(25),
      contentPadding: EdgeInsets.zero,
      backgroundColor: AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(
          color: AppConstants.popUpBorderColor,
          width: 1.5,
        ),
      ),
      surfaceTintColor: Colors.white,
      actions: [
        Column(
          children: [
            NetworkAwareTap(
              onTap: () {
                context.pop();
              },
              child: Container(
                alignment: Alignment.centerRight,
                padding: const EdgeInsets.only(top: 10),
                child: Image.asset(
                  AppConstants.closePopupImagePath,
                  height: 30,
                  width: 30,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 30.0, right: 20),
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 18,
                  ),
                ),
              ),
            ),
            if (subTitle != '') ...[
              const SizedBox(height: 25),
              Padding(
                padding: const EdgeInsets.only(left: 30.0, right: 20),
                child: Text(
                  subTitle!,
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                  ),
                ),
              ),
            ],
            if (showDoneImage) ...[
              const SizedBox(height: 28),
              Padding(
                padding: const EdgeInsets.only(left: 30.0, right: 20),
                child: Image.asset(
                  AppConstants.requestDoneImg,
                  filterQuality: FilterQuality.high,
                  fit: BoxFit.cover,
                  height: 79,
                  width: 79,
                ),
              ),
            ],
            const SizedBox(height: 25),
            Padding(
              padding: const EdgeInsets.only(left: 30.0, right: 20),
              child: Text(
                message,
                textAlign: TextAlign.center,
                style: lbRegular.copyWith(
                  fontSize: incomingClubFont ? 12 : 18,
                ),
              ),
            ),
            const SizedBox(height: 25),
            if (subMessage != '') ...[
              Padding(
                padding: const EdgeInsets.only(left: 30.0, right: 20),
                child: SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Text(
                    subMessage!,
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: incomingClubFont ? 12 : 18,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 25),
            ],
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                NetworkAwareTap(
                  //onTap: onOkPressed,
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 30),
          ],
        )
      ],
    );
  }
}
