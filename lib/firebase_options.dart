// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBEyuMR7owb378lS0F-sLwAGMXRZYmwJ_g',
    appId: '1:953461406948:web:22cd9418d8b00d513740f1',
    messagingSenderId: '953461406948',
    projectId: 'el-junto-4fb80',
    authDomain: 'el-junto-4fb80.firebaseapp.com',
    storageBucket: 'el-junto-4fb80.firebasestorage.app',
    measurementId: 'G-MCE2YX5XJY',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDPHGLrIGYtO3iAl2z3ZtZMCc_LzB0dNAY',
    appId: '1:953461406948:android:2131dbf3f99170e33740f1',
    messagingSenderId: '953461406948',
    projectId: 'el-junto-4fb80',
    storageBucket: 'el-junto-4fb80.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDRhlHw3Ar9ESqJG3a3lm_nH8GKvQrCApg',
    appId: '1:953461406948:ios:c42816b29074855b3740f1',
    messagingSenderId: '953461406948',
    projectId: 'el-junto-4fb80',
    storageBucket: 'el-junto-4fb80.firebasestorage.app',
    iosBundleId: 'com.eljunto',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDRhlHw3Ar9ESqJG3a3lm_nH8GKvQrCApg',
    appId: '1:953461406948:ios:3afd27ef9cdcc2ad3740f1',
    messagingSenderId: '953461406948',
    projectId: 'el-junto-4fb80',
    storageBucket: 'el-junto-4fb80.firebasestorage.app',
    iosBundleId: 'com.example.eljuntobookclub',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBEyuMR7owb378lS0F-sLwAGMXRZYmwJ_g',
    appId: '1:953461406948:web:b4ec8d7186029aee3740f1',
    messagingSenderId: '953461406948',
    projectId: 'el-junto-4fb80',
    authDomain: 'el-junto-4fb80.firebaseapp.com',
    storageBucket: 'el-junto-4fb80.firebasestorage.app',
    measurementId: 'G-CHR7EB6P5Q',
  );
}
