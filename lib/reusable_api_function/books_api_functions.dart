import 'dart:developer';

import 'package:eljunto/controller/profile_controller.dart';
import 'package:eljunto/models/profile_model/edit_bookcase/listof_book_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class BooksApiFunctions {
  static Future<List<Books>> fetchBooks(
    String query,
    int offset,
    int limit,
    BuildContext context,
    int? loggedinUserId,
  ) async {
    try {
      final profileController =
          Provider.of<ProfileController>(context, listen: false);
      final results = await profileController.allBookFunction(
        loggedinUserId ?? 0,
        query.trim().toLowerCase(),
        offset,
        limit,
        context,
      );

      return results ?? [];
    } catch (e) {
      log("Error fetching books: $e");
      return [];
    }
  }
}
