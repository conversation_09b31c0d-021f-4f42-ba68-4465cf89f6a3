import 'package:eljunto/models/home_model/home_screen1_model/new_club_opening_model.dart';
import 'package:flutter/material.dart';

class HomeController with ChangeNotifier {
  int offset = 0;
  int clubOpeningLimit = 10;
  int fellowReadersLimit = 10;
  bool clubOpenigLoading = true;
  bool fellowReadersLoading = true;
  int clubOpeningCount = 0;
  int fellowReadersCount = 0;
  bool isLoading = false;
  List<NewClubOpenModel> clubList = [];

  // Future<void> getNewClubOpenings(
  //     bool isMore, int loggedinUserId, BuildContext context) async {
  //   if (clubList.length <= clubOpeningCount && isMore) {
  //     isLoading = true;
  //     clubOpeningLimit =
  //         clubOpeningLimit + 10; // Increment the limit by 10 for the next load
  //     notifyListeners();
  //   }

  //   await Provider.of<BookClubController>(context, listen: false)
  //       .getNewClubs(loggedinUserId ?? 0, offset, clubOpeningLimit, context)
  //       .then((responseMap) {
  //     if (responseMap["statusCode"] == 200) {
  //       clubOpeningCount = responseMap['count'];
  //       final fetchClubList = (responseMap["data"] as List)
  //           .map((item) => NewClubOpenModel.fromJson(item))
  //           .toList();
  //       if (fetchClubList.isNotEmpty) {
  //         clubList = fetchClubList;
  //       }
  //       isLoading = false;
  //       clubOpenigLoading = fetchClubList.length >= clubOpeningCount;
  //       notifyListeners(); // Update the UI
  //     } else {}
  //   });
  //   void refreshData() {
  //     clubList.clear();
  //     clubOpeningLimit = 10; // Reset limit
  //     notifyListeners(); // Trigger UI update to reload data
  //   }
  // }
}
