import 'dart:developer';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ClubController with ChangeNotifier {
  List<BookClubModel>? standingBookClubList = [];
  List<BookClubModel>? impromptuBookClubList = [];
  List<BookClubModel>? allClub = [];
  int standingClubCount = 0;
  int impromptuClubCount = 0;
  int allClubCount = 0;
  int offSet = 0;
  int standingLimit = 10;
  int impromptuLimit = 10;
  int allClubLimit = 10;
  bool standingLoading = false;
  bool impromptuLoading = false;
  bool allClubLoading = false;

  Future<void> getBookClubsByUserId(BuildContext context, int loggedinUserId,
      String bookClubType, bool isMore) async {
    if (bookClubType == ClubType.standing) {
      if ((standingBookClubList?.length ?? 0) <= standingClubCount || !isMore) {
        // isLoading = true;
        standingLoading = true;
        notifyListeners();

        if (isMore) {
          standingLimit += 10;
        }
        log("Standing Limit : $standingLimit");
        // standingLimit = standingLimit + 10;
      } // Increment the limit by 10 for the next load
      await Provider.of<BookClubController>(context, listen: false)
          .getBookClubs(
        bookClubType,
        loggedinUserId,
        null,
        context,
        offSet,
        standingLimit,
      )
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookClubModel> bookClubList = [];

          if (responseMap["count"] != 0) {
            standingClubCount = responseMap['count'];
            print("Standing Counttttttt : $standingClubCount");
          } else {
            standingClubCount = 0;
          }
          if (responseMap["data"].isNotEmpty) {
            bookClubList = (responseMap["data"] as List)
                .map((item) => BookClubModel.fromJson(item))
                .toList();
            standingBookClubList = bookClubList;
            // notifyListeners();
          } else {
            standingBookClubList?.clear();
            standingBookClubList = [];
          }
          log("Standing Book Club Count : $standingClubCount");
        } else {
          standingBookClubList = [];
        }
      }).whenComplete(() {
        standingLoading = false;
        notifyListeners();
      });
    } else if (bookClubType == ClubType.impromptu) {
      log("Impromptu Count : $impromptuClubCount");

      log("Impromptu List : ${impromptuBookClubList?.length}");
      if ((impromptuBookClubList?.length ?? 0) <= impromptuClubCount ||
          !isMore) {
        impromptuLoading = true;
        notifyListeners();

        if (isMore) {
          impromptuLimit += 10;
        }
      }
      final bookClubController =
          Provider.of<BookClubController>(context, listen: false);

      if (!bookClubController.hasListeners) return;
      bookClubController
          .getBookClubs(
        bookClubType,
        loggedinUserId,
        null,
        context,
        offSet,
        impromptuLimit,
      )
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookClubModel> bookClubList = [];

          if (responseMap["count"] != 0) {
            impromptuClubCount = responseMap['count'];
          } else {
            impromptuClubCount = 0;
          }
          if (responseMap["data"].isNotEmpty) {
            bookClubList = (responseMap["data"] as List)
                .map((item) => BookClubModel.fromJson(item))
                .toList();
            impromptuBookClubList = bookClubList;
          } else {
            impromptuBookClubList?.clear();
            impromptuBookClubList = [];
          }

          log("Impromptu Count : $impromptuClubCount");
          log("Impromptu List : ${impromptuBookClubList?.length}");
        } else {
          impromptuBookClubList = [];
        }
      }).whenComplete(() {
        if (bookClubController.hasListeners) {
          impromptuLoading = false;
          notifyListeners();
        }
      });
    } else {
      log("Club Count : $allClubCount");
      log("All Club List : ${allClub?.length}");
      if ((allClub?.length ?? 0) <= allClubCount || !isMore) {
        allClubLoading = true;
        notifyListeners();

        if (isMore) {
          allClubLimit += 10;
        }
      }
      await Provider.of<BookClubController>(context, listen: false)
          .getBookClubs(
        bookClubType,
        loggedinUserId,
        null,
        context,
        offSet,
        allClubLimit,
      )
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookClubModel> bookClubList = [];

          if (responseMap["count"] != 0) {
            allClubCount = responseMap['count'];
          } else {
            allClubCount = 0;
          }
          if (responseMap["data"].isNotEmpty) {
            bookClubList = (responseMap["data"] as List)
                .map((item) => BookClubModel.fromJson(item))
                .toList();
            allClub = bookClubList;
            // notifyListeners();
          } else {
            allClub?.clear();
            allClub = [];
          }
        } else {
          allClub = [];
        }
      }).whenComplete(() {
        allClubLoading = false;
        notifyListeners();
      });
    }
  }

  // Reset the state
  void resetState() {
    standingBookClubList = [];
    impromptuBookClubList = [];
    allClub = [];
    standingClubCount = 0;
    impromptuClubCount = 0;
    allClubCount = 0;
    standingLimit = 10;
    impromptuLimit = 10;
    allClubLimit = 10;
    standingLoading = false;
    impromptuLoading = false;
    allClubLoading = false;
    notifyListeners();
  }
}
