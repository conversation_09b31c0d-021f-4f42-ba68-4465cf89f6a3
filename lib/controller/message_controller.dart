import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:eljunto/services/message_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:synchronized/synchronized.dart';

import '../models/book_club_model.dart';
import '../views/local_database.dart';
import 'book_club_controller.dart';

class MessageController with ChangeNotifier {
  final _lock = Lock();
  String errorMessage = '';
  final MessageService messageService = MessageService();
  StreamSubscription<List<DocumentSnapshot>>? _groupsStreamSubscription;

  Map<int, bool> notificationStatus = {}; // To track notification status
  bool hasNewNotification = false;

  late List<int?> _userBookClubIds = [];
  List<int?> get userBookClubIds => _userBookClubIds;
  int batchSize = 10;
  bool isFirstLoading = true;
  List<DocumentSnapshot<Object?>> _documentList = [];
  List<DocumentSnapshot<Object?>> get documentList => _documentList;

  Stream<List<DocumentSnapshot>> getGroupsStream(
      BuildContext context, int loggedinUserId) {
    if (_userBookClubIds.isEmpty) {
      isFirstLoading = false;
      // notifyListeners();
      return const Stream.empty();
    }
    receiveMetadata(context, loggedinUserId);

    final chunk = _userBookClubIds.sublist(0, _userBookClubIds.length);

    Stream<List<DocumentSnapshot>> firestoreStream = FirebaseFirestore.instance
        .collection('Club_Collection')
        .where('bookClubId', whereIn: chunk)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((querySnapshot) => querySnapshot.docs);

    _groupsStreamSubscription = firestoreStream.listen((documentList) async {
      _documentList = documentList;
      isFirstLoading = false;

      // Create a temporary map to store notification statuses
      Map<int, bool> tempNotificationStatus =
          Map<int, bool>.from(notificationStatus);

      for (var doc in documentList) {
        final data = doc.data() as Map<String, dynamic>;
        final clubId = data['bookClubId'];
        try {
          final messages = await doc.reference
              .collection('messages')
              .orderBy('createdAt', descending: true)
              .limit(1)
              .get();

          if (messages.docs.isNotEmpty) {
            final latestMessage = messages.docs.first;
            final seenByList = latestMessage['seenBy'] as List<dynamic>? ?? [];
            final userSeenData = seenByList.firstWhere(
              (entry) => entry['user_id'] == loggedinUserId,
              orElse: () => null,
            );
            final isSeen = userSeenData?['isSeen'] ?? false;
            tempNotificationStatus[clubId] = isSeen;
          }
        } catch (e) {
          debugPrint("Error processing club $clubId: $e");
        }
      }

      notificationStatus = tempNotificationStatus;

      final bookClubIdsCopy = List<int?>.from(_userBookClubIds);
      hasNewNotification = bookClubIdsCopy.any(
        (e) => notificationStatus[e] == false,
      );
      notifyListeners();
    });
    return firestoreStream;
  }

  Future<void> receiveMetadata(BuildContext context, int loggedinUserId) async {
    try {
      if (_userBookClubIds.isEmpty) {
        return;
      }

      await _lock.synchronized(() async {
        final batch = <Future>[];
        for (var clubId in _userBookClubIds) {
          batch.add(getLatestMessageSeenBy(
              clubId.toString(), clubId ?? 0, loggedinUserId, context));
        }
        await Future.wait(batch);
      });
    } catch (e) {
      log('Error fetching metadata: $e');
    }
  }

  Future<void> getLatestMessageSeenBy(String clubId, int userClubId,
      int loggedinUserId, BuildContext context) async {
    try {
      // bool localSeenStatus =
      //     await DatabaseHelper.instance.getSeenStatus(loggedinUserId, clubId);

      bool localSeenStatus = false;
      try {
        localSeenStatus =
            await DatabaseHelper.instance.getSeenStatus(loggedinUserId, clubId);
      } catch (e) {
        if (e.toString().contains('database_closed')) {
          // Reinitialize database if it's closed
          await DatabaseHelper.instance.database;
          // Retry once after reinitialization
          try {
            localSeenStatus = await DatabaseHelper.instance
                .getSeenStatus(loggedinUserId, clubId);
          } catch (retryError) {
            // If still failing, default to false and continue
            debugPrint("Error retrying database access: $retryError");
          }
        } else {
          debugPrint("Error accessing database: $e");
        }
      }

      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection('Club_Collection')
          .doc(clubId)
          .collection('messages')
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        // log("No messages found.");
        return;
      }

      var latestMessage = querySnapshot.docs.first;

      if (!(latestMessage.data() as Map<String, dynamic>)
          .containsKey('seenBy')) {
        log("Missing 'seenBy' field in latest message for clubId: $clubId");
        return;
      }
      List<dynamic>? seenByList = latestMessage['seenBy'] as List<dynamic>?;

      if (seenByList == null || seenByList.isEmpty) {
        // log("No seenBy data found.");
        return;
      }

      var userSeenData = seenByList.firstWhere(
        (entry) => entry['user_id'] == loggedinUserId,
        orElse: () => null,
      );

      bool isSeenFirestore = userSeenData?['isSeen'] ?? false;
      // log("isSeenFirestore : $isSeenFirestore");

      notificationStatus[userClubId] = localSeenStatus;

      hasNewNotification =
          notificationStatus.values.any((isSeen) => isSeen == false);
      notifyListeners();

      if (context.mounted) {
        final messageController =
            Provider.of<MessageController>(context, listen: false);

        if (messageController.hasListeners) {
          // log("In hasListeners : ${MessageController.hasListeners}");
          messageController.unSeenClubMessages(hasNewNotification);
        }
      }

      notifyListeners();

      if (localSeenStatus != isSeenFirestore) {
        await DatabaseHelper.instance
            .insertOrUpdateSeenStatus(loggedinUserId, clubId, isSeenFirestore);

        notificationStatus[userClubId] = isSeenFirestore;
        notifyListeners();
      }
    } catch (e, stackTrace) {
      debugPrint("Error fetching seen status: $e\n$stackTrace");
    }
  }

  Future<void> markMessageAsSeen(
    String clubId,
    int userClubId,
    int loggedinUserId,
    BuildContext context,
  ) async {
    try {
      // Check if message is already marked as seen locally
      if (notificationStatus[userClubId] == true) {
        return; // Exit if already seen
      }

      // Immediately update local state and UI
      notificationStatus[userClubId] = true;
      hasNewNotification =
          notificationStatus.values.any((isSeen) => isSeen == false);
      notifyListeners(); // Notify UI immediately

      // Update local database
      await DatabaseHelper.instance
          .insertOrUpdateSeenStatus(loggedinUserId, clubId, true);

      // Check Firebase current status before updating
      final querySnapshot = await FirebaseFirestore.instance
          .collection('Club_Collection')
          .doc(clubId)
          .collection('messages')
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        var latestMessage = querySnapshot.docs.first;
        List<dynamic> seenByList = latestMessage['seenBy'] ?? [];

        // Check if user has already seen this message in Firebase
        var userSeenData = seenByList.firstWhere(
          (entry) => entry['user_id'] == loggedinUserId,
          orElse: () => null,
        );

        if (userSeenData != null && userSeenData['isSeen'] == true) {
          return; // Exit if already marked as seen in Firebase
        }

        // Update or add user's seen status
        int existingIndex = seenByList
            .indexWhere((entry) => entry['user_id'] == loggedinUserId);
        if (existingIndex != -1) {
          seenByList[existingIndex]['isSeen'] = true;
          seenByList[existingIndex]['timestamp'] =
              DateTime.now().millisecondsSinceEpoch;
        } else {
          seenByList.add({
            'user_id': loggedinUserId,
            'isSeen': true,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          });
        }

        await latestMessage.reference.update({'seenBy': seenByList});
      }
    } catch (e) {
      debugPrint("Error updating seen status: $e");
    }
  }

  List<BookClubModel>? messagesBookclubList = [];
  Future<void> getAllBookClubsByUserId(
      bool isMore, BuildContext context, int loggedInUserId) async {
    try {
      await Provider.of<BookClubController>(context, listen: false)
          .getBookClubs(
        '',
        loggedInUserId,
        null,
        context,
        null,
        null,
      )
          .then((responseMap) async {
        if (responseMap["statusCode"] == 200) {
          List<BookClubModel> bookClubList = [];

          if (responseMap["data"].isNotEmpty) {
            bookClubList = (responseMap["data"] as List)
                .map((item) => BookClubModel.fromJson(item))
                .toList();
            log("BookClubsIds List : ${bookClubList.length}");
            messagesBookclubList = bookClubList;
            _userBookClubIds.clear();
            _userBookClubIds =
                messagesBookclubList?.map((e) => e.bookClubId).toList() ?? [];
            notifyListeners();
          } else {
            messagesBookclubList?.clear();
            messagesBookclubList = [];
            _userBookClubIds = [];
          }
        } else {}
      });
    } catch (e) {
      log("Error : $e");
    }
  }

  void disposeGroupsStream() {
    _groupsStreamSubscription?.cancel();
    _groupsStreamSubscription = null;
    _documentList.clear();
    notifyListeners();
  }

  void logoutUser() {
    disposeGroupsStream();
    _userBookClubIds.clear();
    messagesBookclubList?.clear();
    hasNewNotification = false;
    notifyListeners();
  }

  bool _incomingClubInviteFlag = false;
  bool _hasNewOutgoingRequests = false;
  bool _hasNewStandingClubRequests = false;
  bool _hasNewImpromptuClubRequests = false;
  bool _hasUnSeenClubMessages = false;
  bool _manageIncomingRequestFlag = false;

  bool get hasNewNotifications => (_incomingClubInviteFlag ||
      _hasNewOutgoingRequests ||
      _hasNewStandingClubRequests ||
      _hasNewImpromptuClubRequests ||
      _manageIncomingRequestFlag);

  bool get isPendingInvitation => _incomingClubInviteFlag;
  bool get isOutgoingRequest => _hasNewOutgoingRequests;
  bool get isUnSeenClubMessages => _hasUnSeenClubMessages;
  bool get manageIncomingRequest => _manageIncomingRequestFlag;
  bool get hasNewStandingClubRequest => _hasNewStandingClubRequests;

  Future<void> incomingClubInvitationStatus(bool status) async {
    _incomingClubInviteFlag = status;
    log("Incoming Club Invite Status : $_incomingClubInviteFlag");
    notifyListeners();
  }

  Future<void> manageIncomingRequestStatus(
      bool status, BuildContext context) async {
    _manageIncomingRequestFlag = status;
    log("Manage Incoming Request Status : $_manageIncomingRequestFlag");
    notifyListeners();
  }

  Future<void> updateOutgoingRequests(bool status) async {
    _hasNewOutgoingRequests = status;
    notifyListeners();
  }

  Future<void> updateStandingClubRequests(bool status) async {
    _hasNewStandingClubRequests = status;
    notifyListeners();
  }

  Future<void> updateImpromptuClubRequests(bool status) async {
    _hasNewImpromptuClubRequests = status;
    notifyListeners();
  }

  Future<void> unSeenClubMessages(bool status) async {
    _hasUnSeenClubMessages = status;
    notifyListeners();
  }

  bool isInvalidLink = false;
  Future<void> isInvalidDeeplink(bool value) async {
    isInvalidLink = value;
    log("is valid deeplink : $isInvalidLink");
    notifyListeners();
  }
}
