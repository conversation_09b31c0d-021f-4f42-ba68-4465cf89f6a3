import 'package:flutter/material.dart';

class NotificationController with ChangeNotifier {
  // bool isInvalidLink = false;
  // Future<void> isInvalidDeeplink(bool value) async {
  //   isInvalidLink = value;
  //   log("is valid deeplink : $isInvalidLink");
  //   // notifyListeners();
  // }

  // // New method to check and reset in one operation
  // bool checkAndResetInvalidDeeplink() {
  //   final currentValue = isInvalidLink;
  //   if (currentValue) {
  //     isInvalidLink = false;
  //     notifyListeners();
  //   }
  //   return currentValue;
  // }
}
