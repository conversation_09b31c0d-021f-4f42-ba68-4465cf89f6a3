import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import 'package:eljunto/models/clubs_model/clubs_screen4_model/manage__meeting_model.dart';
import 'package:eljunto/models/home_model/home_screen2_model/meeting_model.dart';
import 'package:eljunto/services/book_club_service.dart';
import 'package:flutter/material.dart';

import '../constants/common_helper.dart';
import '../models/home_model/home_screen1_model/new_club_opening_model.dart';
import '../models/meeting_join_model.dart';

class BookClubController with ChangeNotifier {
  final BookClubService bookClubService = BookClubService();
  String errorMessage = '';

  bool isnewClubLoading = false;
  List<NewClubOpeningList> clubList = [];
  int clubOpeningCount = 0;

  /// ACCESS LIST ON BOTTOM SHEET
  List<NewClubOpeningList> _openingClubs = [];

  List<NewClubOpeningList> get openingList => _openingClubs;

  Future<void> getNewClubs(
      int userId, int offset, int limit, BuildContext context) async {
    isnewClubLoading = true;
    notifyListeners();

    try {
      // Make the API call
      dio.Response response =
          await bookClubService.getNewClubs(userId, '', offset, limit);

      if (response.statusCode == 200) {
        final responseObject = response.data;
        // log("Response Object : $responseObject");

        // Parse the full response into the model
        final newClubOpenModel = NewClubOpenModel.fromJson(responseObject);
        log("New Club Open Model : $newClubOpenModel");

        // Assign the data
        clubOpeningCount = newClubOpenModel.count ?? 0;
        clubList = newClubOpenModel.data ?? [];
        _openingClubs = clubList;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        // Handle unauthorized response
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
      } else {
        // Handle other non-200 responses
        clubList = [];
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';

        // errorMessage = 'Something went wrong';
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      // Handle exceptions
      clubList = [];
      errorMessage = 'Something went wrong';
      log(errorMessage);
    } finally {
      // Final cleanup
      isnewClubLoading = false;
      notifyListeners();
    }
  }

  Future<Map<String, dynamic>> getBookClubMembers(
      int bookClubId, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response =
          await bookClubService.getBookClubMembers(bookClubId);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    } finally {
      notifyListeners();
    }
    return responseMap;
  }

  bool isUpcomingMeetingLoader = false;
  MeetingModel? upcomingMeetingModel;
  Future<void> getBookClubUpcomingMeetings(
      int bookClubId, int limit, int offSet, BuildContext context) async {
    isUpcomingMeetingLoader = true;
    notifyListeners();
    try {
      dio.Response response = await bookClubService.getBookClubUpcomingMeetings(
          bookClubId, limit, offSet);

      if (response.statusCode == 200) {
        final responseObject = response.data;
        if (responseObject.containsKey('data') &&
            responseObject['data'] != null) {
          upcomingMeetingModel = MeetingModel.fromJson(responseObject);
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        upcomingMeetingModel = null;
        log("Failed to fetch meeting data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      upcomingMeetingModel = null;
      log(e.toString());
    }
    isUpcomingMeetingLoader = false;
    notifyListeners();
  }

  bool isPreviousMeetingLoader = false;
  MeetingModel? previousMeetingModel;
  Future<void> getBookClubPreviousMeetings(
      int bookClubId, int limit, int offSet, BuildContext context) async {
    isPreviousMeetingLoader = true;
    // notifyListeners();
    try {
      dio.Response response = await bookClubService.getBookClubPreviousMeetings(
          bookClubId, limit, offSet);

      if (response.statusCode == 200) {
        final responseObject = response.data;
        if (responseObject.containsKey('data') &&
            responseObject['data'] != null) {
          previousMeetingModel = MeetingModel.fromJson(responseObject);
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        previousMeetingModel = null;
        log("Failed to fetch meeting data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      previousMeetingModel = null;
      log(e.toString());
    }
    isPreviousMeetingLoader = false;
    notifyListeners();
  }

  bool isLeaveClub = false;
  Future<void> leaveClub(bool value) async {
    isLeaveClub = value;
    notifyListeners();
    // return true;
  }

  Map<String, dynamic> _fetchedData = {};

  Map<String, dynamic>? get fetchedData => _fetchedData;
  bool isBookClubLoading = false;

  Future<Map<String, dynamic>> getBookClubs(
      String? bookClubType, int? userId, int? bookClubId, BuildContext context,
      [int? offSet, int? limit]) async {
    // Map<String, dynamic> responseMap = {};
    isBookClubLoading = true;
    // notifyListeners();
    try {
      // print("API CALL 3");
      dio.Response response = await bookClubService.getBookClubs(
          bookClubType, userId, bookClubId, offSet, limit);

      if (response.statusCode == 200) {
        _fetchedData = response.data;
      } else if (response.statusCode == 401) {
        if (context.mounted) {
          Map<String, dynamic> payload = {};
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        log("Invite Error Message : ${jsonResponse['message']}");
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        _fetchedData = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log("Standing Club : $errorMessage");
      _fetchedData = {"error": errorMessage};
    }
    isBookClubLoading = false;
    notifyListeners();
    return _fetchedData;
  }

  Future<Map<String, dynamic>> addMember(
      Map<String, dynamic> payload, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response = await bookClubService.addMember(payload);
      log("Response Data : ${response.data}");

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else {
        // final jsonResponse = response.data;
        final jsonResponse = response.data;
        log("Invite ERROR Message : ${jsonResponse['message']}");
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log('Error : $e');
      // log("On DioException : ${e.response?.data}");
      // errorMessage = 'Something went wrong';
      // responseMap = {"error": e.response?.data['message']};
    } finally {
      notifyListeners();
    }
    return responseMap;
  }

  Future<Map<String, dynamic>> getPendingInvittions(String? status, int? userId,
      int? bookClubId, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response = await bookClubService.getPendingInvitations(
          status, userId, bookClubId);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    }
    return responseMap;
  }

  Future<Map<String, dynamic>> updateInvitation(
      RequestManage clubMembershipModel, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response =
          await bookClubService.updateInvitation(clubMembershipModel);

      if (response.statusCode == 200) {
        responseMap = response.data;
        // log("Response : ${responseMap}");
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } on dio.DioException catch (e) {
      log("On DioException : ${e.response?.data}");
      errorMessage = 'Something went wrong';
      responseMap = {"error": e.response?.data['message']};
    } finally {
      notifyListeners();
    }

    // catch (e) {
    //   errorMessage = 'Something went wrong';
    //   log(errorMessage);
    //   responseMap = {"error": errorMessage};
    // }
    return responseMap;
  }

  Future<Map<String, dynamic>> checkAavailabilityOfClubName(
      String bookClubName, int? bookId, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response = await bookClubService
          .checkAavailabilityOfClubName(bookClubName, bookId);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    }
    return responseMap;
  }

  Future<Map<String, dynamic>> addBookClub(BookClubModel newBookClub,
      ManageMeetingModel meetingPayload, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response =
          await bookClubService.addClub(newBookClub, meetingPayload);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        final jsonResponse = response.data;
        errorMessage = jsonResponse['message'] ?? 'Something went wrong';
        // errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    }
    return responseMap;
  }

  bool isMeetingDelete = false;
  Future<bool> deleteMeeting(int meetingId, BuildContext context) async {
    try {
      dio.Response response = await bookClubService.deleteMeeting(meetingId);
      if (response.statusCode == 200) {
        log(response.data);
        isMeetingDelete = true;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        log("Can't delete Meeting : ${response.data}");
        isMeetingDelete = true;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isMeetingDelete = true;
    }
    return isMeetingDelete;
  }

  bool isScheduleMeeting = false;
  Future<bool> scheduleMeeting(
      ManageMeetingModel? payload, BuildContext context) async {
    try {
      dio.Response response = await bookClubService.scheduleMeeting(payload);
      if (response.statusCode == 200) {
        log('Meeting Schedule Successfully');
        isScheduleMeeting = true;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        log("Meeting Schedule Failed : ${response.data}");
        isScheduleMeeting = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isScheduleMeeting = false;
    }
    return isScheduleMeeting;
  }

  bool isUpdateMeeting = false;
  Future<bool> updateMeeting(
      ManageMeetingModel? payload, BuildContext context) async {
    try {
      dio.Response response = await bookClubService.updateMeeting(payload);
      if (response.statusCode == 200) {
        log('Meeting Schedule Successfully');
        isUpdateMeeting = true;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        log("Meeting Schedule Failed : ${response.data}");
        isUpdateMeeting = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isUpdateMeeting = false;
    }
    return isUpdateMeeting;
  }

  IncomingOutGoingRequest? incomingOutGoingRequest;
  Future<void> inComingRequest(int clubId, String pendingStatus,
      String reOpenedStatus, String requestType, BuildContext context) async {
    try {
      dio.Response response = await bookClubService.inComingRequest(
          clubId, pendingStatus, reOpenedStatus, requestType);
      if (response.statusCode == 200) {
        final jsonObj = response.data;
        incomingOutGoingRequest = IncomingOutGoingRequest.fromJson(jsonObj);
        notifyListeners();
        log('Data : $incomingOutGoingRequest');
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        incomingOutGoingRequest = null;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      incomingOutGoingRequest = null;
      log(e.toString());
    }
  }

  bool isAddRemoveMember = false;
  Future<bool> addRemoveOpening(
      bool flag, int bookClubId, BuildContext context) async {
    try {
      dio.Response response =
          await bookClubService.addRemoveOpening(flag, bookClubId);
      if (response.statusCode == 200) {
        log('Request Successful : ${response.data}');
        isAddRemoveMember = true;
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        log('Request Unsuccessful : ${response.data}');
        isAddRemoveMember = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isAddRemoveMember = false;
    }
    return isAddRemoveMember;
  }

  bool isMeetingJoin = false;
  MeetingJoinModel? meetingJoinModel;
  Future<bool> joinMeeting(
      int meetingId, int userId, String channelName) async {
    try {
      dio.Response response =
          await bookClubService.joinMeeting(meetingId, userId, channelName);
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = response.data;

        // Create MeetingJoinModel from parsed JSON
        meetingJoinModel = MeetingJoinModel.fromJson(jsonResponse);
        log("Response : ${meetingJoinModel?.data}");
        log("Meeting Join Successfully");
        isMeetingJoin = true;
      } else {
        log("Failed to join meeting");
        isMeetingJoin = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isMeetingJoin = false;
    }
    return isMeetingJoin;
  }

  bool isMeetingLeave = false;
  Future<bool> agoraLogs(Map<String, dynamic> payload) async {
    try {
      dio.Response response = await bookClubService.agoraLogs(payload);
      if (response.statusCode == 201) {
        isMeetingLeave = true;
        log('Agora Logs : ${response.data}');
      } else {
        isMeetingLeave = false;
        log('Failed to get Agora Logs');
      }
    } on SocketException {
      isMeetingLeave = false;
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      isMeetingLeave = false;
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      isMeetingLeave = false;
      log(e.toString());
    }
    return isMeetingLeave;
  }

  BookClubModel? _bookClubModel;
  BookClubModel? get bookClubModel => _bookClubModel;

  void updateData(BookClubModel clubModel) {
    _bookClubModel = clubModel;
    notifyListeners();
  }

  List<RequestManage>? _incomingRequestList;
  List<RequestManage>? get incomingRequestList => _incomingRequestList;

  void incomingRequestFunction(List<RequestManage>? incomingRequestList) {
    _incomingRequestList = incomingRequestList;
    notifyListeners();
  }

  List<MeetingList>? _upComingMeetingList;
  List<MeetingList>? _previousMeetingList;

  List<MeetingList>? get upComingMeetingList => _upComingMeetingList;
  List<MeetingList>? get previousMeetingList => _previousMeetingList;

  void meeting(
    List<MeetingList>? upComingMeetingList,
    List<MeetingList>? previousMeetingList,
  ) {
    _upComingMeetingList = upComingMeetingList;
    _previousMeetingList = previousMeetingList;
    notifyListeners();
  }

  List<int>? _memberIdsList;

  List<int>? get memberIdsList => _memberIdsList;
  void initializeIds(List<int>? ids) {
    _memberIdsList = ids;
  }

  List<Map<int?, String?>> _userHandles = [];
  List<Map<int?, String?>> get userHandles => _userHandles;
  void updateUserHandles(List<Map<int?, String?>> userHandles) {
    _userHandles = userHandles;
    notifyListeners();
  }

  List<Map<int?, String?>> _userProfilePicture = [];
  List<Map<int?, String?>> get userProfilePicture => _userProfilePicture;
  void updateUserProfilePicture(List<Map<int?, String?>> userProfilePicture) {
    _userProfilePicture = userProfilePicture;
    notifyListeners();
  }
}
