import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/models/search_model/search_model.dart';
import 'package:flutter/material.dart';

import '../models/search_model/interested_model.dart';
import '../models/search_model/search_bookclub_model.dart';
import '../models/search_model/what_clubs_into_this_model.dart';
import '../models/search_model/who_into_this_author_model.dart';
import '../models/search_model/who_into_this_book_model.dart';
import '../models/search_model/who_read_this_model.dart';
import '../models/search_model/who_reading_this_model.dart';
import '../services/user_service.dart';

class SearchDataController with ChangeNotifier {
  final UserService userService = UserService();

  SearchModel? searchModel;

  Future<SearchData?> searchFunction(String search, int userId, int offSet,
      int limit, BuildContext context) async {
    SearchData? searchData;
    try {
      dio.Response response =
          await userService.searchFunction(search, userId, offSet, limit);

      if (response.statusCode == 200) {
        ///TODO: Add UTF-8 decoding
        var jsonObject = response.data;

        if (jsonObject.containsKey('data') && jsonObject['data'] != null) {
          searchModel = SearchModel.fromJson(jsonObject);
          searchData = searchModel?.data;
          log('Profile Length: ${searchData?.profiles?[0].userName}');
        } else {
          searchModel = null;
          searchData = null;
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        searchModel = null;
        searchData = null;
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      searchModel = null;
      searchData = null;
      log("Error1 : ${e.toString()}");
    }
    return searchData;
  }

  List<SearchClubs>? bookClubList = [];
  int bookClubCount = 0;

  Future<List<SearchClubs>?> searchClubsFunction(String search, int userId,
      int offSet, int limit, BuildContext context) async {
    // SearchData? searchData;
    try {
      dio.Response response =
          await userService.searchClubsFunction(search, userId, offSet, limit);
      // log("Response : ${response.data}");
      if (response.statusCode == 200) {
        ///TODO: Add UTF-8 decoding
        var jsonObject = response.data;
        final searchBookClubModel = SearchBookClubModel.fromJson(jsonObject);
        bookClubCount = searchBookClubModel.count ?? 0;
        log('Book Club Count : $bookClubCount');
        if (searchBookClubModel.data != null) {
          bookClubList = searchBookClubModel.data;
          log('Book Length : ${bookClubList?.length}');
        } else {
          bookClubList = [];
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        bookClubList = [];
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      bookClubList = [];
      log("Error1 : ${e.toString()}");
    }
    return bookClubList;
  }

  List<WhoIntoThisBookList>? whoIntoThisBookList = [];
  int whoIntoThisBookCount = 0;

  Future<List<WhoIntoThisBookList>?> whoIntoThisBooks(String search, int userId,
      int offSet, int limit, BuildContext context) async {
    // SearchData? searchData;
    try {
      dio.Response response = await userService.postSearchByBookAndAuthor(
          search, userId, offSet, limit, true);
      // log("Response : ${response.data}");
      if (response.statusCode == 200) {
        ///TODO: Add UTF-8 decoding
        var jsonObject = response.data;
        final whoIntoThisBookModel = WhoIntoThisBookModel.fromJson(jsonObject);
        if (whoIntoThisBookModel.data != null) {
          whoIntoThisBookList = whoIntoThisBookModel.data;
          whoIntoThisBookCount =
              whoIntoThisBookModel.data?.first.whoIntoThisBooks?.totalCount ??
                  0;
          log('WhoIntoThisBook Count : $whoIntoThisBookCount');
          log('WhoIntoThisBook Length : ${whoIntoThisBookList?.length}');
          notifyListeners();
        } else {
          whoIntoThisBookList = [];
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        whoIntoThisBookList = [];
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      whoIntoThisBookList = [];
      log("Error1 : ${e.toString()}");
    }

    return whoIntoThisBookList;
  }

  List<WhoIntoThisAuthorList>? whoIntoThisAuthorList = [];
  int whoIntoThisAuthorCount = 0;

  Future<List<WhoIntoThisAuthorList>?> whoIntoThisAuthor(String search,
      int userId, int offSet, int limit, BuildContext context) async {
    // SearchData? searchData;
    try {
      dio.Response response = await userService.postSearchByBookAndAuthor(
          search, userId, offSet, limit, false);
      // log("Response : ${response.data}");
      if (response.statusCode == 200) {
        ///TODO: Add UTF-8 decoding
        var jsonObject = response.data;
        final whoIntoThisAuthorModel =
            WhoIntoThisAuthorModel.fromJson(jsonObject);
        if (whoIntoThisAuthorModel.data != null) {
          whoIntoThisAuthorList = whoIntoThisAuthorModel.data;
          whoIntoThisAuthorCount = whoIntoThisAuthorModel
                  .data?.first.whoIntoThisAuthor?.totalCount ??
              0;
          log('WhoIntoThisAuthor Count : $whoIntoThisAuthorCount');
          log('WhoIntoThisAuthor Length : ${whoIntoThisAuthorList?.length}');
          notifyListeners();
        } else {
          whoIntoThisAuthorList = [];
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        whoIntoThisAuthorList = [];
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      whoIntoThisAuthorList = [];
      log("Error1 : ${e.toString()}");
    }

    return whoIntoThisAuthorList;
  }

  /// IMPLEMENTED 3 JAN 2025

  List<InterestedList>? interestedList;
  int interestedCount = 0;

  Future<List<InterestedList>?> getInterestedList(int userId, List<int>? bookId,
      String filter, int limit, int offset, BuildContext context) async {
    // SearchData? searchData;
    log("Book Id : $bookId");
    try {
      dio.Response response =
          await userService.getByData(userId, bookId, filter, limit, offset);
      // log("Response : ${response.data}");
      if (response.statusCode == 200) {
        ///TODO: Add UTF-8 decoding
        var jsonObject = response.data;
        final interestedModel = InterestedModel.fromJson(jsonObject);
        log("Interested List : ${interestedModel.data}");
        if (interestedModel.data != null) {
          interestedCount = interestedModel.count ?? 0;
          interestedList = interestedModel.data;
          log('Interested List Length : ${interestedList?.length}');
          notifyListeners();
        } else {
          interestedList = [];
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        interestedList = [];
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      interestedList = [];
      log("Error1 : ${e.toString()}");
    }

    return interestedList;
  }

  List<WhoReadThisList>? whoReadThisList;
  int whoReadThisCount = 0;

  Future<List<WhoReadThisList>?> getWhoReadThisList(
      int userId,
      List<int>? bookId,
      String filter,
      int limit,
      int offset,
      BuildContext context) async {
    // SearchData? searchData;
    try {
      dio.Response response =
          await userService.getByData(userId, bookId, filter, limit, offset);
      // log("Response : ${response.data}");
      if (response.statusCode == 200) {
        ///TODO: Add UTF-8 decoding
        var jsonObject = response.data;
        final whoReadThisModel = WhoReadThisModel.fromJson(jsonObject);
        if (whoReadThisModel.data != null) {
          whoReadThisCount = whoReadThisModel.count ?? 0;
          whoReadThisList = whoReadThisModel.data;
          log('WhoReadThis List Length : ${whoReadThisList?.length}');
          notifyListeners();
        } else {
          whoReadThisList = [];
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        whoReadThisList = [];
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      whoReadThisList = [];
      log("Error1 : ${e.toString()}");
    }

    return whoReadThisList;
  }

  List<WhoReadingThisList>? whoReadingThisList;
  int whoReadingCount = 0;

  Future<List<WhoReadingThisList>?> getWhoReadingThisList(
      int userId,
      List<int>? bookId,
      String filter,
      int limit,
      int offset,
      BuildContext context) async {
    // SearchData? searchData;
    try {
      dio.Response response =
          await userService.getByData(userId, bookId, filter, limit, offset);
      // log("Response : ${response.data}");
      if (response.statusCode == 200) {
        ///TODO: Add UTF-8 decoding
        var jsonObject = response.data;
        final whoReadingThisModel = WhoReadingThisModel.fromJson(jsonObject);
        if (whoReadingThisModel.data != null) {
          whoReadingCount = whoReadingThisModel.count ?? 0;
          whoReadingThisList = whoReadingThisModel.data;
          log('WhoReadThis List Length : ${whoReadingThisList?.length}');
          notifyListeners();
        } else {
          whoReadingThisList = [];
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        whoReadingThisList = [];
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      whoReadingThisList = [];
      log("Error1 : ${e.toString()}");
    }
    return whoReadingThisList;
  }

  List<WhatClubsIntoThisList>? whatClubsIntoThisList;
  int clubCount = 0;

  Future<List<WhatClubsIntoThisList>?> getWhatClubsIntoThisList(
      int userId,
      List<int>? bookId,
      String filter,
      int limit,
      int offset,
      BuildContext context) async {
    // SearchData? searchData;
    try {
      dio.Response response =
          await userService.getByData(userId, bookId, filter, limit, offset);
      // log("Response : ${response.data}");
      if (response.statusCode == 200) {
        ///TODO: Add UTF-8 decoding
        var jsonObject = response.data;
        final whatClubsIntoThisModel =
            WhatClubsIntoThisModel.fromJson(jsonObject);
        if (whatClubsIntoThisModel.data != null) {
          clubCount = whatClubsIntoThisModel.count ?? 0;
          whatClubsIntoThisList = whatClubsIntoThisModel.data;
          log('whatClubsIntoThis List Length : ${whatClubsIntoThisList?.length}');
          notifyListeners();
        } else {
          whatClubsIntoThisList = [];
          log("Failed access : ${response.data}");
        }
      } else if (response.statusCode == 401) {
        Map<String, dynamic> payload = {};
        if (context.mounted) {
          await CommonHelper().userLogoutFunction(context, payload);
        }
        notifyListeners();
      } else {
        whatClubsIntoThisList = [];
        log("Can't fetch data : ${response.data}");
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      whatClubsIntoThisList = [];
      log("Error1 : ${e.toString()}");
    }

    return whatClubsIntoThisList;
  }
}
