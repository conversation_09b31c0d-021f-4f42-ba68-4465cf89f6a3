import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart' as dio;
import 'package:eljunto/controller/app_version_controller.dart';
import 'package:eljunto/controller/user_credential_controller.dart';
import 'package:eljunto/services/login_service.dart';
import 'package:eljunto/views/routing.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoginController with ChangeNotifier {
  final LoginService loginService = LoginService();
  bool isSuccess = false;
  String errorMessage = '';
  final key = AppRouter.rootNavigatorKeys;

  String? get appVersion {
    final context = key.currentContext;
    return context != null
        ? Provider.of<AppVersionProvider>(context, listen: false)
            .currentAppVersion
        : null;
  }

  String? get appName {
    final context = key.currentContext;
    return context != null
        ? Provider.of<AppVersionProvider>(context, listen: false).appName
        : null;
  }

  Future<bool> loginFunction(String email, String password, String fcmToken,
      String deviceId, BuildContext context) async {
    try {
      var bytes = utf8.encode(password);
      var encryptedPwd = sha256.convert(bytes).toString();
      dio.Response response =
          await loginService.login(email, encryptedPwd, fcmToken, deviceId);

      if (response.statusCode == 200) {
        isSuccess = true;
        if (context.mounted) {
          setLocalStorageValues(response.data, context);
        }
      } else {
        errorMessage = response.data['message'] ?? 'Something went wrong';
        isSuccess = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      isSuccess = false;
    } finally {
      notifyListeners();
    }
    return isSuccess;
  }

  Future<Map<String, dynamic>> verifyEmail(
      String email, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response = await loginService.emailVerification(email);

      // if (response.statusCode == 200) {
      responseMap = response.data;
      // }
      // else {
      //   errorMessage = 'Something went wrong';
      //   responseMap = response.data;
      // }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    } finally {
      notifyListeners();
    }
    return responseMap;
  }

  Future<Map<String, dynamic>> forgotPassword(
      String email, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response =
          await loginService.forgotPasswordMailVerification(email);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else {
        errorMessage = 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      log(errorMessage);
      responseMap = {"error": errorMessage};
    }
    return responseMap;
  }

  Future<Map<String, dynamic>> verifyOTP(String email, String token, String otp,
      bool flag, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response =
          await loginService.otpVerification(email, token, otp, flag);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else {
        errorMessage = response.data['message'] ?? 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      responseMap = {"error": errorMessage};
    }
    return responseMap;
  }

  Future<Map<String, dynamic>> setPassword(
      String email,
      String token,
      String otp,
      String password,
      String fcmToken,
      String deviceId,
      BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      var bytes = utf8.encode(password);
      var _encryptedPwd = sha256.convert(bytes).toString();
      dio.Response response = await loginService.setPassword(
          email, token, otp, _encryptedPwd, fcmToken, deviceId);

      if (response.statusCode == 200) {
        responseMap = response.data;
        if (responseMap['statusCode'] == 200) {
          if (context.mounted) {
            setLocalStorageValues(responseMap['data'], context);
          }
        }
      } else {
        errorMessage = response.data['message'] ?? 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      responseMap = {"error": errorMessage};
    }
    return responseMap;
  }

  bool isSuccessPass = false;
  Future<bool> reSetPassword(String email, String token, String otp,
      String password, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      var bytes = utf8.encode(password);
      var _encryptedPwd = sha256.convert(bytes).toString();
      dio.Response response =
          await loginService.reSetPassword(email, token, otp, _encryptedPwd);
      responseMap = response.data;
      if (responseMap['statusCode'] == 200) {
        isSuccessPass = true;
        notifyListeners();
      } else {
        isSuccessPass = false;
        errorMessage = responseMap['message'] ?? 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      isSuccessPass = false;
      errorMessage = 'Something went wrong';
      responseMap = {"error": errorMessage};
    }
    return isSuccessPass;
  }

  Future<Map<String, dynamic>> setNameAndHandle(
      String email,
      String token,
      String otp,
      String name,
      String handle,
      String userLocation,
      String userBio,
      BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response = await loginService.setNameAndHandle(
          email, token, otp, name, handle, userLocation, userBio);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else {
        errorMessage = response.data['message'] ?? 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      responseMap = {"error": errorMessage};
    }
    return responseMap;
  }

  Future<Map<String, dynamic>> checkAavailabilityOfHandle(
      String email, String handle, BuildContext context) async {
    Map<String, dynamic> responseMap = {};
    try {
      dio.Response response =
          await loginService.checkAavailabilityOfHandle(email, handle);

      if (response.statusCode == 200) {
        responseMap = response.data;
      } else {
        errorMessage = response.data['message'] ?? 'Something went wrong';
        responseMap = {"error": errorMessage};
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      errorMessage = 'Something went wrong';
      responseMap = {"error": errorMessage};
    }
    return responseMap;
  }

  Future<void> setLocalStorageValues(
      Map<String, dynamic> data, BuildContext context) async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setString('jwttoken', data['token']);
    pref.setInt('userId', data['userId']);
    pref.setString('userEmailId', data['userEmailId']);
    pref.setBool('isUserNameAvailable', data['isUserNameAvailable']);
    pref.setBool(
      'isUserBioAndLocationAvailable',
      data['isUserBioAndLocationAvailable'],
    );
    if (data['userHandle'] != null) {
      pref.setString('userHandle', data['userHandle']);
    }

    if (data['userName'] != null) {
      pref.setString('userName', data['userName']);
    }
    // Update user credentials in the provider
    if (context.mounted) {
      Provider.of<UserCredentialController>(context, listen: false).setUser();
    }
  }

  bool isChangePassword = false;
  String? changePasswordErrorMessage;

  Future<bool> changePassword(String userEmail, String currentPassword,
      String newPassword, String confirmPassword) async {
    try {
      final currentPass = utf8.encode(currentPassword);
      var currentEncruptPass = sha256.convert(currentPass).toString();
      final newPass = utf8.encode(newPassword);
      var newEncruptPass = sha256.convert(newPass).toString();
      final confirmPass = utf8.encode(confirmPassword);
      var confirmEncruptPass = sha256.convert(confirmPass).toString();

      dio.Response response = await loginService.changePassword(
          userEmail, currentEncruptPass, newEncruptPass, confirmEncruptPass);
      if (response.statusCode == 200) {
        log("Password changed successfully : ${response.data}");
        isChangePassword = true;
      } else {
        log("Failed to change password : ${response.data}");
        changePasswordErrorMessage = response.data['message'];
        isChangePassword = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isChangePassword = false;
    }
    return isChangePassword;
  }

  bool isLogOut = false;
  Future<bool> logOutFunction(String userMail) async {
    try {
      dio.Response response = await loginService.logOutFunction(userMail);
      if (response.statusCode == 200) {
        log("Logout Successfull : ${response.data}");
        isLogOut = true;
      } else {
        log("Logout Failed : ${response.data}");
        isLogOut = false;
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      log(e.toString());
      isLogOut = false;
    }
    return isLogOut;
  }

  /// UPDATE FCM TOKEN API CALL FUNCTION
  Future<void> updateFcmToken() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    final fcmToken = pref.getString('fcmToken');
    final deviceId = pref.getString('deviceId');
    try {
      final response =
          await loginService.updateFcmToken(deviceId ?? '', fcmToken ?? '');
      if (response.statusCode == 200) {
        log("Fcm toke updated successfully : ${response.data}");
      } else {
        log("Failed to update Fcm Token : ${response.data}");
      }
    } catch (e) {
      log('Failed to fetch FCM Token: ${e.toString()}');
    }
  }
}
