import 'dart:developer';

import 'package:eljunto/models/user_model.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UserCredentialController with ChangeNotifier {
  UserCredential userCredential = UserCredential();
  int? userId;

  Future<void> setUser() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    userCredential.jwttoken = pref.getString('jwttoken');
    userCredential.userId = pref.getInt('userId');
    userCredential.userHandler = pref.getString('userHandle');
    userCredential.isUserNameAvailable =
        pref.getBool('isUserNameAvailable') ?? true;
    userCredential.userEmailId = pref.getString('userEmailId');
    userCredential.userName = pref.getString('userName');

    notifyListeners();
  }

  Future<void> clearUserCredentials() async {}

  Future<int?> getUserId() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    userId = pref.getInt('userId');
    log("User ID: $userId");
    return userId;
  }

  Future<bool> isUserLoggedIn() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    final jwtToken = pref.getString('jwttoken');
    userId = pref.getInt('userId');
    // Check if JWT token exists and is not empty
    log("JWT Token In UserCred: $jwtToken");
    log("Boolean JWT Token : ${jwtToken != null && jwtToken.isNotEmpty}");
    return jwtToken != null && jwtToken.isNotEmpty;
  }
}
