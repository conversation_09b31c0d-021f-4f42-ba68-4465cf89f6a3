import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:eljunto/controller/subscription_controller.dart';
import 'package:eljunto/models/subscription_model/apple_purchase_response_model.dart';
import 'package:eljunto/models/subscription_model/subscription_model.dart';
import 'package:eljunto/reusableWidgets/subscription/subscription_list_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:provider/provider.dart';

import '../models/subscription_model/subscription_purchase_response_model.dart';

class InAppPurchaseController extends ChangeNotifier {
  SubscriptionController? subscriptionController;
  List<ProductDetails> products = [];
  List<ProductDetails> productsList = [];
  InAppPurchase inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> streamSubscription;
  ProductDetails? purchaseProduct;
  bool isPurchaseLoading = false;
  String planPrice = '';

  Future<void> purchasePlan(ProductDetails? product) async {
    purchaseProduct = product;
    log("Purchase Product Plan: ${purchaseProduct?.price}");
    notifyListeners();
  }

  Future<void> fetchProducts(BuildContext context) async {
    if (!await inAppPurchase.isAvailable()) {
      log("[IAP] Store not available.");
      products = [];
      notifyListeners();
      return;
    }

    log("[IAP] Store is available. Fetching products...");

    try {
      final productIds =
          Provider.of<SubscriptionController>(context, listen: false)
              .subscriptionDetailList
              .toSet();
      if (productIds.isEmpty) throw "No product IDs available";

      final response =
          await InAppPurchase.instance.queryProductDetails(productIds);
      if (response.error != null) throw response.error!;
      if (response.productDetails.isEmpty) {
        throw "No products found. Invalid IDs or setup issue.";
      }

      log("[IAP] Products found: ${response.productDetails.map((p) => p.id).toList()}");
      final purchasedProductIds = await getUserPurchasedProducts();
      log("[IAP] User previously purchased IDs: $purchasedProductIds");

      products = response.productDetails.where((product) {
        final isPurchased = purchasedProductIds.contains(product.id);
        bool isFreeTrial = false;

        if (Platform.isIOS) {
          final iosProduct = product as AppStoreProductDetails;
          isFreeTrial = iosProduct.skProduct.introductoryPrice?.price == '0';
          log("[IAP] iOS product ${product.id}: Free Trial=$isFreeTrial, Purchased=$isPurchased");
        } else if (Platform.isAndroid) {
          final androidProduct = product as GooglePlayProductDetails;
          isFreeTrial = androidProduct.productDetails.subscriptionOfferDetails
                  ?.any((offer) => offer.pricingPhases
                      .any((phase) => phase.priceAmountMicros == 0)) ??
              false;
          log("[IAP] Android product ${product.id}: Free Trial=$isFreeTrial, Purchased=$isPurchased");
        }
        // getProductPrice();
        planPrice = product.price;
        notifyListeners();

        return !(isFreeTrial && isPurchased);
      }).toList();
      log("[IAP] Filtered products: ${products.map((p) => p.id).toList()}");
    } catch (e) {
      log("[IAP] Error: $e");
      products = [];
    }

    notifyListeners();
  }

  void getProductPrice() {
    planPrice = products.first.price;
    notifyListeners();
  }

  Future<List<String>> getUserPurchasedProducts() async {
    // Fetch the list of purchased products from backend or local storage
    // Example: Get from shared preferences
    return []; // Implement backend API or local storage check
  }

  Future<void> handlePurchaseUpdates(
      List<PurchaseDetails> purchaseDetailsList, BuildContext context) async {
    for (var purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        log("Pending: ${purchaseDetails.productID}");
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          log("Error: ${purchaseDetails.productID}");
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          subscriptionPayloadFunction(purchaseDetails, context);

          // WHEN USER CAN PURCHASE SUBSCRIPTION THEN UPDATE TO BACKEND AND PASS VERIFICATIONDATA RESPONSE RECEIVE FROM PLAY OR APP STORE
          //(SERVER VERIFICATION DATA AND PRODUCT ID PASS TO BACKEND)
          final purchaseData = {
            'productId': purchaseDetails.productID,
            'purchaseId': purchaseDetails.purchaseID,
            'localVerificationData':
                purchaseDetails.verificationData.localVerificationData,
            'serverVerificationData':
                purchaseDetails.verificationData.serverVerificationData,
            'purchaseStatus': purchaseDetails.status.name,
            'transactionDate': purchaseDetails.transactionDate,
            'source': purchaseDetails.verificationData.source,
          };
          log('Purchase details: $purchaseData');
          // log("Purchased: ${purchaseDetails.purchaseID}");
          // log("Transaction Date: ${purchaseDetails.transactionDate}");
          // log("Verification Source: ${purchaseDetails.verificationData.source}");
          // log("Verification localVerificationData String: ${purchaseDetails.verificationData.localVerificationData}");
          // log("Verification serverVerificationData: ${purchaseDetails.verificationData.serverVerificationData}");
          // log("Verification Status: ${purchaseDetails.status}");
          // log("Verification productID: ${purchaseDetails.productID}");
          // log("Verification pendingCompletePurchase: ${purchaseDetails.pendingCompletePurchase}");
          // log("Verification error: ${purchaseDetails.error}");

          bool isValid = verifyPurchase(purchaseDetails);
          if (isValid) {
            log("Purchase is valid");
            deliverProduct(purchaseDetails);
          } else {
            log("Purchase is invalid");
          }
        }
      }
      if (purchaseDetails.pendingCompletePurchase) {
        inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  int subScriptionId = 0;

  Future<void> subscriptionPayloadFunction(
      PurchaseDetails purchaseDetails, BuildContext context) async {
    isPurchaseLoading = true;
    notifyListeners();
    if (Platform.isAndroid) {
      final subscriptionDetails =
          Provider.of<SubscriptionController>(context, listen: false)
              .subscriptionDetails;
      log("InAppPurchaseController : $purchaseDetails");
      final data = subscriptionDetails?.data?.subscriptionDetails;

      for (var element in data ?? <SubscriptionDetail>[]) {
        if (element.productId == purchaseDetails.productID) {
          subScriptionId = element.subscriptionId ?? 0;
          log("Subscription Match Id : ${element.subscriptionId}");
        }
      }

      final subscription =
          jsonDecode(purchaseDetails.verificationData.localVerificationData);
      log("JsonDecode Data orderId : ${subscription['orderId']}");

      log("JsonDecode Data : ${jsonDecode(purchaseDetails.verificationData.localVerificationData)}");
      final payload = SubscriptionResponseModel(
        productId: purchaseDetails.productID,
        purchasedId: purchaseDetails.purchaseID,
        transactionDate: int.parse(purchaseDetails.transactionDate ?? ''),
        subscriptionId: subScriptionId,
        verificationSource: purchaseDetails.verificationData.source,
        verificationStatus: purchaseDetails.status.toString(),
        verificationPendingCompletePurchase:
            purchaseDetails.pendingCompletePurchase,
        verificationError: purchaseDetails.error,
        localVerificationData: LocalVerificationData(
          acknowledged: subscription['acknowledged'],
          orderId: subscription['orderId'],
          packageName: subscription['packageName'],
          productId: subscription['productId'],
          purchaseState: subscription['purchaseState'],
          purchaseTime: subscription['purchaseTime'],
          quantity: subscription['quantity'],
          purchaseToken: subscription['purchaseToken'],
          autoRenewing: subscription['autoRenewing'],
          purchaseCode: currencyCode,
          purchaseAmount: planPurchasePrice,
        ),
      );

      Provider.of<SubscriptionController>(context, listen: false)
          .purchaseSubscription(payload)
          .then(
        (value) async {
          if (context.mounted) {
            await Provider.of<SubscriptionController>(context, listen: false)
                .isActiveSubscription();
          }
          if (value) {
            log('Subscription Success');
            if (context.mounted) {
              _proceedToHomeScreen(context);
            }
          } else {
            log('Subscription Failed');
          }
        },
      );
    } else {
      final payload = ApplePurchaseResponseModel(
        localVerificationData:
            purchaseDetails.verificationData.localVerificationData,
        productId: purchaseDetails.productID,
        purchaseId: purchaseDetails.purchaseID,
        purchaseStatus: purchaseDetails.status.toString(),
        serverVerificationData:
            purchaseDetails.verificationData.serverVerificationData,
        source: purchaseDetails.verificationData.source,
        transactionDate: int.parse(purchaseDetails.transactionDate ?? ''),
      );

      Provider.of<SubscriptionController>(context, listen: false)
          .applePurchaseSubscription(payload)
          .then(
        (value) async {
          if (context.mounted) {
            await Provider.of<SubscriptionController>(context, listen: false)
                .isActiveSubscription();
          }
          if (value) {
            log('Subscription Success');
            if (context.mounted) {
              _proceedToHomeScreen(context);
            }
          } else {
            log('Subscription Failed');
          }
        },
      );
    }
    isPurchaseLoading = false;
    notifyListeners();
  }

  void _proceedToHomeScreen(BuildContext context) {
    context.goNamed('Home');
  }

  bool verifyPurchase(PurchaseDetails purchaseDetails) {
    return true;
  }

  void deliverProduct(PurchaseDetails purchaseDetails) {
    log("Delivering product: ${purchaseDetails.productID}");
  }

  Future<void> buyProduct(ProductDetails product) async {
    final PurchaseParam purchaseParam = PurchaseParam(
      productDetails: product,
      // applicationUserName: loggedinUserId.toString(),
    );
    await inAppPurchase.buyNonConsumable(
      purchaseParam: purchaseParam,
    );
  }
}
