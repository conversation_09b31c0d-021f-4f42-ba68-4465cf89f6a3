import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

class ConnectivityProvider extends ChangeNotifier with WidgetsBindingObserver {
  // Use the singleton instance for efficiency if appropriate for your app
  final InternetConnection _internetConnection =
      InternetConnection.createInstance(
    checkInterval: const Duration(seconds: 3),
    customCheckOptions: [
      InternetCheckOption(
        uri: Uri.parse('https://google.com'),
      ),
    ],
  );

  late StreamSubscription<InternetStatus> _subscription;
  InternetStatus _status = InternetStatus.connected;
  bool _isListening = false;

  // Expose the stream directly (NEW)
  Stream<InternetStatus> get statusStream => _internetConnection.onStatusChange;

  // Expose current status
  InternetStatus get status => _status;

  ConnectivityProvider() {
    WidgetsBinding.instance.addObserver(this);
    _checkInitialStatus();
  }

  Future<void> _checkInitialStatus() async {
    _status = await _internetConnection.internetStatus;
    notifyListeners();
  }

  void startListening() {
    // Prevent multiple subscriptions
    if (_isListening) return;

    log('Starting connectivity listener');
    _subscription = _internetConnection.onStatusChange.listen((newStatus) {
      log('Connectivity status changed: $newStatus');
      if (_status != newStatus) {
        // Only notify if status actually changed
        _status = newStatus;
        notifyListeners();
      }
    });
    _isListening = true;
  }

  void stopListening() {
    if (!_isListening) return;
    log('Stopping connectivity listener');
    _subscription.cancel();
    _isListening = false;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
        startListening();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        stopListening();
        break;
    }
  }

  @override
  void dispose() {
    log('Disposing ConnectivityProvider');
    stopListening();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
