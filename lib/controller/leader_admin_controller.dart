import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:eljunto/services/leader_admin_service.dart';
import 'package:flutter/material.dart';

class LeaderAdminController with ChangeNotifier {
  LeaderAdminservice leaderAdminservice = LeaderAdminservice();

  bool isSuccessFlag = false;
  Future<bool> updateClubCharter(
    BuildContext context,
    int? bookClubId,
    String? clubCharter,
    String? memberReqPrompt,
    String? clubStatus,
  ) async {
    try {
      dio.Response response = await leaderAdminservice.updateCharter(
          bookClubId, clubCharter, memberReqPrompt, clubStatus);
      if (response.statusCode == 200) {
        log("Success : ${response.data}");
        isSuccessFlag = true;
      } else {
        isSuccessFlag = false;
        log('Failed to update data : ${response.data}');
      }
    } on SocketException {
      log("SocketException: Unable to connect to ");
    } on TimeoutException {
      log("TimeoutException: Unable to connect to ");
    } catch (e) {
      isSuccessFlag = false;
      log(e.toString());
    }
    return isSuccessFlag;
  }
}
