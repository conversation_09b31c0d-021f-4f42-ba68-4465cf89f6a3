class ClubMembershipModel {
  final int? bookClubMemberId;
  final int? bookClubId;
  final String? clubType;
  final String? bookClubName;
  final String? bookAuthor;
  final String? clubCount;
  String? responseMessage;
  final String? requestMessage;
  final String? invitationMessage;
  final int? userId;
  final String? userName;
  final String? userType;
  String? status;
  final String? userProfilePicture;
  final int? initiatedBy;
  final int? totalVacancies;
  final int? totalPositions;
  final String? userHandle;
  List<String>? fcmTokens;
  bool isMsgNotifyEnabled;

  ClubMembershipModel({
    this.bookClubMemberId,
    this.bookClubId,
    this.clubType,
    this.bookClubName,
    this.bookAuthor,
    this.clubCount,
    this.responseMessage,
    this.requestMessage,
    this.invitationMessage,
    this.userId,
    this.userType,
    this.userName,
    this.status,
    this.userProfilePicture,
    this.initiatedBy,
    this.totalVacancies,
    this.totalPositions,
    this.userHandle,
    this.fcmTokens,
    this.isMsgNotifyEnabled = false,
  });

  factory ClubMembershipModel.fromJson(Map<String, dynamic> json) {
    return ClubMembershipModel(
      bookClubMemberId: json['bookClubMemberId'],
      bookClubId: json['bookClubId'],
      clubType: json['clubType'],
      bookClubName: json['bookClubName'],
      bookAuthor: json['bookAuthor'],
      clubCount: json['clubCount'],
      responseMessage: json['responseMessage'],
      requestMessage: json['requestMessage'],
      invitationMessage: json['invitationMessage'],
      userId: json['userId'],
      userType: json['userType'],
      userName: json['userName'],
      status: json['status'],
      userProfilePicture: json['userProfilePicture'],
      initiatedBy: json['initiatedBy'],
      totalVacancies: json['totalVacancies'],
      totalPositions: json['totalPositions'],
      userHandle: json['userHandle'],
      fcmTokens: json["fcmTokens"] == null
          ? []
          : List<String>.from(json["fcmTokens"]!.map((x) => x)),
      isMsgNotifyEnabled: json['isMsgNotifyEnabled'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bookClubMemberId': bookClubMemberId,
      'bookClubId': bookClubId,
      'clubType': clubType,
      'bookClubName': bookClubName,
      'bookAuthor': bookAuthor,
      'clubCount': clubCount,
      'responseMessage': responseMessage,
      'requestMessage': requestMessage,
      'invitationMessage': invitationMessage,
      'userId': userId,
      'userType': userType,
      'userName': userName,
      'status': status,
      'userProfilePicture': userProfilePicture,
      'initiatedBy': initiatedBy,
      'totalVacancies': totalVacancies,
      'totalPositions': totalPositions,
      'userHandle': userHandle,
      'fcmTokens': fcmTokens,
      'isMsgNotifyEnabled': isMsgNotifyEnabled,
    };
  }
}
