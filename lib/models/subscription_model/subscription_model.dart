// To parse this JSON data, do
//
//     final subscriptionModel = subscriptionModelFromJson(jsonString);

import 'dart:convert';

SubscriptionModel subscriptionModelFromJson(String str) =>
    SubscriptionModel.fromJson(json.decode(str));

String subscriptionModelToJson(SubscriptionModel data) =>
    json.encode(data.toJson());

class SubscriptionModel {
  final String? message;
  final int? statusCode;
  final SubscriptionInfo? data;

  SubscriptionModel({
    this.message,
    this.statusCode,
    this.data,
  });

  factory SubscriptionModel.fromJson(Map<String, dynamic> json) =>
      SubscriptionModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] == null
            ? null
            : SubscriptionInfo.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data?.toJson(),
      };
}

class SubscriptionInfo {
  final List<SubscriptionDetail>? subscriptionDetails;
  final bool? isFreeTrial;

  SubscriptionInfo({
    this.subscriptionDetails,
    this.isFreeTrial,
  });

  factory SubscriptionInfo.fromJson(Map<String, dynamic> json) =>
      SubscriptionInfo(
        subscriptionDetails: json["subscriptionDetails"] == null
            ? []
            : List<SubscriptionDetail>.from(json["subscriptionDetails"]!
                .map((x) => SubscriptionDetail.fromJson(x))),
        isFreeTrial: json["isFreeTrial"],
      );

  Map<String, dynamic> toJson() => {
        "subscriptionDetails": subscriptionDetails == null
            ? []
            : List<dynamic>.from(subscriptionDetails!.map((x) => x.toJson())),
        "isFreeTrial": isFreeTrial,
      };
}

class SubscriptionDetail {
  final int? subscriptionId;
  final String? productId;
  final String? subscriptionBillingCycle;
  final String? subscriptionName;
  final String? subscriptionDescription;
  final double? subscriptionPrice;
  final String? subscriptionCurrency;
  final int? subscriptionStatus;
  final int? subscriptionCreatedAt;

  SubscriptionDetail({
    this.subscriptionId,
    this.productId,
    this.subscriptionBillingCycle,
    this.subscriptionName,
    this.subscriptionDescription,
    this.subscriptionPrice,
    this.subscriptionCurrency,
    this.subscriptionStatus,
    this.subscriptionCreatedAt,
  });

  factory SubscriptionDetail.fromJson(Map<String, dynamic> json) =>
      SubscriptionDetail(
        subscriptionId: json["subscriptionId"],
        productId: json["productId"],
        subscriptionBillingCycle: json["subscriptionBillingCycle"],
        subscriptionName: json["subscriptionName"],
        subscriptionDescription: json["subscriptionDescription"],
        subscriptionPrice: json["subscriptionPrice"]?.toDouble(),
        subscriptionCurrency: json["subscriptionCurrency"],
        subscriptionStatus: json["subscriptionStatus"],
        subscriptionCreatedAt: json["subscriptionCreatedAt"],
      );

  Map<String, dynamic> toJson() => {
        "subscriptionId": subscriptionId,
        "productId": productId,
        "subscriptionBillingCycle": subscriptionBillingCycle,
        "subscriptionName": subscriptionName,
        "subscriptionDescription": subscriptionDescription,
        "subscriptionPrice": subscriptionPrice,
        "subscriptionCurrency": subscriptionCurrency,
        "subscriptionStatus": subscriptionStatus,
        "subscriptionCreatedAt": subscriptionCreatedAt,
      };
}
