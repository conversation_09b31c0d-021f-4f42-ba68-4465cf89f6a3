// To parse this JSON data, do
//
//     final subscriptionResponseModel = subscriptionResponseModelFromJson(jsonString);

import 'dart:convert';

SubscriptionResponseModel subscriptionResponseModelFromJson(String str) =>
    SubscriptionResponseModel.fromJson(json.decode(str));

String subscriptionResponseModelToJson(SubscriptionResponseModel data) =>
    json.encode(data.toJson());

class SubscriptionResponseModel {
  final String? purchasedId;
  final int? subscriptionId;
  final int? transactionDate;
  final String? verificationSource;
  final String? verificationStatus;
  final String? productId;
  final bool? verificationPendingCompletePurchase;
  final dynamic verificationError;
  final LocalVerificationData? localVerificationData;

  SubscriptionResponseModel({
    this.purchasedId,
    this.subscriptionId,
    this.transactionDate,
    this.verificationSource,
    this.verificationStatus,
    this.productId,
    this.verificationPendingCompletePurchase,
    this.verificationError,
    this.localVerificationData,
  });

  SubscriptionResponseModel copyWith({
    String? purchasedId,
    int? subscriptionId,
    int? transactionDate,
    String? verificationSource,
    String? verificationStatus,
    String? productId,
    bool? verificationPendingCompletePurchase,
    dynamic verificationError,
    LocalVerificationData? localVerificationData,
  }) =>
      SubscriptionResponseModel(
        purchasedId: purchasedId ?? this.purchasedId,
        subscriptionId: subscriptionId ?? this.subscriptionId,
        transactionDate: transactionDate ?? this.transactionDate,
        verificationSource: verificationSource ?? this.verificationSource,
        verificationStatus: verificationStatus ?? this.verificationStatus,
        productId: productId ?? this.productId,
        verificationPendingCompletePurchase:
            verificationPendingCompletePurchase ??
                this.verificationPendingCompletePurchase,
        verificationError: verificationError ?? this.verificationError,
        localVerificationData:
            localVerificationData ?? this.localVerificationData,
      );

  factory SubscriptionResponseModel.fromJson(Map<String, dynamic> json) =>
      SubscriptionResponseModel(
        purchasedId: json["purchasedId"],
        subscriptionId: json["subscriptionId"],
        transactionDate: json["transactionDate"],
        verificationSource: json["verificationSource"],
        verificationStatus: json["verificationStatus"],
        productId: json["productId"],
        verificationPendingCompletePurchase:
            json["VerificationPendingCompletePurchase"],
        verificationError: json["Verification error"],
        localVerificationData: json["localVerificationData"] == null
            ? null
            : LocalVerificationData.fromJson(json["localVerificationData"]),
      );

  Map<String, dynamic> toJson() => {
        "purchasedId": purchasedId,
        "subscriptionId": subscriptionId,
        "transactionDate": transactionDate,
        "verificationSource": verificationSource,
        "verificationStatus": verificationStatus,
        "productId": productId,
        "VerificationPendingCompletePurchase":
            verificationPendingCompletePurchase,
        "Verification error": verificationError,
        "localVerificationData": localVerificationData?.toJson(),
      };
}

class LocalVerificationData {
  final String? orderId;
  final int? purchaseTime;
  final int? purchaseState;
  final String? purchaseToken;
  final double? purchaseAmount;
  final String? purchaseCode;
  final String? packageName;
  final String? productId;
  final int? quantity;
  final bool? autoRenewing;
  final bool? acknowledged;

  LocalVerificationData({
    this.orderId,
    this.purchaseTime,
    this.purchaseState,
    this.purchaseToken,
    this.purchaseAmount,
    this.purchaseCode,
    this.packageName,
    this.productId,
    this.quantity,
    this.autoRenewing,
    this.acknowledged,
  });

  LocalVerificationData copyWith({
    String? orderId,
    int? purchaseTime,
    int? purchaseState,
    String? purchaseToken,
    double? purchaseAmount,
    String? purchaseCode,
    String? packageName,
    String? productId,
    int? quantity,
    bool? autoRenewing,
    bool? acknowledged,
  }) =>
      LocalVerificationData(
        orderId: orderId ?? this.orderId,
        purchaseTime: purchaseTime ?? this.purchaseTime,
        purchaseState: purchaseState ?? this.purchaseState,
        purchaseToken: purchaseToken ?? this.purchaseToken,
        purchaseAmount: purchaseAmount ?? this.purchaseAmount,
        purchaseCode: purchaseCode ?? this.purchaseCode,
        packageName: packageName ?? this.packageName,
        productId: productId ?? this.productId,
        quantity: quantity ?? this.quantity,
        autoRenewing: autoRenewing ?? this.autoRenewing,
        acknowledged: acknowledged ?? this.acknowledged,
      );

  factory LocalVerificationData.fromJson(Map<String, dynamic> json) =>
      LocalVerificationData(
        orderId: json["orderId"],
        purchaseTime: json["purchaseTime"],
        purchaseState: json["purchaseState"],
        purchaseToken: json["purchaseToken"],
        purchaseAmount: json["purchaseAmount"]?.toDouble(),
        purchaseCode: json["purchaseCode"],
        packageName: json["packageName"],
        productId: json["productId"],
        quantity: json["quantity"],
        autoRenewing: json["autoRenewing"],
        acknowledged: json["acknowledged"],
      );

  Map<String, dynamic> toJson() => {
        "orderId": orderId,
        "purchaseTime": purchaseTime,
        "purchaseState": purchaseState,
        "purchaseToken": purchaseToken,
        "purchaseAmount": purchaseAmount,
        "purchaseCode": purchaseCode,
        "packageName": packageName,
        "productId": productId,
        "quantity": quantity,
        "autoRenewing": autoRenewing,
        "acknowledged": acknowledged,
      };
}
