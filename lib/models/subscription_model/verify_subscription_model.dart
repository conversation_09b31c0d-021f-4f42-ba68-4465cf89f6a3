// To parse this JSON data, do
//
//     final verifySubscriptionModel = verifySubscriptionModelFromJson(jsonString);

import 'dart:convert';

VerifySubscriptionModel verifySubscriptionModelFromJson(String str) =>
    VerifySubscriptionModel.fromJson(json.decode(str));

String verifySubscriptionModelToJson(VerifySubscriptionModel data) =>
    json.encode(data.toJson());

class VerifySubscriptionModel {
  final String? message;
  final int? statusCode;
  final Data? data;

  VerifySubscriptionModel({
    this.message,
    this.statusCode,
    this.data,
  });

  factory VerifySubscriptionModel.fromJson(Map<String, dynamic> json) =>
      VerifySubscriptionModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data?.toJson(),
      };
}

class Data {
  final int? subscriptionId;
  final int? userId;
  final double? ubAmount;
  final String? ubCurrency;
  final int? ubStartDate;
  final int? ubEndDate;
  final int? ubCreatedAt;
  final int? ubPurchaseTime;
  final String? usubStatus;
  final String? usubProductId;
  final String? ubPlatform;

  Data({
    this.subscriptionId,
    this.userId,
    this.ubAmount,
    this.ubCurrency,
    this.ubStartDate,
    this.ubEndDate,
    this.ubCreatedAt,
    this.ubPurchaseTime,
    this.usubStatus,
    this.usubProductId,
    this.ubPlatform,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        subscriptionId: json["subscriptionId"],
        userId: json["userId"],
        ubAmount: json["ubAmount"],
        ubCurrency: json["ubCurrency"],
        ubStartDate: json["ubStartDate"],
        ubEndDate: json["ubEndDate"],
        ubCreatedAt: json["ubCreatedAt"],
        ubPurchaseTime: json["ubPurchaseTime"],
        usubStatus: json["usubStatus"],
        usubProductId: json["usubProductId"],
        ubPlatform: json["ubPlatform"],
      );

  Map<String, dynamic> toJson() => {
        "subscriptionId": subscriptionId,
        "userId": userId,
        "ubAmount": ubAmount,
        "ubCurrency": ubCurrency,
        "ubStartDate": ubStartDate,
        "ubEndDate": ubEndDate,
        "ubCreatedAt": ubCreatedAt,
        "ubPurchaseTime": ubPurchaseTime,
        "usubStatus": usubStatus,
        "usubProductId": usubProductId,
        "ubPlatform": ubPlatform,
      };
}
