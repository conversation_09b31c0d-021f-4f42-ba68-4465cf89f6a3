// To parse this JSON data, do
//
//     final applePurchaseResponseModel = applePurchaseResponseModelFromJson(jsonString);

import 'dart:convert';

ApplePurchaseResponseModel applePurchaseResponseModelFromJson(String str) =>
    ApplePurchaseResponseModel.fromJson(json.decode(str));

String applePurchaseResponseModelToJson(ApplePurchaseResponseModel data) =>
    json.encode(data.toJson());

class ApplePurchaseResponseModel {
  final String? productId;
  final String? purchaseId;
  final String? localVerificationData;
  final String? serverVerificationData;
  final String? purchaseStatus;
  final int? transactionDate;
  final String? source;

  ApplePurchaseResponseModel({
    this.productId,
    this.purchaseId,
    this.localVerificationData,
    this.serverVerificationData,
    this.purchaseStatus,
    this.transactionDate,
    this.source,
  });

  factory ApplePurchaseResponseModel.fromJson(Map<String, dynamic> json) =>
      ApplePurchaseResponseModel(
        productId: json["productId"],
        purchaseId: json["purchaseId"],
        localVerificationData: json["localVerificationData"],
        serverVerificationData: json["serverVerificationData"],
        purchaseStatus: json["purchaseStatus"],
        transactionDate: json["transactionDate"],
        source: json["source"],
      );

  Map<String, dynamic> toJson() => {
        "productId": productId,
        "purchaseId": purchaseId,
        "localVerificationData": localVerificationData,
        "serverVerificationData": serverVerificationData,
        "purchaseStatus": purchaseStatus,
        "transactionDate": transactionDate,
        "source": source,
      };
}
