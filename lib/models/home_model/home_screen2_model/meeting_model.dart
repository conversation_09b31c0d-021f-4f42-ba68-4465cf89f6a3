// To parse this JSON data, do
//
//     final meetingModel = meetingModelFromJson(jsonString);

import 'dart:convert';

MeetingModel meetingModelFromJson(String str) =>
    MeetingModel.fromJson(json.decode(str));

String meetingModelToJson(MeetingModel data) => json.encode(data.toJson());

class MeetingModel {
  final String? message;
  final int? statusCode;
  final int? count;
  final List<MeetingList>? data;

  MeetingModel({
    this.message,
    this.statusCode,
    this.count,
    this.data,
  });

  factory MeetingModel.fromJson(Map<String, dynamic> json) => MeetingModel(
        message: json["message"],
        statusCode: json["statusCode"],
        count: json["count"],
        data: json["data"] == null
            ? []
            : List<MeetingList>.from(
                json["data"]!.map((x) => MeetingList.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "count": count,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class MeetingList {
  final int? meetingId;
  final int? bookClubId;
  final String? bookName;
  final String? partOfBookCovered;
  final int? meetingDate;
  final int? meetingStartTime;
  final String? meetingDuration;
  final String? discussionQuestions;
  final String? meetingStatus;
  final String? bookAuthor;
  final List<String>? meetingAlerts; // expecting a List<int> here
  final int? meetingEndTime;
  final String? token;
  final int? userId;
  final String? channelName;

  MeetingList({
    this.meetingId,
    this.bookClubId,
    this.bookName,
    this.partOfBookCovered,
    this.meetingDate,
    this.meetingStartTime,
    this.meetingDuration,
    this.discussionQuestions,
    this.meetingStatus,
    this.bookAuthor,
    this.meetingAlerts,
    this.meetingEndTime,
    this.token,
    this.userId,
    this.channelName,
  });

  factory MeetingList.fromJson(Map<String, dynamic> json) => MeetingList(
        meetingId: json["meetingId"],
        bookClubId: json["bookClubId"],
        bookName: json["bookName"],
        partOfBookCovered: json["partOfBookCovered"],
        meetingDate: json["meetingDate"],
        meetingStartTime: json["meetingStartTime"],
        meetingDuration: json["meetingDuration"],
        discussionQuestions: json["discussionQuestions"],
        meetingStatus: json["meetingStatus"],
        bookAuthor: json["bookAuthor"],
        // Convert the dynamic list to List<int>
        meetingAlerts: json["meetingAlerts"] == null
            ? []
            : List<String>.from(json["meetingAlerts"].map((x) => x.toString())),

        // meetingAlerts: json["meetingAlerts"],
        // == null
        //     ? []
        //     : List<int>.from(json["meetingAlerts"].map((x) => x as int)),
        meetingEndTime: json["meetingEndTime"],
        token: json['token'],
        userId: json['userId'],
        channelName: json['channelName'],
      );

  Map<String, dynamic> toJson() => {
        "meetingId": meetingId,
        "bookClubId": bookClubId,
        "bookName": bookName,
        "partOfBookCovered": partOfBookCovered,
        "meetingDate": meetingDate,
        "meetingStartTime": meetingStartTime,
        "meetingDuration": meetingDuration,
        "discussionQuestions": discussionQuestions,
        "meetingStatus": meetingStatus,
        "bookAuthor": bookAuthor,
        "meetingAlerts": meetingAlerts == null
            ? []
            : List<dynamic>.from(meetingAlerts!.map((x) => x)),
        "meetingEndTime": meetingEndTime,
        "token": token,
        "userId": userId,
        "channelName": channelName,
      };
}



// class MeetingModel {
//   final int? meetingId;
//   final int? bookClubId;
//   final String? bookName;
//   final String? bookAuthor;
//   final String? partOfBookCovered;
//   final int? meetingAlerts;
//   final int? meetingDate;
//   final int? meetingStartTime;
//   final String? meetingDuration;
//   final String? discussionQuestions;
//   final String? meetingStatus;
//   final int? meetingEndTime;

//   MeetingModel({
//     this.meetingId,
//     this.bookClubId,
//     this.bookName,
//     this.bookAuthor,
//     this.partOfBookCovered,
//     this.meetingAlerts,
//     this.meetingDate,
//     this.meetingStartTime,
//     this.meetingDuration,
//     this.discussionQuestions,
//     this.meetingStatus,
//     this.meetingEndTime,
//   });

//   factory MeetingModel.fromJson(Map<String, dynamic> json) {
//     return MeetingModel(
//       meetingId: json['meetingId'],
//       bookClubId: json['bookClubId'],
//       bookName: json['bookName'],
//       bookAuthor: json['bookAuthor'],
//       partOfBookCovered: json['partOfBookCovered'],
//       meetingDate: json['meetingDate'],
//       meetingStartTime: json['meetingStartTime'],
//       meetingDuration: json['meetingDuration'],
//       discussionQuestions: json['discussionQuestions'],
//       meetingStatus: json['meetingStatus'],
//       meetingEndTime: json['meetingEndTime'],
//       meetingAlerts: json['meetingAlerts'],
//     );
//   }
// }
