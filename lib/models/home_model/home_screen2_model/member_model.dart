class MemberModel {
  /* final String? profileLogo;
  final String? name;
  final String? leaderLogo;

  MemberModel({
    this.leaderLogo,
    this.name,
    this.profileLogo,
  }); */
  final int? bookClubMemberId;
  final int? bookClubId;
  final int? userId;
  final String? userName;
  final String? userProfilePicture;
  final String? userType;
  final String? status;

  MemberModel(
      {this.bookClubMemberId,
      this.bookClubId,
      this.userId,
      this.userName,
      this.userProfilePicture,
      this.userType,
      this.status});

  factory MemberModel.fromJson(Map<String, dynamic> json) {
    return MemberModel(
        bookClubMemberId: json['bookClubMemberId'],
        bookClubId: json['bookClubId'],
        userId: json['userId'],
        userName: json['userName'],
        userProfilePicture: json['userProfilePicture'],
        userType: json['userType'],
        status: json['status']);
  }
}
