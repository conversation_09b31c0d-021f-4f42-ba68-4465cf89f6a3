// To parse this JSON data, do
//
//     final fellowReaderModel = fellowReaderModelFromJson(jsonString);

import 'dart:convert';

FellowReaderModel fellowReaderModelFromJson(String str) =>
    FellowReaderModel.fromJson(json.decode(str));

String fellowReaderModelTo<PERSON>son(FellowReaderModel data) =>
    json.encode(data.toJson());

class FellowReaderModel {
  final String? message;
  final List<FellowReader>? data;
  final int? count;
  final int? statusCode;

  FellowReaderModel({
    this.message,
    this.data,
    this.count,
    this.statusCode,
  });

  factory FellowReaderModel.fromJson(Map<String, dynamic> json) =>
      FellowReaderModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<FellowReader>.from(
                json["data"]!.map((x) => FellowReader.fromJson(x))),
        count: json["count"],
        statusCode: json["statusCode"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
        "statusCode": statusCode,
      };
}

class FellowReader {
  int? userId;
  final String? userName;
  final String? userProfilePicture;
  final String? bookName;
  final String? bookAuthor;
  final bool? currentlyReading;
  final String? userLocation;
  final List<int>? currentlyReadingBooks;
  final List<int>? toBeReadBooks;
  final List<int>? fiveStarMatchBooks;
  final int? totalMatches;
  final int? bookId;
  final bool? isCurrentlyReading;
  final bool? toBeRead;
  final double? ratings;
  final String? review;

  FellowReader({
    this.userId,
    this.userName,
    this.userProfilePicture,
    this.bookName,
    this.bookAuthor,
    this.currentlyReading,
    this.userLocation,
    this.currentlyReadingBooks,
    this.toBeReadBooks,
    this.fiveStarMatchBooks,
    this.totalMatches,
    this.bookId,
    this.isCurrentlyReading,
    this.toBeRead,
    this.ratings,
    this.review,
  });

  factory FellowReader.fromJson(Map<String, dynamic> json) => FellowReader(
        userId: json["userId"],
        userName: json["userName"],
        userProfilePicture: json["userProfilePicture"],
        bookName: json["bookName"],
        bookAuthor: json["bookAuthor"],
        currentlyReading: json["currentlyReading"],
        userLocation: json["userLocation"],
        currentlyReadingBooks: json["currentlyReadingBooks"] == null
            ? []
            : List<int>.from(json["currentlyReadingBooks"]!.map((x) => x)),
        toBeReadBooks: json["toBeReadBooks"] == null
            ? []
            : List<int>.from(json["toBeReadBooks"]!.map((x) => x)),
        fiveStarMatchBooks: json["fiveStarMatchBooks"] == null
            ? []
            : List<int>.from(json["fiveStarMatchBooks"]!.map((x) => x)),
        totalMatches: json["totalMatches"],
        bookId: json["bookId"],
        isCurrentlyReading: json["isCurrentlyReading"],
        toBeRead: json["toBeRead"],
        ratings: json["ratings"],
        review: json["review"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "userName": userName,
        "userProfilePicture": userProfilePicture,
        "bookName": bookName,
        "bookAuthor": bookAuthor,
        "currentlyReading": currentlyReading,
        "userLocation": userLocation,
        "currentlyReadingBooks": currentlyReadingBooks == null
            ? []
            : List<dynamic>.from(currentlyReadingBooks!.map((x) => x)),
        "toBeReadBooks": toBeReadBooks == null
            ? []
            : List<dynamic>.from(toBeReadBooks!.map((x) => x)),
        "fiveStarMatchBooks": fiveStarMatchBooks == null
            ? []
            : List<dynamic>.from(fiveStarMatchBooks!.map((x) => x)),
        "totalMatches": totalMatches,
        "bookId": bookId,
        "isCurrentlyReading": isCurrentlyReading,
        "toBeRead": toBeRead,
        "ratings": ratings,
        "review": review,
      };
}



// // To parse this JSON data, do
// //
// //     final fellowReaderModel = fellowReaderModelFromJson(jsonString);

// import 'dart:convert';

// FellowReaderModel fellowReaderModelFromJson(String str) =>
//     FellowReaderModel.fromJson(json.decode(str));

// String fellowReaderModelToJson(FellowReaderModel data) =>
//     json.encode(data.toJson());

// class FellowReaderModel {
//   final String? message;
//   final List<FellowReader>? data;
//   final int? count;
//   final int? statusCode;

//   FellowReaderModel({
//     this.message,
//     this.data,
//     this.count,
//     this.statusCode,
//   });

//   factory FellowReaderModel.fromJson(Map<String, dynamic> json) =>
//       FellowReaderModel(
//         message: json["message"],
//         data: json["data"] == null
//             ? []
//             : List<FellowReader>.from(
//                 json["data"]!.map((x) => FellowReader.fromJson(x))),
//         count: json["count"],
//         statusCode: json["statusCode"],
//       );

//   Map<String, dynamic> toJson() => {
//         "message": message,
//         "data": data == null
//             ? []
//             : List<dynamic>.from(data!.map((x) => x.toJson())),
//         "count": count,
//         "statusCode": statusCode,
//       };
// }

// class FellowReader {
//   final int? userId;
//   final String? userName;
//   final String? userProfilePicture;
//   final String? bookName;
//   final String? bookAuthor;
//   final bool? currentlyReading;
//   final String? userLocation;

//   FellowReader({
//     this.userId,
//     this.userName,
//     this.userProfilePicture,
//     this.bookName,
//     this.bookAuthor,
//     this.currentlyReading,
//     this.userLocation,
//   });

//   factory FellowReader.fromJson(Map<String, dynamic> json) => FellowReader(
//         userId: json["userId"],
//         userName: json["userName"],
//         userProfilePicture: json["userProfilePicture"],
//         bookName: json["bookName"],
//         bookAuthor: json["bookAuthor"],
//         currentlyReading: json["currentlyReading"],
//         userLocation: json["userLocation"],
//       );

//   Map<String, dynamic> toJson() => {
//         "userId": userId,
//         "userName": userName,
//         "userProfilePicture": userProfilePicture,
//         "bookName": bookName,
//         "bookAuthor": bookAuthor,
//         "currentlyReading": currentlyReading,
//         "userLocation": userLocation,
//       };
// }
