class ChatMessageModel {
  final String id;
  final String text;
  final int userId;
  final String userName;
  final String profileImage;
  final DateTime createdAt;
  final bool isSeen;

  ChatMessageModel({
    required this.id,
    required this.text,
    required this.userId,
    required this.userName,
    required this.profileImage,
    required this.createdAt,
    required this.isSeen,
  });

  // Convert a message to a map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'user_id': userId,
      'user_name': userName,
      'profile_image': profileImage,
      'created_at': createdAt,
      'is_seen': isSeen,
    };
  }

  // Create a ChatMessageModel from a map
  factory ChatMessageModel.fromMap(Map<String, dynamic> map) {
    return ChatMessageModel(
      id: map['id'],
      text: map['text'],
      userId: map['user_id'],
      userName: map['user_name'],
      profileImage: map['profile_image'],
      createdAt: map['created_at'],
      isSeen: map['is_seen'],
    );
  }
}
