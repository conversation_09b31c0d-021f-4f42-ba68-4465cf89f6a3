// To parse this JSON data, do
//
//     final whoReadThisModel = whoReadThisModelFromJson(jsonString);

import 'dart:convert';

WhoReadThisModel whoReadThisModelFromJson(String str) =>
    WhoReadThisModel.fromJson(json.decode(str));

String whoReadThisModelToJson(WhoReadThisModel data) =>
    json.encode(data.toJson());

class WhoReadThisModel {
  final String? message;
  final List<WhoReadThisList>? data;
  final int? count;
  final int? statusCode;

  WhoReadThisModel({
    this.message,
    this.data,
    this.count,
    this.statusCode,
  });

  factory WhoReadThisModel.fromJson(Map<String, dynamic> json) =>
      WhoReadThisModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<WhoReadThisList>.from(
                json["data"]!.map((x) => WhoReadThisList.from<PERSON>son(x))),
        count: json["count"],
        statusCode: json["statusCode"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
        "statusCode": statusCode,
      };
}

class WhoReadThisList {
  final int? userId;
  final String? userName;
  final String? userProfilePicture;
  final String? bookNames;
  final String? bookAuthors;
  final bool? isCurrentlyReading;
  final String? review;
  final double? ratings;
  final int? readingCompleteDate;

  WhoReadThisList({
    this.userId,
    this.userName,
    this.userProfilePicture,
    this.bookNames,
    this.bookAuthors,
    this.isCurrentlyReading,
    this.review,
    this.ratings,
    this.readingCompleteDate,
  });

  factory WhoReadThisList.fromJson(Map<String, dynamic> json) =>
      WhoReadThisList(
        userId: json["userId"],
        userName: json["userName"],
        userProfilePicture: json["userProfilePicture"],
        bookNames: json["bookNames"],
        bookAuthors: <AUTHORS>
        isCurrentlyReading: json["isCurrentlyReading"],
        review: json["review"],
        ratings: json["ratings"],
        readingCompleteDate: json["readingCompleteDate"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "userName": userName,
        "userProfilePicture": userProfilePicture,
        "bookNames": bookNames,
        "bookAuthors": bookAuthors,
        "isCurrentlyReading": isCurrentlyReading,
        "review": review,
        "ratings": ratings,
        "readingCompleteDate": readingCompleteDate,
      };
}
