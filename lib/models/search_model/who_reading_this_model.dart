// To parse this JSON data, do
//
//     final whoReadingThisModel = whoReadingThisModelFromJson(jsonString);

import 'dart:convert';

WhoReadingThisModel whoReadingThisModelFromJson(String str) =>
    WhoReadingThisModel.fromJson(json.decode(str));

String whoReadingThisModelToJson(WhoReadingThisModel data) =>
    json.encode(data.toJson());

class WhoReadingThisModel {
  final String? message;
  final List<WhoReadingThisList>? data;
  final int? count;
  final int? statusCode;

  WhoReadingThisModel({
    this.message,
    this.data,
    this.count,
    this.statusCode,
  });

  factory WhoReadingThisModel.fromJson(Map<String, dynamic> json) =>
      WhoReadingThisModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<WhoReadingThisList>.from(
                json["data"]!.map((x) => WhoReadingThisList.fromJson(x))),
        count: json["count"],
        statusCode: json["statusCode"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
        "statusCode": statusCode,
      };
}

class WhoReadingThisList {
  final int? userId;
  final String? userName;
  final String? userProfilePicture;
  final String? bookName;
  final String? bookAuthor;
  final bool? isCurrentlyReading;
  final dynamic review;

  WhoReadingThisList({
    this.userId,
    this.userName,
    this.userProfilePicture,
    this.bookName,
    this.bookAuthor,
    this.isCurrentlyReading,
    this.review,
  });

  factory WhoReadingThisList.fromJson(Map<String, dynamic> json) =>
      WhoReadingThisList(
        userId: json["userId"],
        userName: json["userName"],
        userProfilePicture: json["userProfilePicture"],
        bookName: json["bookName"],
        bookAuthor: json["bookAuthor"],
        isCurrentlyReading: json["isCurrentlyReading"],
        review: json["review"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "userName": userName,
        "userProfilePicture": userProfilePicture,
        "bookName": bookName,
        "bookAuthor": bookAuthor,
        "isCurrentlyReading": isCurrentlyReading,
        "review": review,
      };
}
