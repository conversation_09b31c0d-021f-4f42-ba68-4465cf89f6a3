// To parse this JSON data, do
//
//     final whoIntoThisBookModel = whoIntoThisBookModelFromJson(jsonString);

import 'dart:convert';

WhoIntoThisBookModel whoIntoThisBookModelFromJson(String str) =>
    WhoIntoThisBookModel.fromJson(json.decode(str));

String whoIntoThisBookModelToJson(WhoIntoThisBookModel data) =>
    json.encode(data.toJson());

class WhoIntoThisBookModel {
  final String? message;
  final int? statusCode;
  final List<WhoIntoThisBookList>? data;

  WhoIntoThisBookModel({
    this.message,
    this.statusCode,
    this.data,
  });

  // factory WhoIntoThisBookModel.fromJson(Map<String, dynamic> json) =>
  //     WhoIntoThisBookModel(
  //       message: json["message"],
  //       statusCode: json["statusCode"],
  //       data: json["data"] == null
  //           ? []
  //           : List<WhoIntoThisBookList>.from((json["data"] as List)
  //               .map((x) => WhoIntoThisBookList.fromJson(x))),

  //       //  json["data"] == null
  //       //     ? []
  //       //     : List<WhoIntoThisBookList>.from(
  //       //         json["data"]!.map((x) => WhoIntoThisBookList.fromJson(x))),
  //     );

  factory WhoIntoThisBookModel.fromJson(Map<String, dynamic> json) =>
      WhoIntoThisBookModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] is List
            ? List<WhoIntoThisBookList>.from(
                (json["data"] as List)
                    .map((x) => WhoIntoThisBookList.fromJson(x)),
              )
            : [], // Handle non-list cases by providing an empty list
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data == null
            ? []
            : List<Map<String, dynamic>>.from(data!.map((x) => x.toJson())),

        /// List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class WhoIntoThisBookList {
  final WhoIntoThisBooks? whoIntoThisBooks;

  WhoIntoThisBookList({
    this.whoIntoThisBooks,
  });

  factory WhoIntoThisBookList.fromJson(Map<String, dynamic> json) =>
      WhoIntoThisBookList(
        whoIntoThisBooks: json["whoIntoThisBooks"] == null
            ? null
            : WhoIntoThisBooks.fromJson(json["whoIntoThisBooks"]),
      );

  Map<String, dynamic> toJson() => {
        "whoIntoThisBooks": whoIntoThisBooks?.toJson(),
      };
}

class WhoIntoThisBooks {
  final List<Book>? books;
  final int? totalCount;

  WhoIntoThisBooks({
    this.books,
    this.totalCount,
  });

  factory WhoIntoThisBooks.fromJson(Map<String, dynamic> json) =>
      WhoIntoThisBooks(
        books: json["books"] is List
            ? List<Book>.from(
                (json["books"] as List).map((x) => Book.fromJson(x)),
              )
            : [], // Handle non-list cases by providing an empty list
        totalCount: json["totalCount"],
      );

  Map<String, dynamic> toJson() => {
        "books": books == null
            ? []
            : List<Map<String, dynamic>>.from(books!.map((x) => x.toJson())),
        "totalCount": totalCount,
      };
}

class Book {
  final String? bookAuthor;
  // final int? bookId;
  final List<int>? bookId;
  final String? bookNames;

  Book({
    this.bookAuthor,
    this.bookId,
    this.bookNames,
  });

  factory Book.fromJson(Map<String, dynamic> json) => Book(
        bookAuthor: json["book_author"],
        // bookId: json["book_id"],
        bookId: json["book_id"] == null
            ? []
            : List<int>.from(json["book_id"]!.map((x) => x)),
        bookNames: json["book_names"],
      );

  Map<String, dynamic> toJson() => {
        "book_author": bookAuthor,
        // "book_id": bookId,
        "book_id":
            bookId == null ? [] : List<dynamic>.from(bookId!.map((x) => x)),
        "book_names": bookNames,
      };
}


// class WhoIntoThisBooks {
//   final List<Book>? books;
//   final int? totalCount;

//   WhoIntoThisBooks({
//     this.books,
//     this.totalCount,
//   });

//   factory WhoIntoThisBooks.fromJson(Map<String, dynamic> json) =>
//       WhoIntoThisBooks(
//         books: json["books"] == null
//             ? []
//             : List<Book>.from(json["books"]!.map((x) => Book.fromJson(x))),
//         totalCount: json["totalCount"],
//       );

//   Map<String, dynamic> toJson() => {
//         "books": books == null
//             ? []
//             : List<Map<String, dynamic>>.from(books!.map((x) => x.toJson())),
//         "totalCount": totalCount,
//       };
// }

