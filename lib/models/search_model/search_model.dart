// To parse this JSON data, do
//
//     final searchModel = searchModelFromJson(jsonString);

import 'dart:convert';

SearchModel searchModelFromJson(String str) =>
    SearchModel.fromJson(json.decode(str));

String searchModelToJson(SearchModel data) => json.encode(data.toJson());

class SearchModel {
  final String? message;
  final int? statusCode;
  final SearchData? data;

  SearchModel({
    this.message,
    this.statusCode,
    this.data,
  });

  factory SearchModel.fromJson(Map<String, dynamic> json) => SearchModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] == null ? null : SearchData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data?.toJson(),
      };
}

class SearchData {
  final List<WhoReadThis>? whoReadThis;
  final List<WhoReadingThis>? whoReadingThis;
  final List<Profile>? profiles;
  final List<DataBookClub>? bookClubs;

  SearchData({
    this.whoReadThis,
    this.whoReadingThis,
    this.profiles,
    this.bookClubs,
  });

  factory SearchData.fromJson(Map<String, dynamic> json) => SearchData(
        whoReadThis: json["whoReadThis"] == null
            ? []
            : List<WhoReadThis>.from(
                json["whoReadThis"]!.map((x) => WhoReadThis.fromJson(x))),
        whoReadingThis: json["whoReadingThis"] == null
            ? []
            : List<WhoReadingThis>.from(
                json["whoReadingThis"]!.map((x) => WhoReadingThis.fromJson(x))),
        profiles: json["profiles"] == null
            ? []
            : List<Profile>.from(
                json["profiles"]!.map((x) => Profile.fromJson(x))),
        bookClubs: json["bookClubs"] == null
            ? []
            : List<DataBookClub>.from(
                json["bookClubs"]!.map((x) => DataBookClub.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "whoReadThis": whoReadThis == null
            ? []
            : List<dynamic>.from(whoReadThis!.map((x) => x.toJson())),
        "whoReadingThis": whoReadingThis == null
            ? []
            : List<dynamic>.from(whoReadingThis!.map((x) => x.toJson())),
        "profiles": profiles == null
            ? []
            : List<dynamic>.from(profiles!.map((x) => x.toJson())),
        "bookClubs": bookClubs == null
            ? []
            : List<dynamic>.from(bookClubs!.map((x) => x.toJson())),
      };
}

class DataBookClub {
  final int? bookId;
  final String? bookName;
  final String? bookAuthor;
  final List<BookClub>? bookClubs;

  DataBookClub({
    this.bookId,
    this.bookName,
    this.bookAuthor,
    this.bookClubs,
  });

  factory DataBookClub.fromJson(Map<String, dynamic> json) => DataBookClub(
        bookId: json["bookId"],
        bookName: json["bookName"],
        bookAuthor: json['bookAuthor'],
        bookClubs: json["bookClubs"] == null
            ? []
            : List<BookClub>.from(
                json["bookClubs"]!.map((x) => BookClub.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "bookId": bookId,
        "bookName": bookName,
        "bookClubs": bookClubs == null
            ? []
            : List<dynamic>.from(bookClubs!.map((x) => x.toJson())),
      };
}

class BookClub {
  final int? bookClubId;
  final String? bookClubName;
  // final String? bookAuthor;
  final String? bookClubType;
  final int? totalPositions;
  final int? totalMembers;
  final int? totalVacancies;
  final String? clubCount;
  final String? clubCharter;
  final MeetingDetails? meetingDetails;

  BookClub({
    this.bookClubId,
    this.bookClubName,
    // this.bookAuthor,
    this.bookClubType,
    this.totalMembers,
    this.totalPositions,
    this.totalVacancies,
    this.meetingDetails,
    this.clubCount,
    this.clubCharter,
  });

  factory BookClub.fromJson(Map<String, dynamic> json) => BookClub(
        bookClubId: json["bookClubId"],
        bookClubName: json["bookClubName"],
        // bookAuthor: json['bookAuthor'],
        bookClubType: json['bookClubType'],
        totalMembers: json['totalMembers'],
        totalPositions: json['totalPositions'],
        totalVacancies: json['totalVacancies'],
        clubCount: json['clubCount'],
        clubCharter: json['clubCharter'],
        meetingDetails: json["meetingDetails"] == null
            ? null
            : MeetingDetails.fromJson(json["meetingDetails"]),
      );

  Map<String, dynamic> toJson() => {
        "bookClubId": bookClubId,
        "bookClubName": bookClubName,
        "meetingDetails": meetingDetails?.toJson(),
        "totalVacancies": totalVacancies,
        "totalMembers": totalMembers,
        "totalPositions": totalPositions,
        "bookClubType": bookClubType,
        "clubCount": clubCount,
        "clubCharter": clubCharter,
      };
}

class MeetingDetails {
  final int? meetingId;
  final int? meetingDate;
  final int? meetingStartTime;
  final dynamic meetingDuration;
  final String? discussionQuestions;
  final dynamic meetingStatus;
  final int? meetingEndTime;
  final String? meetingAlerts;
  final String? partOfBookCovered;

  MeetingDetails({
    this.meetingId,
    this.meetingDate,
    this.meetingStartTime,
    this.meetingDuration,
    this.discussionQuestions,
    this.meetingStatus,
    this.meetingEndTime,
    this.meetingAlerts,
    this.partOfBookCovered,
  });

  factory MeetingDetails.fromJson(Map<String, dynamic> json) => MeetingDetails(
        meetingId: json["meetingId"],
        meetingDate: json["meetingDate"],
        meetingStartTime: json["meetingStartTime"],
        meetingDuration: json["meetingDuration"],
        discussionQuestions: json["discussionQuestions"],
        meetingStatus: json["meetingStatus"],
        meetingEndTime: json["meetingEndTime"],
        meetingAlerts: json["meetingAlerts"],
        partOfBookCovered: json["partOfBookCovered"],
      );

  Map<String, dynamic> toJson() => {
        "meetingId": meetingId,
        "meetingDate": meetingDate,
        "meetingStartTime": meetingStartTime,
        "meetingDuration": meetingDuration,
        "discussionQuestions": discussionQuestions,
        "meetingStatus": meetingStatus,
        "meetingEndTime": meetingEndTime,
        "meetingAlerts": meetingAlerts,
        "partOfBookCovered": partOfBookCovered,
      };
}

class Profile {
  final int? userId;
  final String? userName;
  // final int? userPhoneNumber;
  // final String? userEmailId;
  final String? userHandle;
  // final dynamic userGroup;
  // final String? portalType;
  // final List<dynamic>? roles;
  // final dynamic userCredSalt;
  // final dynamic userCred;
  // final dynamic userCredHistory;
  // final dynamic userIsActive;
  // final dynamic userIsLocked;
  // final bool? userIsDeleted;
  // final dynamic userLastSuccessLogin;
  // final dynamic userLastFailedLogin;
  // final dynamic userLastLogoutTime;
  // final dynamic userLastPwdChangeTime;
  // final dynamic userLastStatusUpdateTime;
  // final dynamic userNoFailedAttempts;
  // final dynamic userBlockReleaseTime;
  // final dynamic userCreatedDate;
  // final dynamic userUpdatedDate;
  // final dynamic user30DayTrialEndDate;
  // final bool userIsSubscriptionActive;
  // final dynamic userSubscriptionStartedDate;
  // final dynamic userSubscriptionEndedDate;
  // final dynamic userActiveStatus;
  final String? userProfilePicture;
  // final String? userLocation;
  // final String? userBio;
  // final bool userClubInvitation;

  Profile({
    this.userId,
    this.userName,
    // this.userPhoneNumber,
    // this.userEmailId,
    this.userHandle,
    // this.userGroup,
    // this.portalType,
    // this.roles,
    // this.userCredSalt,
    // this.userCred,
    // this.userCredHistory,
    // this.userIsActive,
    // this.userIsLocked,
    // this.userIsDeleted,
    // this.userLastSuccessLogin,
    // this.userLastFailedLogin,
    // this.userLastLogoutTime,
    // this.userLastPwdChangeTime,
    // this.userLastStatusUpdateTime,
    // this.userNoFailedAttempts,
    // this.userBlockReleaseTime,
    // this.userCreatedDate,
    // this.userUpdatedDate,
    // this.user30DayTrialEndDate,
    // this.userIsSubscriptionActive = false,
    // this.userSubscriptionStartedDate,
    // this.userSubscriptionEndedDate,
    // this.userActiveStatus,
    this.userProfilePicture,
    // this.userLocation,
    // this.userBio,
    // this.userClubInvitation = false,
  });

  factory Profile.fromJson(Map<String, dynamic> json) => Profile(
        userId: json["userId"],
        userName: json["userName"],
        // userPhoneNumber: json["userPhoneNumber"],
        // userEmailId: json["userEmailId"],
        userHandle: json["userHandle"],
        // userGroup: json["userGroup"],
        // portalType: json["portalType"],
        // roles: json["roles"] == null
        //     ? []
        //     : List<dynamic>.from(json["roles"]!.map((x) => x)),
        // userCredSalt: json["userCredSalt"],
        // userCred: json["userCred"],
        // userCredHistory: json["userCredHistory"],
        // userIsActive: json["userIsActive"],
        // userIsLocked: json["userIsLocked"],
        // userIsDeleted: json["userIsDeleted"],
        // userLastSuccessLogin: json["userLastSuccessLogin"],
        // userLastFailedLogin: json["userLastFailedLogin"],
        // userLastLogoutTime: json["userLastLogoutTime"],
        // userLastPwdChangeTime: json["userLastPwdChangeTime"],
        // userLastStatusUpdateTime: json["userLastStatusUpdateTime"],
        // userNoFailedAttempts: json["userNoFailedAttempts"],
        // userBlockReleaseTime: json["userBlockReleaseTime"],
        // userCreatedDate: json["userCreatedDate"],
        // userUpdatedDate: json["userUpdatedDate"],
        // user30DayTrialEndDate: json["user30DayTrialEndDate"],
        // userIsSubscriptionActive: json["userIsSubscriptionActive"],
        // userSubscriptionStartedDate: json["userSubscriptionStartedDate"],
        // userSubscriptionEndedDate: json["userSubscriptionEndedDate"],
        // userActiveStatus: json["userActiveStatus"],
        userProfilePicture: json["userProfilePicture"],
        // userLocation: json["userLocation"],
        // userBio: json["userBio"],
        // userClubInvitation: json["userClubInvitation"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "userName": userName,
        // "userPhoneNumber": userPhoneNumber,
        // "userEmailId": userEmailId,
        "userHandle": userHandle,
        // "userGroup": userGroup,
        // "portalType": portalType,
        // "roles": roles == null ? [] : List<dynamic>.from(roles!.map((x) => x)),
        // "userCredSalt": userCredSalt,
        // "userCred": userCred,
        // "userCredHistory": userCredHistory,
        // "userIsActive": userIsActive,
        // "userIsLocked": userIsLocked,
        // "userIsDeleted": userIsDeleted,
        // "userLastSuccessLogin": userLastSuccessLogin,
        // "userLastFailedLogin": userLastFailedLogin,
        // "userLastLogoutTime": userLastLogoutTime,
        // "userLastPwdChangeTime": userLastPwdChangeTime,
        // "userLastStatusUpdateTime": userLastStatusUpdateTime,
        // "userNoFailedAttempts": userNoFailedAttempts,
        // "userBlockReleaseTime": userBlockReleaseTime,
        // "userCreatedDate": userCreatedDate,
        // "userUpdatedDate": userUpdatedDate,
        // "user30DayTrialEndDate": user30DayTrialEndDate,
        // "userIsSubscriptionActive": userIsSubscriptionActive,
        // "userSubscriptionStartedDate": userSubscriptionStartedDate,
        // "userSubscriptionEndedDate": userSubscriptionEndedDate,
        // "userActiveStatus": userActiveStatus,
        "userProfilePicture": userProfilePicture,
        // "userLocation": userLocation,
        // "userBio": userBio,
        // "userClubInvitation": userClubInvitation,
      };
}

class WhoReadThis {
  final int? bookId;
  final String? bookName;
  final String? bookAuthor;
  final List<WhoReadThisUser>? users;

  WhoReadThis({
    this.bookId,
    this.bookName,
    this.bookAuthor,
    this.users,
  });

  factory WhoReadThis.fromJson(Map<String, dynamic> json) => WhoReadThis(
        bookId: json["bookId"],
        bookName: json["bookName"],
        bookAuthor: json['bookAuthor'],
        users: json["users"] == null
            ? []
            : List<WhoReadThisUser>.from(
                json["users"]!.map((x) => WhoReadThisUser.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "bookId": bookId,
        "bookName": bookName,
        "users": users == null
            ? []
            : List<dynamic>.from(users!.map((x) => x.toJson())),
      };
}

class WhoReadThisUser {
  final int? userId;
  final String? userName;
  final String? userProfilePicture;
  // final int? bookClubId;
  // final String? bookClubName;
  final double? bookRatings;
  final String? bookReview;
  final int? readingCompleteDate;

  WhoReadThisUser({
    this.userId,
    this.userName,
    this.userProfilePicture,
    // this.bookClubId,
    // this.bookClubName,
    this.bookRatings,
    this.bookReview,
    this.readingCompleteDate,
  });

  factory WhoReadThisUser.fromJson(Map<String, dynamic> json) =>
      WhoReadThisUser(
        userId: json["userId"],
        userName: json["userName"],
        userProfilePicture: json["userProfilePicture"],
        // bookClubId: json["bookClubId"],
        // bookClubName: json["bookClubName"],
        bookRatings: json["bookRatings"]?.toDouble(),
        bookReview: json['bookReview'],
        readingCompleteDate: json['readingCompleteDate'],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "userName": userName,
        "userProfilePicture": userProfilePicture,
        // "bookClubId": bookClubId,
        // "bookClubName": bookClubName,
        "bookRatings": bookRatings,
        "bookReview": bookReview,
        "readingCompleteDate": readingCompleteDate,
      };
}

class WhoReadingThis {
  final int? bookId;
  final String? bookName;
  final String? bookAuthor;
  final List<WhoReadingThisUser>? users;

  WhoReadingThis({
    this.bookId,
    this.bookName,
    this.bookAuthor,
    this.users,
  });

  factory WhoReadingThis.fromJson(Map<String, dynamic> json) => WhoReadingThis(
        bookId: json["bookId"],
        bookName: json["bookName"],
        bookAuthor: json['bookAuthor'],
        users: json["users"] == null
            ? []
            : List<WhoReadingThisUser>.from(
                json["users"]!.map((x) => WhoReadingThisUser.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "bookId": bookId,
        "bookName": bookName,
        "users": users == null
            ? []
            : List<dynamic>.from(users!.map((x) => x.toJson())),
      };
}

class WhoReadingThisUser {
  final int? userId;
  final String? userName;
  final String? userProfilePicture;
  final double? bookRatings;

  WhoReadingThisUser({
    this.userId,
    this.userName,
    this.userProfilePicture,
    this.bookRatings,
  });

  factory WhoReadingThisUser.fromJson(Map<String, dynamic> json) =>
      WhoReadingThisUser(
        userId: json["userId"],
        userName: json["userName"],
        userProfilePicture: json["userProfilePicture"],
        bookRatings: json["bookRatings"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "userName": userName,
        "userProfilePicture": userProfilePicture,
        "bookRatings": bookRatings,
      };
}
