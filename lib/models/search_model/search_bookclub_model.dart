// To parse this JSON data, do
//
//     final searchBookClubModel = searchBookClubModelFromJson(jsonString);

import 'dart:convert';

SearchBookClubModel searchBookClubModelFromJson(String str) =>
    SearchBookClubModel.fromJson(json.decode(str));

String searchBookClubModelToJson(SearchBookClubModel data) =>
    json.encode(data.toJson());

class SearchBookClubModel {
  final String? message;
  final List<SearchClubs>? data;
  final int? count;
  final int? statusCode;

  SearchBookClubModel({
    this.message,
    this.data,
    this.count,
    this.statusCode,
  });

  factory SearchBookClubModel.fromJson(Map<String, dynamic> json) =>
      SearchBookClubModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<SearchClubs>.from(
                json["data"]!.map((x) => SearchClubs.fromJson(x))),
        count: json["count"],
        statusCode: json["statusCode"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
        "statusCode": statusCode,
      };
}

class SearchClubs {
  final int? bookClubId;
  final String? bookClubType;
  final int? userId;
  final int? totalPositions;
  final int? totalMembers;
  final int? totalVacancies;
  final int? clubCreatedDate;
  final String? clubCharter;
  final String? memberReqPrompt;
  final int? bookId;
  final String? bookClubName;
  final String? clubStatus;
  final dynamic bookName;
  final dynamic bookAuthor;
  final dynamic clubCount;
  final dynamic incomingRequest;
  final dynamic memberUserIds;

  SearchClubs({
    this.bookClubId,
    this.bookClubType,
    this.userId,
    this.totalPositions,
    this.totalMembers,
    this.totalVacancies,
    this.clubCreatedDate,
    this.clubCharter,
    this.memberReqPrompt,
    this.bookId,
    this.bookClubName,
    this.clubStatus,
    this.bookName,
    this.bookAuthor,
    this.clubCount,
    this.incomingRequest,
    this.memberUserIds,
  });

  factory SearchClubs.fromJson(Map<String, dynamic> json) => SearchClubs(
        bookClubId: json["bookClubId"],
        bookClubType: json["bookClubType"],
        userId: json["userId"],
        totalPositions: json["totalPositions"],
        totalMembers: json["totalMembers"],
        totalVacancies: json["totalVacancies"],
        clubCreatedDate: json["clubCreatedDate"],
        clubCharter: json["clubCharter"],
        memberReqPrompt: json["memberReqPrompt"],
        bookId: json["bookId"],
        bookClubName: json["bookClubName"],
        clubStatus: json["clubStatus"],
        bookName: json["bookName"],
        bookAuthor: json["bookAuthor"],
        clubCount: json["clubCount"],
        incomingRequest: json["incomingRequest"],
        memberUserIds: json["memberUserIds"],
      );

  Map<String, dynamic> toJson() => {
        "bookClubId": bookClubId,
        "bookClubType": bookClubType,
        "userId": userId,
        "totalPositions": totalPositions,
        "totalMembers": totalMembers,
        "totalVacancies": totalVacancies,
        "clubCreatedDate": clubCreatedDate,
        "clubCharter": clubCharter,
        "memberReqPrompt": memberReqPrompt,
        "bookId": bookId,
        "bookClubName": bookClubName,
        "clubStatus": clubStatus,
        "bookName": bookName,
        "bookAuthor": bookAuthor,
        "clubCount": clubCount,
        "incomingRequest": incomingRequest,
        "memberUserIds": memberUserIds,
      };
}
