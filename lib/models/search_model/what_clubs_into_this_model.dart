// To parse this JSON data, do
//
//     final whatClubsIntoThisModel = whatClubsIntoThisModelFromJson(jsonString);

import 'dart:convert';

WhatClubsIntoThisModel whatClubsIntoThisModelFromJson(String str) =>
    WhatClubsIntoThisModel.fromJson(json.decode(str));

String whatClubsIntoThisModelToJson(WhatClubsIntoThisModel data) =>
    json.encode(data.toJson());

class WhatClubsIntoThisModel {
  final String? message;
  final List<WhatClubsIntoThisList>? data;
  final int? count;
  final int? statusCode;

  WhatClubsIntoThisModel({
    this.message,
    this.data,
    this.count,
    this.statusCode,
  });

  factory WhatClubsIntoThisModel.fromJson(Map<String, dynamic> json) =>
      WhatClubsIntoThisModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<WhatClubsIntoThisList>.from(
                json["data"]!.map((x) => WhatClubsIntoThisList.fromJson(x))),
        count: json["count"],
        statusCode: json["statusCode"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
        "statusCode": statusCode,
      };
}

class WhatClubsIntoThisList {
  final int? bookClubId;
  final String? bookClubName;
  final int? meetingId;
  final String? bookName;
  final int? meetingDate;
  final int? meetingStartTime;
  final String? bookAuthor;
  final int? bookId;
  final String? bookClubType;
  final int? totalPositions;
  final int? totalMembers;
  final int? totalVacancies;
  final String? clubCount;
  final String? clubCharter;
  final String? clubStatus;

  WhatClubsIntoThisList({
    this.bookClubId,
    this.bookClubName,
    this.meetingId,
    this.bookName,
    this.meetingDate,
    this.meetingStartTime,
    this.bookAuthor,
    this.bookId,
    this.bookClubType,
    this.totalPositions,
    this.totalMembers,
    this.totalVacancies,
    this.clubCount,
    this.clubCharter,
    this.clubStatus,
  });

  factory WhatClubsIntoThisList.fromJson(Map<String, dynamic> json) =>
      WhatClubsIntoThisList(
        bookClubId: json["book_club_id"],
        bookClubName: json["book_club_name"],
        meetingId: json["meeting_id"],
        bookName: json["book_name"],
        meetingDate: json["meeting_date"],
        meetingStartTime: json["meeting_start_time"],
        bookAuthor: json["book_author"],
        bookId: json["book_id"],
        bookClubType: json["book_club_type"],
        totalPositions: json["total_positions"],
        totalMembers: json["total_members"],
        totalVacancies: json["total_vacancies"],
        clubCount: json["club_count"],
        clubCharter: json["club_charter"],
        clubStatus: json["club_status"],
      );

  Map<String, dynamic> toJson() => {
        "book_club_id": bookClubId,
        "book_club_name": bookClubName,
        "meeting_id": meetingId,
        "book_name": bookName,
        "meeting_date": meetingDate,
        "meeting_start_time": meetingStartTime,
        "book_author": bookAuthor,
        "book_id": bookId,
        "book_club_type": bookClubType,
        "total_positions": totalPositions,
        "total_members": totalMembers,
        "total_vacancies": totalVacancies,
        "club_count": clubCount,
        "club_charter": clubCharter,
        "club_status": clubStatus,
      };
}
