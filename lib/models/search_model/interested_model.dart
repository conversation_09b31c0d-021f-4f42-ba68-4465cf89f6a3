// To parse this JSON data, do
//
//     final interestedModel = interestedModelFromJson(jsonString);

import 'dart:convert';

InterestedModel interestedModelFromJson(String str) =>
    InterestedModel.fromJson(json.decode(str));

String interestedModelToJson(InterestedModel data) =>
    json.encode(data.toJson());

class InterestedModel {
  final String? message;
  final List<InterestedList>? data;
  final int? count;
  final int? statusCode;

  InterestedModel({
    this.message,
    this.data,
    this.count,
    this.statusCode,
  });

  factory InterestedModel.fromJson(Map<String, dynamic> json) =>
      InterestedModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<InterestedList>.from(
                json["data"]!.map((x) => InterestedList.fromJson(x))),
        count: json["count"],
        statusCode: json["statusCode"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
        "statusCode": statusCode,
      };
}

class InterestedList {
  final int? userId;
  final String? userName;
  final String? userProfilePicture;
  final String? bookNames;
  final String? bookAuthors;
  final bool? toBeRead;
  final String? review;

  InterestedList({
    this.userId,
    this.userName,
    this.userProfilePicture,
    this.bookNames,
    this.bookAuthors,
    this.toBeRead,
    this.review,
  });

  factory InterestedList.fromJson(Map<String, dynamic> json) => InterestedList(
        userId: json["userId"],
        userName: json["userName"],
        userProfilePicture: json["userProfilePicture"],
        bookNames: json["bookNames"],
        bookAuthors: <AUTHORS>
        toBeRead: json["toBeRead"],
        review: json["review"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "userName": userName,
        "userProfilePicture": userProfilePicture,
        "bookNames": bookNames,
        "bookAuthors": bookAuthors,
        "toBeRead": toBeRead,
        "review": review,
      };
}
