// To parse this JSON data, do
//
//     final whoIntoThisAuthorModel = whoIntoThisAuthorModelFromJson(jsonString);

import 'dart:convert';

WhoIntoThisAuthorModel whoIntoThisAuthorModelFromJson(String str) =>
    WhoIntoThisAuthorModel.fromJson(json.decode(str));

String whoIntoThisAuthorModelToJson(WhoIntoThisAuthorModel data) =>
    json.encode(data.toJson());

class WhoIntoThisAuthorModel {
  final String? message;
  final int? statusCode;
  final List<WhoIntoThisAuthorList>? data;

  WhoIntoThisAuthorModel({
    this.message,
    this.statusCode,
    this.data,
  });

  // factory WhoIntoThisAuthorModel.fromJson(Map<String, dynamic> json) =>
  //     WhoIntoThisAuthorModel(
  //       message: json["message"],
  //       statusCode: json["statusCode"],
  //       data: json["data"] == null
  //           ? []
  //           : List<WhoIntoThisAuthorList>.from((json["data"] as List)
  //               .map((x) => WhoIntoThisAuthorList.fromJson(x))),
  //     );

  factory WhoIntoThisAuthorModel.fromJson(Map<String, dynamic> json) =>
      WhoIntoThisAuthorModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] is List
            ? List<WhoIntoThisAuthorList>.from(
                (json["data"] as List)
                    .map((x) => WhoIntoThisAuthorList.fromJson(x)),
              )
            : [], // Handle non-list cases by providing an empty list
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class WhoIntoThisAuthorList {
  final WhoIntoThisAuthor? whoIntoThisAuthor;

  WhoIntoThisAuthorList({
    this.whoIntoThisAuthor,
  });

  factory WhoIntoThisAuthorList.fromJson(Map<String, dynamic> json) =>
      WhoIntoThisAuthorList(
        whoIntoThisAuthor: json["whoIntoThisAuthor"] == null
            ? null
            : WhoIntoThisAuthor.fromJson(json["whoIntoThisAuthor"]),
      );

  Map<String, dynamic> toJson() => {
        "whoIntoThisAuthor": whoIntoThisAuthor?.toJson(),
      };
}

class WhoIntoThisAuthor {
  final List<BookAuthor>? books;
  final int? totalCount;

  WhoIntoThisAuthor({
    this.books,
    this.totalCount,
  });

  factory WhoIntoThisAuthor.fromJson(Map<String, dynamic> json) =>
      WhoIntoThisAuthor(
        books: json["books"] == null
            ? []
            : List<BookAuthor>.from(
                (json["books"] as List).map((x) => BookAuthor.fromJson(x))),
        totalCount: json["totalCount"],
      );

  Map<String, dynamic> toJson() => {
        "books": books == null
            ? []
            : List<dynamic>.from(books!.map((x) => x.toJson())),
        "totalCount": totalCount,
      };
}

class BookAuthor {
  final List<int>? bookIds;
  final String? bookAuthor;

  BookAuthor({
    this.bookIds,
    this.bookAuthor,
  });

  factory BookAuthor.fromJson(Map<String, dynamic> json) => BookAuthor(
        bookIds: json["book_ids"] == null
            ? []
            : List<int>.from((json["book_ids"] as List).map((x) => x)),
        bookAuthor: json["book_author"],
      );

  Map<String, dynamic> toJson() => {
        "book_ids":
            bookIds == null ? [] : List<dynamic>.from(bookIds!.map((x) => x)),
        "book_author": bookAuthor,
      };
}
