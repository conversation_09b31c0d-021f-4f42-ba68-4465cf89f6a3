import 'dart:convert';

// Helper function to parse the main JSON
AppVersionModel appVersionConfigFromJson(String str) =>
    AppVersionModel.fromJson(json.decode(str));
String appVersionConfigToJson(AppVersionModel data) =>
    json.encode(data.toJson());

class AppVersionModel {
  final PlatformVersionInfo android;
  final PlatformVersionInfo ios;

  AppVersionModel({
    required this.android,
    required this.ios,
  });

  factory AppVersionModel.fromJson(Map<String, dynamic> json) =>
      AppVersionModel(
        android: PlatformVersionInfo.fromJson(json["android"]),
        ios: PlatformVersionInfo.fromJson(json["ios"]),
      );

  Map<String, dynamic> toJson() => {
        "android": android.toJson(),
        "ios": ios.toJson(),
      };
}

class PlatformVersionInfo {
  final String downloadUrl;
  final VersionDetails version;
  final StatusDetails status;

  PlatformVersionInfo({
    required this.downloadUrl,
    required this.version,
    required this.status,
  });

  factory PlatformVersionInfo.fromJson(Map<String, dynamic> json) =>
      PlatformVersionInfo(
        downloadUrl: json["download_url"],
        version: VersionDetails.fromJson(json["version"]),
        status: StatusDetails.fromJson(json["status"]),
      );

  Map<String, dynamic> toJson() => {
        "download_url": downloadUrl,
        "version": version.toJson(),
        "status": status.toJson(),
      };
}

class StatusDetails {
  final bool active;
  final Map<String, dynamic> message;

  StatusDetails({
    required this.active,
    required this.message,
  });

  factory StatusDetails.fromJson(Map<String, dynamic> json) => StatusDetails(
        active: json["active"],
        message:
            json["message"] ?? {}, // Ensure message is at least an empty map
      );

  Map<String, dynamic> toJson() => {
        "active": active,
        "message": message,
      };
}

class VersionDetails {
  final String latest;
  final String minimum;

  VersionDetails({
    required this.latest,
    required this.minimum,
  });

  factory VersionDetails.fromJson(Map<String, dynamic> json) => VersionDetails(
        latest: json["latest"],
        minimum: json["minimum"],
      );

  Map<String, dynamic> toJson() => {
        "latest": latest,
        "minimum": minimum,
      };
}
