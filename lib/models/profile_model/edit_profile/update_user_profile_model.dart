// To parse this JSON data, do
//
//     final userProfileUpdateModel = userProfileUpdateModelFromJson(jsonString);

import 'dart:convert';

UserProfileUpdateModel userProfileUpdateModelFromJson(String str) =>
    UserProfileUpdateModel.fromJson(json.decode(str));

String userProfileUpdateModelToJson(UserProfileUpdateModel data) =>
    json.encode(data.toJson());

class UserProfileUpdateModel {
  final int? userId;
  final String userName;
  final String? userHandle;
  final String? userPhoneNumber;
  final String? userEmailId;
  final bool? userIsActive;
  final bool? userIsLocked;
  final String? userProfilePicture;
  final String userLocation;
  final String userBio;
  final bool? userClubInvitation;

  UserProfileUpdateModel({
    this.userId,
    required this.userName,
    this.userHandle,
    this.userPhoneNumber,
    this.userEmailId,
    this.userIsActive,
    this.userIsLocked,
    this.userProfilePicture,
    required this.userLocation,
    required this.userBio,
    this.userClubInvitation,
  });

  factory UserProfileUpdateModel.fromJson(Map<String, dynamic> json) =>
      UserProfileUpdateModel(
        userId: json["userId"],
        userName: json["userName"],
        userHandle: json["userHandle"],
        userPhoneNumber: json["userPhoneNumber"],
        userEmailId: json["userEmailId"],
        userIsActive: json["userIsActive"],
        userIsLocked: json["userIsLocked"],
        userProfilePicture: json["userProfilePicture"],
        userLocation: json["userLocation"],
        userBio: json["userBio"],
        userClubInvitation: json["userClubInvitation"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "userName": userName,
        "userHandle": userHandle,
        "userPhoneNumber": userPhoneNumber,
        "userEmailId": userEmailId,
        "userIsActive": userIsActive,
        "userIsLocked": userIsLocked,
        "userProfilePicture": userProfilePicture,
        "userLocation": userLocation,
        "userBio": userBio,
        "userClubInvitation": userClubInvitation,
      };
}
