// To parse this JSON data, do
//
//     final locationModel = locationModelFromJson(jsonString);

import 'dart:convert';

LocationModel locationModelFromJson(String str) =>
    LocationModel.fromJson(json.decode(str));

String locationModelToJson(LocationModel data) => json.encode(data.toJson());

class LocationModel {
  final String? message;
  final int? statusCode;
  final List<Location>? data;

  LocationModel({
    this.message,
    this.statusCode,
    this.data,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) => LocationModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] == null
            ? []
            : List<Location>.from(
                json["data"]!.map((x) => Location.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Location {
  final int? id;
  final String? name;
  final int? stateId;
  final String? stateCode;
  final int? countryId;
  final String? countryCode;
  final int? latitude;
  final int? longitude;
  final int? createdAt;
  final int? updatedAt;
  final int? flag;

  Location({
    this.id,
    this.name,
    this.stateId,
    this.stateCode,
    this.countryId,
    this.countryCode,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
    this.flag,
  });

  factory Location.fromJson(Map<String, dynamic> json) => Location(
        id: json["id"],
        name: json["name"],
        stateId: json["stateId"],
        stateCode: json["stateCode"],
        countryId: json["countryId"],
        countryCode: json["countryCode"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        createdAt: json["createdAt"],
        updatedAt: json["updatedAt"],
        flag: json["flag"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "stateId": stateId,
        "stateCode": stateCode,
        "countryId": countryId,
        "countryCode": countryCode,
        "latitude": latitude,
        "longitude": longitude,
        "createdAt": createdAt,
        "updatedAt": updatedAt,
        "flag": flag,
      };
}
