import 'dart:convert';

ProfileBookCaseModel profileBookCaseModelFromJson(String str) =>
    ProfileBookCaseModel.fromJson(json.decode(str));

String profileBookCaseModelToJson(ProfileBookCaseModel data) =>
    json.encode(data.toJson());

class ProfileBookCaseModel {
  final String? message;
  final List<ProfileBookCase>? data;
  final int? count;
  final int? statusCode;

  ProfileBookCaseModel({
    this.message,
    this.data,
    this.count,
    this.statusCode,
  });

  factory ProfileBookCaseModel.fromJson(Map<String, dynamic> json) =>
      ProfileBookCaseModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<ProfileBookCase>.from(
                json["data"]!.map((x) => ProfileBookCase.fromJson(x))),
        count: json["count"],
        statusCode: json["statusCode"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "count": count,
        "statusCode": statusCode,
      };
}

class ProfileBookCase {
  final int? bookcaseId;
  final int? userId;
  final int? bookId;
  final String? bookName;
  final String? bookAuthor;
  final bool? isCurrentlyReading;
  final int? readingCompleteDate;
  final double? ratings;
  final bool? topShelf;
  final String? review;

  ProfileBookCase({
    this.bookcaseId,
    this.userId,
    this.bookId,
    this.bookName,
    this.bookAuthor,
    this.isCurrentlyReading,
    this.readingCompleteDate,
    this.ratings,
    this.topShelf,
    this.review,
  });

  factory ProfileBookCase.fromJson(Map<String, dynamic> json) =>
      ProfileBookCase(
        bookcaseId: json["bookcase_id"] as int?,
        userId: json["user_id"] as int?,
        bookId: json["book_id"] as int?,
        bookName: json["bookName"] as String?,
        bookAuthor: json["bookAuthor"] as String?,
        isCurrentlyReading: json["is_currently_reading"] as bool?,
        readingCompleteDate: json["reading_complete_date"],
        ratings: (json["ratings"] is int)
            ? (json["ratings"] as int).toDouble()
            : json["ratings"] as double?,
        topShelf: json["topShelf"] as bool?,
        review: json["review"] as String?,
      );

  Map<String, dynamic> toJson() => {
        "bookcase_id": bookcaseId,
        "user_id": userId,
        "book_id": bookId,
        "bookName": bookName,
        "bookAuthor": bookAuthor,
        "is_currently_reading": isCurrentlyReading,
        "reading_complete_date": readingCompleteDate,
        "ratings": ratings,
        "topShelf": topShelf,
        "review": review,
      };
}


// // To parse this JSON data, do
// //
// //     final profileBookCaseModel = profileBookCaseModelFromJson(jsonString);

// import 'dart:convert';

// ProfileBookCaseModel profileBookCaseModelFromJson(String str) =>
//     ProfileBookCaseModel.fromJson(json.decode(str));

// String profileBookCaseModelToJson(ProfileBookCaseModel data) =>
//     json.encode(data.toJson());

// class ProfileBookCaseModel {
//   final String? message;
//   final List<ProfileBookCase>? data;
//   final int? count;
//   final int? statusCode;

//   ProfileBookCaseModel({
//     this.message,
//     this.data,
//     this.count,
//     this.statusCode,
//   });

//   factory ProfileBookCaseModel.fromJson(Map<String, dynamic> json) =>
//       ProfileBookCaseModel(
//         message: json["message"],
//         data: json["data"] == null
//             ? []
//             : List<ProfileBookCase>.from(
//                 json["data"]!.map((x) => ProfileBookCase.fromJson(x))),
//         count: json["count"],
//         statusCode: json["statusCode"],
//       );

//   Map<String, dynamic> toJson() => {
//         "message": message,
//         "data": data == null
//             ? []
//             : List<dynamic>.from(data!.map((x) => x.toJson())),
//         "count": count,
//         "statusCode": statusCode,
//       };
// }

// class ProfileBookCase {
//   final int? bookcaseId;
//   final int? userId;
//   final int? bookId;
//   final String? bookName;
//   final bool? isCurrentlyReading;
//   final DateTime? readingCompleteDate;
//   final int? ratings;
//   final bool? topShelf;
//   final dynamic review;

//   ProfileBookCase({
//     this.bookcaseId,
//     this.userId,
//     this.bookId,
//     this.bookName,
//     this.isCurrentlyReading,
//     this.readingCompleteDate,
//     this.ratings,
//     this.topShelf,
//     this.review,
//   });

//   factory ProfileBookCase.fromJson(Map<String, dynamic> json) =>
//       ProfileBookCase(
//         bookcaseId: json["bookcase_id"],
//         userId: json["user_id"],
//         bookId: json["book_id"],
//         bookName: json["bookName"],
//         isCurrentlyReading: json["is_currently_reading"],
//         readingCompleteDate: json["reading_complete_date"],
//         ratings: json["ratings"],
//         topShelf: json["topShelf"],
//         review: json["review"],
//       );

//   Map<String, dynamic> toJson() => {
//         "bookcase_id": bookcaseId,
//         "user_id": userId,
//         "book_id": bookId,
//         "bookName": bookName,
//         "is_currently_reading": isCurrentlyReading,
//         "reading_complete_date": readingCompleteDate,
//         "ratings": ratings,
//         "topShelf": topShelf,
//         "review": review,
//       };
// }



// // class ProfileBookCaseModel {
// //   final String? bookName;
// //   final String? author;

// //   ProfileBookCaseModel({
// //     this.author,
// //     this.bookName,
// //   });
// // }
