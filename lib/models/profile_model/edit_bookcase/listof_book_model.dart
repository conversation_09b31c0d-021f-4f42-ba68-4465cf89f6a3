// To parse this JSON data, do
//
//     final allBookModel = allBookModelFromJson(jsonString);

import 'dart:convert';

AllBookModel allBookModelFromJson(String str) =>
    AllBookModel.fromJson(json.decode(str));

String allBookModelToJson(AllBookModel data) => json.encode(data.toJson());

class AllBookModel {
  final String? message;
  final int? statusCode;
  final List<Books>? data;

  AllBookModel({
    this.message,
    this.statusCode,
    this.data,
  });

  factory AllBookModel.fromJson(Map<String, dynamic> json) => AllBookModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] == null
            ? []
            : List<Books>.from(json["data"]!.map((x) => Books.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Books {
  final int? bookId;
  final String? bookName;
  final String? bookAuthor;
  final String? amazonAffeliateLink;
  final int? bookCaseId;
  final dynamic publication;
  final String? unaccentedBookName;
  final String? unaccentedBookAuthorName;

  Books({
    this.bookId,
    this.bookName,
    this.bookAuthor,
    this.amazonAffeliateLink,
    this.publication,
    this.bookCaseId,
    this.unaccentedBookAuthorName,
    this.unaccentedBookName,
  });

  factory Books.fromJson(Map<String, dynamic> json) => Books(
        bookId: json["bookId"],
        bookName: cleanString(json["bookName"]),
        bookAuthor: cleanString(json["bookAuthor"]),
        amazonAffeliateLink: json["amazonAffeliateLink"],
        publication: json["publication"],
        bookCaseId: json["bookcase_id"],
        unaccentedBookAuthorName: json['unaccentedBookAuthorName'],
        unaccentedBookName: json['unaccentedBookName'],
      );

  Map<String, dynamic> toJson() => {
        "bookId": bookId,
        "bookName": bookName,
        "bookAuthor": bookAuthor,
        "amazonAffeliateLink": amazonAffeliateLink,
        "publication": publication,
        "bookcase_id": bookCaseId,
        "unaccentedBookName": unaccentedBookName,
        "unaccentedBookAuthorName": unaccentedBookAuthorName,
      };

  // Utility to clean and decode strings
  static String? cleanString(dynamic value) {
    if (value is String) {
      // Remove escaped quotes or other unwanted characters
      return value.replaceAll(r'\"', '"');
    }
    return value;
  }
}
