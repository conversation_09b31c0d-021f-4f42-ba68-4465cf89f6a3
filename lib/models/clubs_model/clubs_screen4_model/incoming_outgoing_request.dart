// To parse this JSON data, do
//
//     final incomingOutGoingRequest = incomingOutGoingRequestFromJson(jsonString);

import 'dart:convert';

IncomingOutGoingRequest incomingOutGoingRequestFromJson(String str) =>
    IncomingOutGoingRequest.fromJson(json.decode(str));

String incomingOutGoingRequestToJson(IncomingOutGoingRequest data) =>
    json.encode(data.toJson());

class IncomingOutGoingRequest {
  final String? message;
  final int? statusCode;
  final List<RequestManage>? data;

  IncomingOutGoingRequest({
    this.message,
    this.statusCode,
    this.data,
  });

  factory IncomingOutGoingRequest.fromJson(Map<String, dynamic> json) =>
      IncomingOutGoingRequest(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"] == null
            ? []
            : List<RequestManage>.from(
                json["data"]!.map((x) => RequestManage.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class RequestManage {
  final int? bookClubMemberId;
  final int? bookClubId;
  final String? bookClubName;
  final String? bookAuthor;
  final String? clubCount;
  String? responseMessage;
  final String? requestMessage;
  final String? invitationMessage;
  final int? userId;
  final String? userName;
  final String? userProfilePicture;
  String? status;
  final String? clubType;
  final String? userType;
  final int? initiatedBy;
  final int? totalVacancies;
  final int? totalPositions;
  final String? memberReqPrompt;

  RequestManage({
    this.bookClubMemberId,
    this.bookClubId,
    this.bookClubName,
    this.bookAuthor,
    this.clubCount,
    this.responseMessage,
    this.requestMessage,
    this.invitationMessage,
    this.userId,
    this.userName,
    this.userProfilePicture,
    this.status,
    this.clubType,
    this.userType,
    this.initiatedBy,
    this.totalPositions,
    this.totalVacancies,
    this.memberReqPrompt,
  });

  factory RequestManage.fromJson(Map<String, dynamic> json) => RequestManage(
        bookClubMemberId: json["bookClubMemberId"],
        bookClubId: json["bookClubId"],
        bookClubName: json["bookClubName"],
        bookAuthor: json["bookAuthor"],
        clubCount: json["clubCount"],
        responseMessage: json["responseMessage"],
        requestMessage: json["requestMessage"],
        invitationMessage: json["invitationMessage"],
        userId: json["userId"],
        userName: json["userName"],
        userProfilePicture: json["userProfilePicture"],
        status: json["status"],
        clubType: json["clubType"],
        userType: json["userType"],
        initiatedBy: json['initiatedBy'],
        totalPositions: json['totalPositions'],
        totalVacancies: json['totalVacancies'],
        memberReqPrompt: json['memberReqPrompt'],
      );

  Map<String, dynamic> toJson() => {
        "bookClubMemberId": bookClubMemberId,
        "bookClubId": bookClubId,
        "bookClubName": bookClubName,
        "bookAuthor": bookAuthor,
        "clubCount": clubCount,
        "responseMessage": responseMessage,
        "requestMessage": requestMessage,
        "invitationMessage": invitationMessage,
        "userId": userId,
        "userName": userName,
        "userProfilePicture": userProfilePicture,
        "status": status,
        "clubType": clubType,
        "userType": userType,
        "initiatedBy": initiatedBy,
        "totalVacancies": totalVacancies,
        "totalPositions": totalPositions,
        "memberReqPrompt": memberReqPrompt,
      };
}
