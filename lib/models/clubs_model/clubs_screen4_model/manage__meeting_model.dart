// To parse this JSON data, do
//
//     final manageMeetingModel = manageMeetingModelFromJson(jsonString);

import 'dart:convert';

ManageMeetingModel manageMeetingModelFromJson(String str) =>
    ManageMeetingModel.fromJson(json.decode(str));

String manageMeetingModelToJson(ManageMeetingModel data) =>
    json.encode(data.toJson());

class ManageMeetingModel {
  final int? bookClubId;
  final int? bookId;
  final String? partOfBookCovered;
  final int? meetingDate;
  final int? meetingStartTime;
  final String? discussionQuestions;
  final String? meetingStatus;
  final List<String>? meetingAlerts;
  final int? meetingEndTime;
  final int? meetingId;
  final int? userId;
  final String? meetingDuration;
  final String? channelName;

  ManageMeetingModel({
    this.bookClubId,
    this.bookId,
    this.partOfBookCovered,
    this.meetingDate,
    this.meetingStartTime,
    this.discussionQuestions,
    this.meetingStatus,
    this.meetingAlerts,
    this.meetingEndTime,
    this.meetingId,
    this.userId,
    this.meetingDuration,
    this.channelName,
  });

  factory ManageMeetingModel.fromJson(Map<String, dynamic> json) =>
      ManageMeetingModel(
        bookClubId: json["bookClubId"],
        bookId: json["bookId"],
        partOfBookCovered: json["partOfBookCovered"],
        meetingDate: json["meetingDate"],
        meetingStartTime: json["meetingStartTime"],
        discussionQuestions: json["discussionQuestions"],
        meetingStatus: json["meetingStatus"],
        meetingAlerts: json["meetingAlerts"],
        meetingEndTime: json["meetingEndTime"],
        meetingId: json['meetingId'],
        userId: json['userId'],
        meetingDuration: json['meetingDuration'],
        channelName: json['channelName'],
      );

  Map<String, dynamic> toJson() => {
        "bookClubId": bookClubId,
        "bookId": bookId,
        "partOfBookCovered": partOfBookCovered,
        "meetingDate": meetingDate,
        "meetingStartTime": meetingStartTime,
        "discussionQuestions": discussionQuestions,
        "meetingStatus": meetingStatus,
        "meetingAlerts": meetingAlerts,
        "meetingEndTime": meetingEndTime,
        'meetingId': meetingId,
        "userId": userId,
        "meetingDuration": meetingDuration,
        "channelName": channelName,
      };
}
