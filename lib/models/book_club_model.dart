class BookClubModel {
  final int? bookClubId;
  final String? bookClubType;
  final int? userId;
  final int? totalPositions;
  final int? totalMembers;
  final int? totalVacancies;
  final DateTime? clubCreatedDate;
  String? clubCharter;
  String? memberReqPrompt;
  int? bookId;
  final String? bookClubName;
  final String? clubStatus;
  final String? bookAuthor;
  final String? clubCount;
  final bool incomingRequest;
  final List<int>? memberUserIds;
  final String? meetingBookName;
  final String? meetingBookAuthor;
  final int? meetingStartTime;
  final String? discussionQuestions;
  final int? meetingId;
  final String? meetingDuration;
  final String? meetingStatus;
  final String? partsOfTheBookCovered;
  final int? meetingEndTime;
  final String? channelName;

  BookClubModel({
    this.bookClubId,
    this.bookClubType,
    this.userId,
    this.totalPositions,
    this.totalMembers,
    this.totalVacancies,
    this.clubCreatedDate,
    this.club<PERSON>harter,
    this.memberReqPrompt,
    this.bookId,
    this.bookClubName,
    this.clubStatus,
    this.bookAuthor,
    this.clubCount,
    this.incomingRequest = false,
    this.memberUserIds,
    this.meetingId,
    this.meetingBookName,
    this.meetingBookAuthor,
    this.meetingStartTime,
    this.discussionQuestions,
    this.meetingDuration,
    this.meetingStatus,
    this.partsOfTheBookCovered,
    this.meetingEndTime,
    this.channelName,
  });

  factory BookClubModel.fromJson(Map<String, dynamic> json) {
    return BookClubModel(
      bookClubId: json['bookClubId'],
      bookClubType: json['bookClubType'],
      userId: json['userId'],
      totalPositions: json['totalPositions'],
      totalMembers: json['totalMembers'],
      totalVacancies: json['totalVacancies'],
      clubCreatedDate: json['clubCreatedDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['clubCreatedDate'])
          : null,
      clubCharter: json['clubCharter'],
      memberReqPrompt: json['memberReqPrompt'],
      bookId: json['bookId'],
      bookClubName: json['bookClubName'],
      clubStatus: json['clubStatus'],
      bookAuthor: json['bookAuthor'],
      clubCount: json['clubCount'],
      incomingRequest: json['incomingRequest'],
      memberUserIds: json["memberUserIds"] == null
          ? []
          : List<int>.from(json["memberUserIds"]!.map((x) => x)),
      meetingId: json["meetingId"],
      meetingBookName: json["meetingBookName"],
      meetingBookAuthor: json["meetingBookAuthor"],
      meetingStartTime: json["meetingStartTime"],
      discussionQuestions: json["discussionQuestions"],
      meetingDuration: json["meetingDuration"],
      meetingStatus: json["meetingStatus"],
      partsOfTheBookCovered: json["partsOfTheBookCovered"],
      meetingEndTime: json["meetingEndTime"],
      channelName: json['channelName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bookClubId': bookClubId,
      'bookClubType': bookClubType,
      'userId': userId,
      'totalPositions': totalPositions,
      'totalMembers': totalMembers,
      'totalVacancies': totalVacancies,
      'clubCreatedDate': clubCreatedDate,
      'clubCharter': clubCharter,
      'memberReqPrompt': memberReqPrompt,
      'bookId': bookId,
      'bookClubName': bookClubName,
      'clubStatus': clubStatus,
      'bookAuthor': bookAuthor,
      'clubCount': clubCount,
      'incomingRequest': incomingRequest,
      "memberUserIds": memberUserIds == null
          ? []
          : List<dynamic>.from(memberUserIds!.map((x) => x)),
      "meetingId": meetingId,
      "meetingBookName": meetingBookName,
      "meetingBookAuthor": meetingBookAuthor,
      "meetingStartTime": meetingStartTime,
      "discussionQuestions": discussionQuestions,
      "meetingDuration": meetingDuration,
      "meetingStatus": meetingStatus,
      "partsOfTheBookCovered": partsOfTheBookCovered,
      "meetingEndTime": meetingEndTime,
      "channelName": channelName,
    };
  }
}
