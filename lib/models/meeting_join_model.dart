// To parse this JSON data, do
//
//     final meetingJoinModel = meetingJoinModelFromJson(jsonString);

import 'dart:convert';

MeetingJoinModel meetingJoinModelFromJson(String str) =>
    MeetingJoinModel.fromJson(json.decode(str));

String meetingJoinModelToJson(MeetingJoinModel data) =>
    json.encode(data.toJson());

class MeetingJoinModel {
  final String? message;
  final int? statusCode;
  final String? data;

  MeetingJoinModel({
    this.message,
    this.statusCode,
    this.data,
  });

  factory MeetingJoinModel.fromJson(Map<String, dynamic> json) =>
      MeetingJoinModel(
        message: json["message"],
        statusCode: json["statusCode"],
        data: json["data"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "statusCode": statusCode,
        "data": data,
      };
}
