import 'package:eljunto/constants/app_config.dart';
import 'package:eljunto/constants/constants.dart';

class Config {
  //localhost
  //static const String baseUrl = 'http://*************:8092/eljuntobookclub/api'; ************
  // 'http://api.eljunto.com:8092/eljuntobookclub/api'; http://ej-dev-lb-956641434.us-east-1.elb.amazonaws.com:80/eljuntobookclub/api

  // DEVELOPMENT SERVER
  static const String baseUrlDev =
      'https://devapi.eljunto.com/eljuntobookclub/api';

  //PRODUCTION SERVER
  static const String baseUrlProd =
      'https://api.eljunto.com/eljuntobookclub/api';

  static const String imageBaseUrl = 'https://el-junto.s3.amazonaws.com/';

  static String flavorBaseUrl =
      AppConfig.shared.flavor == Flavor.dev ? baseUrlDev : baseUrlProd;

  static Map<String, String> getApiHeaders(
      {String portalType = PortalType.admin}) {
    return {
      'Content-Type': 'application/json',
      'Portal-Type': portalType,
    };
  }
}
