class AppVersion implements Comparable<AppVersion> {
  final int major;
  final int minor;
  final int patch;
  final String? suffix;

  AppVersion(this.major, this.minor, this.patch, [this.suffix]);

  factory AppVersion.parse(String versionString) {
    String cleanVersion = versionString;
    String? suffix;

    final lastDotIndex = versionString.lastIndexOf('.');
    if (lastDotIndex != -1 && lastDotIndex < versionString.length - 1) {
      final potentialSuffix = versionString.substring(lastDotIndex + 1);
      // If it's not a number, treat it as a suffix
      if (int.tryParse(potentialSuffix) == null) {
        cleanVersion = versionString.substring(0, lastDotIndex);
        suffix = potentialSuffix;
      }
    }

    final parts = cleanVersion.split('.');
    if (parts.length != 3) {
      throw FormatException(
        "Invalid version string: $versionString. Expected format: X.Y.Z[.suffix]",
      );
    }
    try {
      return AppVersion(
        int.parse(parts[0]),
        int.parse(parts[1]),
        int.parse(parts[2]),
        suffix,
      );
    } catch (e) {
      throw FormatException(
        "Invalid version string: $versionString. Parts must be integers.",
      );
    }
  }

  @override
  int compareTo(AppVersion other) {
    if (major != other.major) return major.compareTo(other.major);
    if (minor != other.minor) return minor.compareTo(other.minor);
    return patch.compareTo(other.patch);
  }

  bool operator >(AppVersion other) => compareTo(other) > 0;
  bool operator <(AppVersion other) => compareTo(other) < 0;
  bool operator >=(AppVersion other) => compareTo(other) >= 0;
  bool operator <=(AppVersion other) => compareTo(other) <= 0;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppVersion &&
          runtimeType == other.runtimeType &&
          major == other.major &&
          minor == other.minor &&
          patch == other.patch;

  @override
  int get hashCode => major.hashCode ^ minor.hashCode ^ patch.hashCode;

  @override
  String toString() =>
      suffix != null ? '$major.$minor.$patch.$suffix' : '$major.$minor.$patch';

  String get cleanVersion => '$major.$minor.$patch';
}
