import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/club_charter_model.dart';
import 'package:eljunto/models/profile_model/profile_home_model/current_reading_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/connection_lost_screen.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/customDialouge_with_message.dart';
import 'package:eljunto/services/notification_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:intl/intl.dart';
import 'package:month_picker_dialog/month_picker_dialog.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../controller/book_club_controller.dart';
import '../controller/connectivity_controller.dart';
import '../controller/login_controller.dart';
import '../controller/message_controller.dart';
import '../services/setup_locator.dart';
import '../views/local_database.dart';

class CommonHelper {
  static Future<String?> getLoggedinToken() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    String? loggedinToken = pref.getString('token');
    // String? loggedinMail = pref.getString('userEmailId');
    print("Token : $loggedinToken");
    return loggedinToken;
  }

  static Future<int?> getLoggedInUserId() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    int? loggedinUserId = pref.getInt('userId');
    // String? loggedinMail = pref.getString('userEmailId');

    return loggedinUserId;
  }

  static Future<String?> getLoggedinUserMail() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    // int? loggedinUserId = pref.getInt('userId');
    String? loggedinMail = pref.getString('userEmailId');

    return loggedinMail;
  }

  static Future<String?> getLoggedinUserName() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    // int? loggedinUserId = pref.getInt('userId');
    String? loggedinName = pref.getString('userName');

    return loggedinName;
  }

  static Future<String?> getLoggedinUserHandler() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    // int? loggedinUserId = pref.getInt('userId');
    String? loggedinUserHandler = pref.getString('userHandle');

    return loggedinUserHandler;
  }

  Future<void> userLogoutFunction(
    BuildContext context,
    Map<String, dynamic> payload,
  ) async {
    final email = await CommonHelper.getLoggedinUserMail();
    log("Email : $email");
    SharedPreferences pref = await SharedPreferences.getInstance();
    bool logoutStatus = false;
    if (context.mounted) {
      logoutStatus = await logOutFunction(context, email ?? '', payload);
    }
    log("Logout Status : $logoutStatus");
    if (logoutStatus) {
      if (context.mounted) {
        Provider.of<MessageController>(context, listen: false).logoutUser();
      }
      NotificationServices().cancelAllOperations();
      // CLEAR DATABASE WHEN USER LOGOUT THE APPLICATION
      await DatabaseHelper.instance.clearEntireDatabase();
      await pref.clear();

      print("UserID : ${pref.getInt('userId')}");
      log('Token expire');
      if (context.mounted) {
        context.goNamed('login');
      }
    } else {
      await pref.clear();

      if (context.mounted) {
        context.goNamed('login');
      }
    }
  }

  bool logOutValue = false;
  Future<bool> logOutFunction(BuildContext context, String userMailId,
      Map<String, dynamic> payload) async {
    await Provider.of<BookClubController>(context, listen: false)
        .agoraLogs(payload)
        .then((value) async {
      log("Logout from Agora : $value");
      logOutValue = await Provider.of<LoginController>(context, listen: false)
          .logOutFunction(userMailId);
    });
    log("Logout Status Value: $logOutValue");

    return logOutValue;
  }

  static Future<String?> getLoggedinUserLocation() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    // int? loggedinUserId = pref.getInt('userId');
    String? loggedinLocation = pref.getString('userLocation');

    return loggedinLocation;
  }

  static Future<String?> getLoggedinUserBio() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    // int? loggedinUserId = pref.getInt('userId');
    String? loggedinBio = pref.getString('userBio');

    return loggedinBio;
  }

  static List<List<BookCaseModel>> getCurrentlyReadingAndTopShelfBooks(
      List<BookCaseModel> books) {
    List<BookCaseModel> currentlyReadingBooks = [];
    List<BookCaseModel> topShelfBooks = [];
    List<BookCaseModel> completedBooks = [];

    /* for (var book in books) {
      if (book.is_currently_reading!) {
        currentlyReadingBooks.add(book);
      } else {
        completedBooks.add(book);
        if (book.topShelf!) {
          topShelfBooks.add(book);
        }
      }
    } */

    for (var book in books) {
      if (book.is_currently_reading!) {
        // Add the book to currentlyReadingBooks if it is currently being read
        currentlyReadingBooks.add(book);

        // If the book is being reread (reRead >= 1), also add it to completedBooks
        if (book.reRead != null && book.reRead! >= 1) {
          completedBooks.add(book);
          if (book.topShelf!) {
            topShelfBooks.add(book);
          }
        }
      } else {
        // If the book is not currently being read, add it to completedBooks
        completedBooks.add(book);
        if (book.topShelf!) {
          topShelfBooks.add(book);
        }
      }
    }
    print("IN COMPLETED BOOKS : ${completedBooks.length}");
    return [currentlyReadingBooks, topShelfBooks, completedBooks];
  }

  static List<List<BookClubModel>> getStandingAndImprmptuClubs(
      List<BookClubModel> bookClubs) {
    List<BookClubModel> standingBookClubs = [];
    List<BookClubModel> impromptuBookClubs = [];

    for (var bookClub in bookClubs) {
      if (bookClub.bookClubType?.toLowerCase() ==
          ClubType.standing.toLowerCase()) {
        standingBookClubs.add(bookClub);
      } else if (bookClub.bookClubType?.toLowerCase() ==
          ClubType.impromptu.toLowerCase()) {
        impromptuBookClubs.add(bookClub);
      }
    }

    return [standingBookClubs, impromptuBookClubs];
  }

  static List<BookClubModel> getClubsLedByUser(
      List<BookClubModel> bookClubs, int userId) {
    List<BookClubModel> leaderBookClubs = [];

    for (var bookClub in bookClubs) {
      if (bookClub.userId == userId) {
        leaderBookClubs.add(bookClub);
      }
    }

    return leaderBookClubs;
  }

  static String getMonthYearDateFormat(int? intDatetoFormat) {
    DateTime? datetoFormat = intDatetoFormat != null
        ? DateTime.fromMillisecondsSinceEpoch(intDatetoFormat)
        : null;

    if (datetoFormat == null) {
      return 'Unknown';
    }
    return DateFormat('MMM yyyy').format(datetoFormat);
  }

  static String getDayMonthYearDateFormat(int? intDatetoFormat) {
    DateTime? datetoFormat = intDatetoFormat != null
        ? DateTime.fromMillisecondsSinceEpoch(intDatetoFormat)
        : null;

    if (datetoFormat == null) {
      return 'TBD';
    }
    return DateFormat('EEE MMM dd, yyyy').format(datetoFormat.toLocal());
  }

  static String getMeetingScheduleTime(
      int? meetingstartTime, int? meetingEndTime) {
    if (meetingstartTime == 0 || meetingEndTime == 0) {
      return 'TBD';
    }
    final yy =
        DateTime.fromMillisecondsSinceEpoch(meetingstartTime ?? 0).toLocal();
    final yyy =
        DateTime.fromMillisecondsSinceEpoch(meetingEndTime ?? 0).toLocal();
    // DateTime startTime =yy.toLocal();
    // DateTime endTime =
    //     DateFormat('HH:mm').parse(meetingEndndTime ?? '').toLocal();
    String formattedTime = DateFormat('h:mm a').format(yy);
    String formatedEndTime = DateFormat('h:mm a').format(yyy);
    final meetingTime = '$formattedTime-$formatedEndTime';
    return meetingTime;
  }

  static List<BookCaseModel> sortBookCaseList(
      List<BookCaseModel> bookcaseList, String sortOption) {
    switch (sortOption) {
      case BookCaseSortingOptions.title:
        bookcaseList
            .sort((a, b) => (a.bookName ?? '').compareTo(b.bookName ?? ''));
        break;
      case BookCaseSortingOptions.completionDate:
        bookcaseList.sort((a, b) =>
            (b.readingCompleteDate ?? 0).compareTo(a.readingCompleteDate ?? 0));
        break;
      case BookCaseSortingOptions.author:
        bookcaseList
            .sort((a, b) => (a.bookAuthor ?? '').compareTo(b.bookAuthor ?? ''));
        break;
      case BookCaseSortingOptions.ratings:
        bookcaseList
            .sort((a, b) => (b.ratings ?? 0.0).compareTo(a.ratings ?? 0.0));
        break;
      default:
        throw ArgumentError('Invalid sort option');
    }
    return bookcaseList;
  }

  static List<ProfileBookCase> sortProfileBookCaseList(
      List<ProfileBookCase> bookcaseList, String sortOption) {
    switch (sortOption) {
      case BookCaseSortingOptions.title:
        bookcaseList
            .sort((a, b) => (a.bookName ?? '').compareTo(b.bookName ?? ''));
        break;
      case BookCaseSortingOptions.completionDate:
        bookcaseList.sort((a, b) =>
            (a.readingCompleteDate ?? 0).compareTo(b.readingCompleteDate ?? 0));
        break;
      case BookCaseSortingOptions.author:
        bookcaseList
            .sort((a, b) => (a.bookAuthor ?? '').compareTo(b.bookAuthor ?? ''));

        break;
      case BookCaseSortingOptions.ratings:
        bookcaseList
            .sort((a, b) => (b.ratings ?? 0.0).compareTo(a.ratings ?? 0.0));
        break;
      default:
        throw ArgumentError('Invalid sort option');
    }
    return bookcaseList;
  }

  static Future<void> topShelfandAlreadyBookAddFunction(
      BuildContext context, String title, String description) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          description,
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () async {
                    // final bookId = currentbookCaseList?[index].bookId;
                    // await deleteBook(bookId).then(
                    //   (value) {},
                    // );
                    // setState(() {});
                    if (context.mounted) {
                      context.pop();
                    }
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  static List<String> getMonths() {
    List<String> monthNames = List.generate(12, (index) {
      return DateFormat.MMM().format(DateTime(0, index + 1));
    });

    return monthNames;
  }

  static List<String> getYears() {
    List<String> years = List.generate(2025 - 2000, (index) {
      return (2000 + index).toString();
    });

    return years;
  }

  static Future<File> renameFile(File originalFile, File croppedFile) async {
    final newPath = originalFile.path.replaceFirst(
      RegExp(r'[^/]+$'), // This will match the file name part
      originalFile.uri.pathSegments.last
          .replaceAll('.jpg', '.png'), // Replacing original name with new one
    );
    return croppedFile.rename(newPath);
  }

  /// SELECT MONTH AND YEAR

  static Future<DateTime?> getMonthYear(BuildContext context) async {
    DateTime? selectedDate;
    await showMonthPicker(
      monthPickerDialogSettings: MonthPickerDialogSettings(
        headerSettings: PickerHeaderSettings(
          headerCurrentPageTextStyle: lbRegular.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
          headerSelectedIntervalTextStyle: lbRegular.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
          headerBackgroundColor: AppConstants.textGreenColor,
        ),
        dialogSettings: PickerDialogSettings(
          insetPadding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.05),
          dialogBackgroundColor: AppConstants.backgroundColor,
        ),
        actionBarSettings: PickerActionBarSettings(
          cancelWidget: Text(
            "Cancel",
            style: lbRegular.copyWith(
              fontSize: 12,
              color: AppConstants.primaryColor,
            ),
          ),
          confirmWidget: Text(
            "Ok",
            style: lbBold.copyWith(
              fontSize: 12,
              color: AppConstants.primaryColor,
            ),
          ),
        ),
        dateButtonsSettings: PickerDateButtonsSettings(
          selectedDateRadius: 7,
          selectedMonthBackgroundColor: AppConstants.textGreenColor,
          monthTextStyle: lbRegular.copyWith(
            fontSize: 14,
            color: AppConstants.primaryColor,
          ),
          yearTextStyle: lbRegular.copyWith(
            fontSize: 12,
            color: AppConstants.primaryColor,
          ),
          unselectedYearsTextColor: AppConstants.primaryColor,
          unselectedMonthsTextColor: AppConstants.primaryColor,
          currentMonthTextColor: AppConstants.primaryColor,
          selectedMonthTextColor: Colors.white,
        ),
      ),
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime(
        DateTime.now().year,
        DateTime.now().month,
      ),
    ).then((value) {
      selectedDate = value;
    });
    return selectedDate;
  }

  static String? getMimeType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      default:
        return null;
    }
  }

  static Future privacyPolicy() async {
    if (!await launchUrl(AppConstants.privacyPolicyUrl)) {
      throw Exception('Could not launch : ${AppConstants.privacyPolicyUrl}');
    }
  }

  static Future termsAndCondition() async {
    if (!await launchUrl(AppConstants.termsAndConditionUrl)) {
      throw Exception(
          'Could not launch : ${AppConstants.termsAndConditionUrl}');
    }
  }

  static TimeOfDay parseTimeOfDay(String timeString) {
    final format = DateFormat('h:mm a'); // For 12-hour format with AM/PM
    final DateTime parsedTime = format.parse(timeString);
    // print("End Time : $parsedTime");
    return TimeOfDay.fromDateTime(parsedTime);
  }

  static TimeOfDay parseTimeOfDay_1(String time) {
    try {
      final format = DateFormat.jm(); // 12-hour format with AM/PM
      final dateTime = format.parseLoose(time);
      return TimeOfDay.fromDateTime(dateTime);
    } catch (e) {
      print("Error parsing time: $e");
      return const TimeOfDay(hour: 0, minute: 0); // or handle it as needed
    }
  }

  // Increment TimeOfDay by a given number of minutes
  static TimeOfDay incrementTimeByMinutes(TimeOfDay time, int minutes) {
    final totalMinutes = time.hour * 60 + time.minute + minutes;
    return TimeOfDay(hour: totalMinutes ~/ 60, minute: totalMinutes % 60);
  }

  // Check if the selected date is today
  static bool isToday(DateTime? pickDate) {
    final today = DateTime.now();
    return pickDate?.year == today.year &&
        pickDate?.month == today.month &&
        pickDate?.day == today.day;
  }

  // Check if the loop time is a future time compared to the current time
  static bool isFutureTime(TimeOfDay loopTime, TimeOfDay currentTime) {
    return (loopTime.hour > currentTime.hour) ||
        (loopTime.hour == currentTime.hour &&
            loopTime.minute > currentTime.minute);
  }

  // Format TimeOfDay to a readable string
  static String formatTimeOfDay(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  static List<Map<String, String>> generateTimeIntervalsMap() {
    List<Map<String, String>> timeIntervals = [
      {'TBD': 'TBD'}
    ]; // Start with "TBD" as the first item
    const int totalMinutes = 4 * 60; // 4 hours in minutes
    const int interval = 30; // 15 minutes interval

    for (int minutes = interval; minutes <= totalMinutes; minutes += interval) {
      int hours = minutes ~/ 60;
      int remainingMinutes = minutes % 60;

      // Format the key as "HH:mm"
      String key =
          '${hours.toString().padLeft(2, '0')}:${remainingMinutes.toString().padLeft(2, '0')}';

      // Format the display value as a human-readable time interval
      String value = '';
      if (hours > 0) {
        value += '$hours hour';
        if (hours > 1) value += 's';
      }
      if (remainingMinutes > 0) {
        if (hours > 0) value += ' ';
        value += '$remainingMinutes min';
      }

      timeIntervals.add({key: value});
    }

    return timeIntervals;
  }

  static String formatedDateFunction(DateTime date, DateTime? pickDate) {
    String daySuffix(int day) {
      if (day >= 11 && day <= 13) {
        return 'th';
      }
      switch (day % 10) {
        case 1:
          return 'st';
        case 2:
          return 'nd';
        case 3:
          return 'rd';
        default:
          return 'th';
      }
    }

    String formattedDate = DateFormat('EEE MMM d').format(date);
    String dayWithSuffix = daySuffix(pickDate?.day ?? 0);
    String yearAndTime = DateFormat('yyyy').format(
      pickDate ?? DateTime.now(),
    );
    return '$formattedDate$dayWithSuffix, $yearAndTime';
  }

// Generate time options in 15-minute intervals based on the selected date
  static List<String> generateTimeOptions(
      DateTime? pickDate, TimeOfDay? pickedStartTime) {
    List<String> timeOptions = ['TBD'];
    const int interval = 15;
    const startTime = TimeOfDay(hour: 0, minute: 0);
    const endTime = TimeOfDay(hour: 23, minute: 45);
    final currentTime = pickedStartTime ?? TimeOfDay.now();

    var loopTime = startTime;
    while (loopTime.hour < endTime.hour ||
        (loopTime.hour == endTime.hour && loopTime.minute <= endTime.minute)) {
      if (!isToday(pickDate) || isFutureTime(loopTime, currentTime)) {
        timeOptions.add(formatTimeOfDay(loopTime));
      }
      loopTime = incrementTimeByMinutes(loopTime, interval);
    }
    return timeOptions;
  }

  static void showDurationValidationDialog(BuildContext context) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CustomDialog(
          title: "Add meeting:",
          message:
              "Date, time and duration should either be marked as 'TBD' or have specific values assigned",
          showDoneImage: false,
        );
      },
    );
  }

  static bool conditionCheck(
      DateTime? pickDate, TimeOfDay? pickedStartTime, String? durationKey) {
    if (pickDate == null && pickedStartTime == null && durationKey == null) {
      return false;
    } else if (pickDate == null ||
        pickedStartTime == null ||
        durationKey == null) {
      return true;
    }
    return false;
  }

  static Future<void> showInfoDialog({
    required BuildContext context,
    required String title,
    required String subTitle,
    required List<ClubCharterModel> infoList,
    required bool isHide,
    String canEditText = "(You can edit this later)",
  }) async {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Center(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      // overflow: TextOverflow.ellipsis,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 3),
                isHide
                    ? Center(
                        child: Text(
                          canEditText, //"(You can edit this later)",
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
                const SizedBox(height: 23),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    subTitle,
                    textAlign: TextAlign.start,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Column(
                  children: infoList.map((e) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 35, right: 20),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 3.0),
                            child: Text(
                              "•",
                              textAlign: TextAlign.start,
                              style: lbRegular.copyWith(
                                fontSize: 15,
                                height: 0.8,
                              ),
                            ),
                          ),
                          const SizedBox(width: 5),
                          Expanded(
                            child: Text(
                              e.rules ?? '',
                              textAlign: TextAlign.start,
                              style: lbRegular.copyWith(
                                fontSize: 12,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 25),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.5,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
              ],
            ),
          ],
        );
      },
    );
  }

  static Future<void> showClubCharterInfo({
    required BuildContext context,
    required String? clubType,
  }) async {
    List<ClubCharterModel> clubCharterInfo;
    String subTitle;

    if (clubType == ClubType.standing) {
      clubCharterInfo = [
        ClubCharterModel(
          rules:
              'Genre/Themes/Authors of books (For eg: 70s Sci-fi books turned into movies, Reese Witherspoon book club books, Stephen King books, Civil War Historical Fiction)',
        ),
        ClubCharterModel(
          rules: 'Typical meeting frequency, day of the week, and time.',
        ),
        ClubCharterModel(rules: 'Membership cap: Eg 7 members only.'),
        ClubCharterModel(rules: 'If the discussion leader rotates.'),
        ClubCharterModel(
          rules: 'Who is responsible for book selection (Eg: it rotates).',
        ),
      ];
      subTitle = "Standing club charter includes:";
    } else {
      clubCharterInfo = [
        ClubCharterModel(
          rules: 'How many sessions the book will be discussed over?',
        ),
        ClubCharterModel(rules: 'Membership cap: Eg 7 members only.'),
      ];
      subTitle = "Impromptu club charter includes:";
    }

    await showInfoDialog(
      context: context,
      title: "Club Charter",
      subTitle: subTitle,
      infoList: clubCharterInfo,
      isHide: true,
    );
  }

  static Future<void> showMemberRequestPromptInfo({
    required BuildContext context,
    required String? clubType,
  }) async {
    List<ClubCharterModel> memberRequestPromptInfo =
        clubType == ClubType.standing
            ? [
                ClubCharterModel(rules: 'Reading speed.'),
                ClubCharterModel(rules: 'Willing to lead discussion?'),
              ]
            : [
                ClubCharterModel(rules: 'Have they read the book?'),
                ClubCharterModel(
                    rules: 'When do they anticipate finishing the book?'),
              ];

    await showInfoDialog(
      context: context,
      title: "Member Request Prompt",
      subTitle:
          "What do you want to know about people asking to join your club?",
      infoList: memberRequestPromptInfo,
      isHide: true,
    );
  }

  static List<String> getMeetingAlertOptions(DateTime selectedMeetingDate) {
    DateTime now = DateTime.now();

    // Calculate the difference between current date and selected meeting date
    Duration difference = selectedMeetingDate.difference(now);
    List<String> alertOptions = [];

    // If the meeting is more than 1 day away, show all options
    if (difference.inDays >= 7) {
      alertOptions = [
        '15 Min',
        '1 Day',
        '1 Week',
      ];
      //filters = alertOptions;
    }
    // If the meeting is set for tomorrow, show 1 Day and 15 Min options
    else if (difference.inHours >= 24 && difference.inDays < 7) {
      alertOptions = [
        '15 Min',
        '1 Day',
      ];
      //filters = alertOptions;
    }
    // If the meeting is today, show only the 15 Min option
    else if (difference.inHours <= 24) {
      alertOptions = ['15 Min'];
      //filters = alertOptions;
    }

    return alertOptions;
  }

  static Future<String> getDeviceName() async {
    final prefs = await SharedPreferences.getInstance();
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      log('getDeviceName: ${androidInfo.model}-${androidInfo.id} & ${androidInfo.model}');

      await prefs.setString(
          "deviceId", '${androidInfo.model}-${androidInfo.id}');
      await prefs.setString("deviceName", androidInfo.model);
      // deviceId = '${androidInfo.model}-${androidInfo.id}';
      log("DeviceId : ${androidInfo.model}-${androidInfo.id} ");

      return '${androidInfo.model}-${androidInfo.id}';
    } else {
      final iosInfo = await deviceInfo.iosInfo;
      log('getDeviceName: ${iosInfo.identifierForVendor}');
      await prefs.setString("deviceId", iosInfo.identifierForVendor!);
      await prefs.setString("deviceName", iosInfo.model);
      // deviceId = iosInfo.identifierForVendor;
      return iosInfo.identifierForVendor ?? 'iPhone';
    }
  }

  static Future<void> checkInternetConnectivity(
      {required BuildContext context, required VoidCallback onTryAgain}) async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: false,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      barrierColor: AppConstants.backgroundColor.withOpacity(.5),
      sheetAnimationStyle: AnimationStyle(
        duration: Duration(milliseconds: 300),
        curve: Curves.elasticIn,
        reverseDuration: Duration(milliseconds: 300),
        reverseCurve: Curves.elasticOut,
      ),
      builder: (context) => ConnectivityLossSheet(
        onTryAgain: onTryAgain,
      ),
    );
  }

  static void networkClose(Future<void> loadMoreResult, BuildContext context) {
    final provider = locator<ConnectivityProvider>();
    if (provider.status == InternetStatus.connected) {
      loadMoreResult;
    } else {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: true,
        enableDrag: true,
        backgroundColor: Colors.transparent,
        useRootNavigator: true,
        barrierColor: AppConstants.backgroundColor.withOpacity(.5),
        sheetAnimationStyle: AnimationStyle(
          duration: Duration(milliseconds: 300),
          curve: Curves.elasticIn,
          reverseDuration: Duration(milliseconds: 300),
          reverseCurve: Curves.elasticOut,
        ),
        builder: (context) => SingleChildScrollView(
          child: ConnectivityLossSheet(
              // onTryAgain: clubOpeningRefresh,
              ),
        ),
      );
    }
    if (provider.status == InternetStatus.connected) {
      loadMoreResult;
    }
  }

  static Future<void> appUpdateDialog(
    BuildContext context, {
    required bool isMandatory,
    required String storeUrl,
  }) async {
    log('app store url : $storeUrl');
    await showDialog(
      barrierColor: Colors.white54,
      barrierDismissible: isMandatory ? false : true,
      context: context,
      builder: (context) => AlertDialog(
        insetPadding: EdgeInsets.symmetric(horizontal: 20),
        backgroundColor: AppConstants.backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: AppConstants.primaryColor),
        ),
        titlePadding: EdgeInsets.only(top: 10, right: 10),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            GestureDetector(
              onTap: () => isMandatory ? SystemNavigator.pop() : context.pop(),
              child: Image.asset(
                AppConstants.closePopupImagePath,
                alignment: Alignment.centerRight,
                height: 30,
                width: 30,
              ),
            ),
            Text(
              'New update available',
              textAlign: TextAlign.center,
              style: lbRegular.copyWith(fontSize: 18),
            ),
          ],
        ),
        content: Text(
          isMandatory
              ? 'We’ve made some important improvements. Please update the app to continue using it.'
              : 'A new version of the app is available. Update now to enjoy the latest features and improvements.',
          textAlign: TextAlign.center,
          style: lbRegular.copyWith(fontSize: 18),
        ),
        actionsAlignment: MainAxisAlignment.center,
        actions: [
          TextButton(
            onPressed: () async {
              try {
                final launched = await launchUrl(
                  Uri.parse(storeUrl),
                  mode: LaunchMode.externalApplication,
                );
                if (!launched) {
                  log('Failed to launch store URL');
                }
              } catch (e) {
                log('error : $e');
              }
            },
            style: TextButton.styleFrom(
              backgroundColor: AppConstants.textGreenColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
              ),
              padding: EdgeInsets.symmetric(vertical: 12, horizontal: 25),
            ),
            child: Text(
              'Update',
              style: lbBold.copyWith(fontSize: 18),
            ),
          ),
          const SizedBox(width: 10),
          TextButton(
            onPressed: () =>
                isMandatory ? SystemNavigator.pop() : context.pop(),
            style: TextButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
                side: BorderSide(color: AppConstants.primaryColor),
              ),
              padding: EdgeInsets.symmetric(vertical: 12, horizontal: 30),
            ),
            child: Text(
              isMandatory ? 'Exit' : 'Cancel',
              style: lbBold.copyWith(fontSize: 18),
            ),
          ),
        ],
      ),
    );
  }

  static inValidDeepLink(BuildContext context, String message,
      {Function? onDialogClosed}) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                    if (onDialogClosed != null) {
                      onDialogClosed();
                    }
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30.0),
                  child: Center(
                    child: Text(
                      message,
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        context.pop();
                        if (onDialogClosed != null) {
                          onDialogClosed();
                        }
                      },
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 12, horizontal: 45),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
              ],
            ),
          ],
        );
      },
    );
  }
}
