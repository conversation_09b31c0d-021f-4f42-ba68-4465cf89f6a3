import 'package:flutter/material.dart';

class AppConstants {
  static const String appName = "El Junto";
  static const String appVersion = "1.0.0";
  static const String appNameWithVersion = "El Junto v1.1.1";
  static const int otpExpiryTime = 0; // 10 minutes
  static const Color primaryColor = Color(0xFF253943);
  static Color ratingColor =
      const Color(0xFF253943).withValues(alpha: 60).withAlpha(60);
  static const Color backgroundColor = Color(0xFFFFF5D6);
  static const Color popUpBorderColor = Color.fromRGBO(37, 57, 67, 0.8);
  static const Color textGreenColor = Color(0xFF7C9C9A);
  static const Color blueColor = Color(0xFF3100F6);
  static const Color redColor = Color(0xFFF60303);
  static const Color hintTextColor = Color(0xFFA1A1A1);
  static const Color skeletonBackgroundColor = Color(0xFFE0E0E0);
  static const Color skeletonforgroundColor = Color(0xFFC2C2C2);
  static const Color videoConferenceAppBarColor = Color(0xFF2D2D2D);
  static const Color isActiveRequestColor = Color(0xFFBEC4C7);
  static const String assetImagePath = 'assets/images/';
  static const String bgImagePath = 'assets/images/PaperBackground.png';
  static const String closePopupImagePath = 'assets/icons/Cancel_Popups.png';
  static const String profileLogoImagePath = 'assets/icons/Profile_2.png';
  static const String eJuntoBookClubLogoPng =
      'assets/images/ElJuntoBookClubLogoPng.png';
  static const String leaderStar =
      "assets/icons/Leader_Transparent_Background.png";
  static const String ratingsStar = 'assets/icons/Rating_Star.png';
  static const String locationImagePath = "assets/icons/Location.png";
  static const String openToInvitationImagePath =
      "assets/icons/Open_to_Invitation.png";
  static const String notOpenToInvitationImagePath =
      "assets/icons/NotOpenToInvitation.png";
  static const String clubOpeningLogoImagePath =
      "assets/icons/Club_Opening_1.png";
  static const String clubLogoImagePath = "assets/icons/Clubs_2.png";
  static const String questionLogoImagePath = "assets/images/Question.png";
  static final Uri privacyPolicyUrl =
      Uri.parse('https://eljunto.com/privacypolicy.html');
  static final Uri termsAndConditionUrl =
      Uri.parse('https://eljunto.com/termsofservice.html');
  static const String notificationImagePath = "assets/icons/Notification.png";
  static const String currentlyReadingIcon =
      "assets/icons/Currently_Reading.png";
  static const String topShelfBookIcon = "assets/icons/Top_Shelf_book.png";
  static const String toBeReadIcon = "assets/images/To-Be-Read_.png";
  static const String bookReadIcon = "assets/images/all_book_read.png";

  static const String clubOpeningZero = "assets/icons/club_opening_0.png";
  static const String elJuntoLogo = "assets/images/ElJunto_logo.png";
  static const String requestDoneImg = "assets/icons/Done.png";
}

class PortalType {
  static const String admin = 'ADMIN';
  static const String app = 'APP';
}

class ClubType {
  static const String standing = 'STANDING';
  static const String impromptu = 'IMPROMPTU';
}

class ClubMemberType {
  static const String member = 'MEMBER';
  static const String leader = 'LEADER';
}

class BookCaseSortingOptions {
  static const String title = 'Title A-Z';
  static const String completionDate = 'Date Completed';
  static const String author = 'Author';
  static const String ratings = 'Rating';
}

class ClubMembershipStatus {
  static const String pending = 'PENDING';
  static const String active = 'ACTIVE';
  static const String rejected = 'REJECTED';
  static const String revoked = 'REVOKED';
  static const String removed = 'REMOVED';
  static const String left = 'LEFT';
  static const String reOpened = ',REOPENED';
  static const String reScind = 'RESCIND';
  static const String inActive = ',INACTIVE';
  static const String isInActive = 'INACTIVE';
}

class ClubRequestType {
  static const String incomingClubRequestByUserId =
      'incomingClubRequestByUserId';
  static const String outgoingClubRequestByUserId =
      'outgoingClubRequestByUserId';
  static const String incomingRequestByClubId = 'incomingRequestByClubId';
  static const String outGoingRequestByClubId = 'outGoingRequestByClubId';
}

class TopShelfMessage {
  static const String title = 'Add to Topshelf';
  static const String description =
      'You already have 20 books in your topshelf\n\nRemove a book from your topshelf first to add this book.';
}

class BookAllReadyPresent {
  static const String title = 'Add new book';
  static const String description =
      'This book is already present in your currently reading list';
}

class AddBookInBookCase {
  static const String title = 'Add to Bookcase';
  static const String description =
      'This book is presently in your currently reading section would you like to mark it as complete and move it to your bookcase?';
}

class SearchOption {
  static const String whoReadingThis = "Who’s Reading This?";
  static const String whoReadThis = "Who’s Read This?";
  static const String profile = "Profile";
  static const String whatClubRead = "What Clubs Are Reading This?";
  static const String clubs = "Clubs";
  static const String whoIntoThisBook = "Who’s Into This Book?";
  static const String whoIntoThisAuthor = "Who’s Into This Author?";
}

class FilterOption {
  static const String interested = "Interested";
  static const String whoReadingThis = "whoReadingThis";
  static const String whoReadThis = "whoReadThis";
  static const String whatClubIntoThis = "whatClubIntoThis";
}

class MatchesFilter {
  static const String currentlyReadMatch = "currentlyReaders";
  static const String toBeReadMatch = "interestedUsers";
  static const String starMatch = "ratingMatchedUsers";
}

class ErrorSources {
  static const String agora = "AgoraMeeting";
  static const String logOut = "AutoLogout";
}

class VersionResult {
  static const forceUpdate = "FORCE_UPDATE";
  static const optionalUpdate = "OPTIONAL_UPDATE";
  static const upToDate = "UP_TO_DATE";
}

class InvalidDeepLink {
  static const String message =
      'There was an issue with the link please try again.';
}

class PlatformPurchase {
  static const String google = "GOOGLE";
  static const String apple = "APPLE";
}
