name: eljunto
description: "A new Flutter project."
publish_to: "none" # Remove this line if you wish to publish to pub.dev
version: 1.0.3+91

environment:
  sdk: ">=3.3.4 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_svg: ^2.0.9
  google_fonts: ^6.2.1
  email_validator: ^3.0.0
  go_router: ^15.1.2
  sliding_clipped_nav_bar: ^3.1.1
  url_launcher: ^6.3.0
  flutter_rating_bar: ^4.0.1
  pinput: ^5.0.0
  package_info_plus: ^8.0.0
  flutter_advanced_switch: ^3.1.0
  intl: ^0.19.0
  http: ^1.2.2
  provider: ^6.1.2
  crypto: ^3.0.3
  shared_preferences: ^2.2.3
  http_interceptor: ^2.0.0
  flutter_typeahead: ^5.2.0
  image_cropper: ^9.0.0
  image_picker: ^1.1.2
  roundcheckbox: ^2.0.5
  image: ^4.2.0
  easy_loading_button: ^0.4.0
  loading_btn: ^1.0.3
  flutter_timer_countdown: ^1.0.7
  flutter_pannable_rating_bar: ^2.7.2+1
  advanced_search: ^2.2.4
  month_picker_dialog: ^6.0.3
  cached_network_image: ^3.3.1
  animated_rating_stars: ^1.0.1
  custom_rating_bar: ^3.0.0
  agora_rtc_engine: ^6.3.2
  # Permissions handler
  permission_handler: ^12.0.0+1
  flutter_datetime_picker_plus: ^2.2.0
  adoptive_calendar: ^0.2.0
  flutter_time_picker_spinner: ^2.0.0
  multi_dropdown: ^3.0.1
  animated_custom_dropdown: ^3.1.1
  flutter_staggered_grid_view: ^0.7.0
  firebase_core: ^3.2.0
  firebase_messaging: ^15.0.3
  firebase_analytics: ^11.2.0
  flutter_local_notifications: ^19.2.1
  overlay_support: ^2.1.0
  app_settings: ^6.1.1
  firebase_storage: ^12.3.5
  cloud_firestore: ^5.4.5
  dash_chat_2: ^0.0.21
  searchfield: ^1.1.9
  async: ^2.11.0
  webview_flutter: ^4.10.0
  firebase_in_app_messaging: ^0.8.0+10
  shimmer: ^3.0.0
  wakelock_plus: ^1.2.8
  googleapis_auth: ^2.0.0
  emoji_picker_flutter: ^4.3.0
  app_badge_plus: ^1.1.6
  app_links: ^6.3.3
  defer_pointer: ^0.0.2
  in_app_purchase: ^3.2.1
  connectivity_plus: ^6.1.3
  device_info_plus: ^11.3.0
  synchronized: ^3.3.0+3
  internet_connection_checker_plus: ^2.7.1
  dio: ^5.8.0+1
  in_app_purchase_storekit: ^0.4.0
  in_app_purchase_android: ^0.4.0
  get_it: ^8.0.3
  skeletonizer: ^2.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  flutter_launcher_icons: ^0.14.1
  change_app_package_name: ^1.3.0

# flutter_launcher_icons:
#   android: true
#   ios: true
#   image_path: "assets/images/dark_icon.png"
#   adaptive_icon_foreground: 'assets/images/dev_app_icon.png'
#   adaptive_icon_background: "#7C9C9A"

flutter:
  uses-material-design: true

  assets:
    - assets/el-junto-4fb80-firebase-adminsdk-gwtik-f421cef23d.json
    - assets/icons/svg/
    - assets/icons/
    - assets/images/
    - assets/images/ads/
    - assets/images/bottom_navigation_icon/
    - assets/images/ElJuntoBookClubLogoSvg.svg
    - assets/el-junto-development-server-firebase-adminsdk-vmec3-eb3a739632.json
    - assets/el-junto-4fb80-firebase-adminsdk-gwtik-f421cef23d.json
    - assets/config/currency_code.json

  fonts:
    - family: Libre Baskerville Regular
      fonts:
        - asset: assets\fonts\LibreBaskerville-Regular.ttf
    - family: Libre Baskerville Italic
      fonts:
        - asset: assets\fonts\LibreBaskerville-Italic.ttf
    - family: Libre Baskerville Bold
      fonts:
        - asset: assets\fonts\LibreBaskerville-Bold.ttf
