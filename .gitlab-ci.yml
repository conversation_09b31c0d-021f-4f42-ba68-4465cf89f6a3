image: ghcr.io/cirruslabs/flutter:stable

variables:
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
  GIT_DEPTH: "0"

cache:
  key: "$CI_PROJECT_NAME"
  paths:
    - .pub-cache/
    - .dart_tool/
    - sonar-scanner/

stages:
  - get-binaries
  - format
  - lint
  - test
  - metrics
  - build
  - security
  - sonarcloud-check

get-binaries:
  stage: get-binaries
  script:
    - curl -sSLo ./sonar-scanner.zip 'https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-7.0.2.4839-linux-x64.zip'
    - unzip -o sonar-scanner.zip
    - rm -rf sonar-scanner
    - mv sonar-scanner-7.0.2.4839-linux-x64 sonar-scanner
  allow_failure: true

flutter-format-check:
  stage: format
  script:
    - flutter pub get
    - dart format --set-exit-if-changed .
  allow_failure: true

flutter-lint:
  stage: lint
  script:
    - flutter pub get
    - flutter analyze --fatal-infos --fatal-warnings
  allow_failure: true

flutter-test:
  stage: test
  script:
    - flutter pub get
    - flutter test --coverage
  artifacts:
    paths:
      - coverage/lcov.info
  allow_failure: true

flutter-metrics:
  stage: metrics
  script:
    - flutter pub get
    - dart pub global activate dart_code_metrics
    #- ~/.pub-cache/bin/dcm analyze lib
    - export PATH="$PATH":"$HOME/.pub-cache/bin"
    - dcm analyze lib
  allow_failure: true

flutter-build:
  stage: build
  script:
    - flutter pub get
    - flutter build apk --debug
  artifacts:
    paths:
      - build/app/outputs/flutter-apk/app-debug.apk
  allow_failure: true

security-check:
  stage: security
  script:
    - flutter pub get
    - dart pub outdated --mode=null-safety
  allow_failure: true

sonarcloud-check:
  stage: sonarcloud-check
  script:
    - sonar-scanner/bin/sonar-scanner
  allow_failure: true
