                        -H/Users/<USER>/Development/flutter-3.29/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=28
-<PERSON><PERSON>DROID_PLATFORM=android-28
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/StudioProjects/el-junto-flutter-app/build/app/intermediates/cxx/Debug/3f6r4v28/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/StudioProjects/el-junto-flutter-app/build/app/intermediates/cxx/Debug/3f6r4v28/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/StudioProjects/el-junto-flutter-app/android/app/.cxx/Debug/3f6r4v28/x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2