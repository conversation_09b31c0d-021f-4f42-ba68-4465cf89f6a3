{"buildFiles": ["/Users/<USER>/Development/flutter-3.29/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/el-junto-flutter-app/android/app/.cxx/Debug/3f6r4v28/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/el-junto-flutter-app/android/app/.cxx/Debug/3f6r4v28/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}