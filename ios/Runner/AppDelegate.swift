import UIKit
import Firebase
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> <PERSON>ol {
    FirebaseApp.configure()
    if #available(iOS 10.0, *) {
          UNUserNotificationCenter.current().delegate = self
    }
    application.registerForRemoteNotifications()
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}

// import UIKit
// import Firebase
// import Flutter

// @UIApplicationMain
// @objc class AppDelegate: FlutterAppDelegate {
//   override func application(
//     _ application: UIApplication,
//     didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
//   ) -> <PERSON><PERSON> {
//     FirebaseApp.configure()
    
//     if #available(iOS 10.0, *) {
//       UNUserNotificationCenter.current().delegate = self
//     }

//     application.registerForRemoteNotifications()

//     return super.application(application, didFinishLaunchingWithOptions: launchOptions)
//   }
// }
